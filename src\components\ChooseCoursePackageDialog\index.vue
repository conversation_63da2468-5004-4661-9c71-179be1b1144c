<!--
 * @Description: 课程套餐选择弹窗
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-06-28 11:08:57
 * @LastEditTime: 2023-11-02 08:51:10
-->

<template>
  <div class="app-container">
    <el-dialog
      v-model="dialogVisible"
      title="选择课程套餐"
      width="50%"
      top="110px"
      @close="close"
      :key="new Date().getTime()"
      center
    >
      <el-form
        :model="queryParams"
        ref="queryRef"
        :inline="true"
        label-width="68px"
      >
        <el-form-item label="套餐名" prop="name">
          <el-input
            v-model="queryParams.name"
            placeholder="请输入套餐名"
            clearable
            style="width: 240px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select
            v-model="queryParams.status"
            placeholder="状态"
            clearable
            style="width: 240px"
          >
            <el-option
              v-for="dict in sys_job_status"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="创建时间" style="width: 308px">
          <el-date-picker
            v-model="dateRange"
            value-format="YYYY-MM-DD"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="selectDate"
          ></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-table
        ref="chooseCoursePackageTableRef"
        :data="tableData"
        highlight-current-row
        @current-change="handleCurrentChange"
      >
        <el-table-column label="套餐编号" prop="id" />
        <el-table-column label="套餐名" prop="name" />
        <el-table-column label="状态" prop="status">
          <template #default="scope">
            <dict-tag :options="sys_job_status" :value="scope.row.status" />
          </template>
        </el-table-column>
        <el-table-column label="备注" prop="remark" />
        <el-table-column label="创建时间" prop="createTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        class="dialog-pagination"
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
        :autoScroll="false"
      />
      <template #footer>
        <el-button @click="close">取消</el-button>
        <el-button type="primary" @click="save">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="ChooseCoursePackageDialog">
  import { listPackage } from "@/api/system/package"

  const emit = defineEmits(["fetch-data"])
  const { proxy } = getCurrentInstance()
  const { sys_job_status } = proxy.useDict("sys_job_status")

  const dateRange = ref([])
  const tableData = ref([])
  const dialogVisible = ref(false)
  const total = ref(0)
  const propItem = ref()

  const data = reactive({
    queryParams: {
      pageNum: 1,
      pageSize: 10
    }
  })

  const { queryParams } = toRefs(data)

  //** 弹框打开事件 */
  const openDialog = async row => {
    propItem.value = row
    dialogVisible.value = true
    await getList()
  }

  /** 查询目录列表 */
  const getList = async () => {
    const res = await listPackage(queryParams.value)
    if (res.code === 200) {
      tableData.value = res.rows
      total.value = res.total
    }
  }

  /** 搜索按钮操作 */
  function handleQuery() {
    getList()
  }
  /** 重置按钮操作 */
  function resetQuery() {
    dateRange.value = []
    proxy.resetForm("queryRef")
    handleQuery()
  }
  // 关闭弹框并重置操作
  const close = () => {
    proxy.resetForm("queryRef")
    dialogVisible.value = false
  }
  const save = () => {
    emit("fetch-data", currentRow.value, propItem.value)
    close()
  }

  const currentRow = ref()
  const handleCurrentChange = val => {
    currentRow.value = val
  }
  const selectDate = () => {
    if (dateRange.value != null) {
      queryTimeFrom.value = dateRange.value[0]
      queryTimeTo.value = dateRange.value[1]
    } else {
      queryTimeFrom.value = ""
      queryTimeTo.value = ""
    }
  }

  defineExpose({
    openDialog
  })
</script>

<style lang="scss" scoped>
  :deep(.pagination-container .el-pagination) {
    position: static;
  }
</style>
