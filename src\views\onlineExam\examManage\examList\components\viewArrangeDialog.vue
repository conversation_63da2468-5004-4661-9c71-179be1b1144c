<!--
 * @Description: 考试安排弹窗
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-03-09 16:07:07
 * @LastEditTime: 2024-05-11 15:24:57
-->
<template>
  <div class="app-container">
    <el-dialog
      v-model="dialogVisible"
      title="考试安排"
      width="70%"
      top="110px"
      @close="close"
      :key="new Date().getTime()"
      center
    >
      <el-table :data="tableData" :border="false" v-loading="loading">
        <el-table-column label="安排类型" prop="arrangeType">
          <template #default="scope">
            <dict-tag
              :options="exam_arrange_type"
              :value="scope.row.arrangeType"
            />
          </template>
        </el-table-column>
        <el-table-column label="开始时间" prop="startTime" />
        <el-table-column label="结束时间" prop="endTime" />
        <el-table-column label="考试人员" prop="examUserNames" />
        <el-table-column label="创建时间" prop="createTime" />
        <el-table-column label="考试二维码">
          <template #default="scope">
            <el-popover placement="right" width="230" trigger="click">
              <div
                :id="'qrCode' + scope.$index"
                :ref="'qrCodeDiv' + scope.$index"
              ></div>
              <template #reference>
                <el-button link @click="showQRcode(scope.row, scope.$index)">
                  <el-icon size="20">
                    <i class="iconfont icon-erweima"></i>
                  </el-icon>
                </el-button>
              </template>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          width="220"
          class-name="small-padding fixed-width"
        >
          <template #default="scope">
            <el-button
              link
              type="primary"
              icon="Edit"
              @click="arrangeUpdate(scope.row)"
              >修改</el-button
            >
            <el-button
              link
              type="primary"
              icon="Delete"
              @click="arrangeDelete(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <pagination
        class="dialog-pagination"
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
        :autoScroll="false"
      />
      <template #footer>
        <el-button @click="close">关闭</el-button>
      </template>
    </el-dialog>
    <!-- 安排考试弹窗 -->
    <arrangeDialog ref="arrangeDialogRef" @fetch-data="getList" />
  </div>
</template>

<script setup name="viewArrangeDialog">
  import QRCode from "qrcodejs2-fix"
  import { listArrange, delArrange } from "@/api/onlineExam/exam.js"
  import arrangeDialog from "./arrangeDialog"
  import useTenantStore from "@/store/modules/tenant"

  const tenantStore = useTenantStore()
  const { domainName } = storeToRefs(tenantStore)
  const emit = defineEmits(["fetch-data"])
  const { proxy } = getCurrentInstance()
  const { exam_arrange_type } = proxy.useDict("exam_arrange_type")

  const tableData = ref([])
  const loading = ref(true)
  const dialogVisible = ref(false)
  const total = ref(0)
  const examInfo = ref({})

  const data = reactive({
    queryParams: {
      pageNum: 1,
      pageSize: 10
    }
  })

  const { queryParams } = toRefs(data)

  //** 弹框打开事件 */
  const openDialog = row => {
    dialogVisible.value = true
    examInfo.value = row
    queryParams.value.baseId = row.baseId
    getList()
  }

  /** 查询目录列表 */
  function getList() {
    loading.value = true
    listArrange(queryParams.value).then(res => {
      tableData.value = res.rows || []
      total.value = res.total
      loading.value = false
    })
  }
  /** 安排修改按钮操作 */
  function arrangeUpdate(row) {
    proxy.$refs["arrangeDialogRef"].openDialog("edit", examInfo.value, row)
  }
  /** 安排删除按钮操作 */
  function arrangeDelete(row) {
    proxy.$modal
      .confirm('是否确认删除id为"' + row.arrangeId + '"的数据项?')
      .then(function () {
        return delArrange(row.arrangeId)
      })
      .then(() => {
        getList()
        proxy.$modal.msgSuccess("删除成功")
      })
      .catch(() => {})
  }
  // 关闭弹框
  const close = () => {
    dialogVisible.value = false
  }

  // 生成二维码
  const showQRcode = (row, index) => {
    const qrCodeText =
      domainName.value === "lymy"
        ? `https://cmoc-edu.bkehs.com/#/pages/login`
        : "http://localhost:9090"
    if (!document.getElementById("qrCode" + index).innerHTML) {
      new QRCode(proxy.$refs["qrCodeDiv" + index], {
        text: `${qrCodeText}?arrangeId=${row.arrangeId}&baseId=${row.baseId}`,
        width: 200,
        height: 200,
        colorDark: "#333333", //二维码颜色
        colorLight: "#ffffff", //二维码背景色
        correctLevel: QRCode.CorrectLevel.L //容错率，L/M/H
      })
    }
  }

  defineExpose({
    openDialog
  })
</script>

<style lang="scss" scoped>
  :deep(.pagination-container .el-pagination) {
    position: static;
  }

  .al_icon {
    color: #77baff;
  }
</style>
