<!--
 * @Description: 创建考试弹窗
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-06-09 14:05:22
 * @LastEditTime: 2024-12-19 16:16:34
-->
<template>
  <div class="app-container">
    <el-dialog
      v-model="dialogVisible"
      title="创建考试"
      width="80%"
      top="110px"
      @close="close"
      :key="new Date().getTime()"
      center
    >
      <el-form
        :model="form"
        ref="formRef"
        label-width="100px"
        :rules="rules"
        :inline="true"
      >
        <!-- ========================= 基本信息 ===========================  -->
        <el-tabs v-model="activeName" class="demo-tabs">
          <el-tab-pane label="基本信息" name="1" />
        </el-tabs>
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="试卷名称" prop="baseN<PERSON>">
              <el-input v-model="form.baseName" />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="考试用卷" prop="paperName">
              <el-input v-model="form.paperName" disabled />
              <el-button
                type="primary"
                class="choosePaperBtn"
                @click="choosePaper"
                >选择试卷</el-button
              >
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="学分" prop="credit">
              <el-input-number
                v-model="form.credit"
                :min="1"
                :max="999"
                :precision="1"
                :step="0.5"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="积分" prop="integral">
              <el-input-number
                v-model="form.integral"
                :min="1"
                :max="999"
                :precision="1"
                :step="0.5"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="证书设置" prop="certificateName">
              <el-input v-model="form.certificateName" disabled />
              <div class="extendCerBtn">
                <el-button type="primary" @click="chooseCertificate">
                  设置证书
                </el-button>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否签名" prop="signFlag">
              <el-radio-group v-model="form.signFlag" class="ml-4">
                <el-radio label="0" size="large">否 </el-radio>
                <el-radio label="1" size="large">是 </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form
          :model="form.examConfig"
          ref="examConfigRef"
          label-width="100px"
          :rules="examConfigRules"
          :inline="true"
        >
          <el-row :gutter="10">
            <!-- ========================= 基本设置 ===========================  -->
            <el-col :span="24">
              <el-tabs v-model="activeName" class="demo-tabs">
                <el-tab-pane label="基本设置" name="1" />
              </el-tabs>
            </el-col>
            <el-col :span="12">
              <el-form-item label="考试时长" prop="examDurationLimit">
                <el-radio-group
                  v-model="examDuration"
                  class="ml-4"
                  @change="examDurationChange"
                >
                  <el-radio label="1" size="large">
                    限
                    <div class="margin-number-input">
                      <el-input-number
                        :disabled="examDuration === '2'"
                        v-model="form.examConfig.examDurationLimit"
                        :min="0"
                        :max="999"
                      />
                    </div>
                    分钟
                  </el-radio>
                  <el-radio label="2" size="large">不限时 </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="参加次数" prop="limitNumber">
                <el-radio-group
                  v-model="participationTimes"
                  class="ml-4"
                  @change="participationTimesChange"
                >
                  <el-radio label="1" size="large">
                    限
                    <div class="margin-number-input">
                      <el-input-number
                        :disabled="participationTimes === '2'"
                        v-model="form.examConfig.limitNumber"
                        :min="0"
                        :max="999"
                      />
                    </div>
                    次
                  </el-radio>
                  <el-radio label="2" size="large">无限次 </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="及格条件" prop="lowestScore">
                成绩不低于
                <div class="margin-number-input">
                  <el-input-number
                    v-model="form.examConfig.lowestScore"
                    :min="1"
                    :max="999"
                    :precision="1"
                    :step="0.5"
                  />
                </div>
                分
              </el-form-item>
            </el-col>
            <!-- ========================= 补考设置 ===========================  -->
            <el-col :span="24">
              <el-tabs v-model="activeName" class="demo-tabs">
                <el-tab-pane label="补考设置" name="1" />
              </el-tabs>
            </el-col>
            <el-col :span="12">
              <el-form-item label="补考类型" prop="makeUpType">
                <el-radio-group
                  v-model="form.examConfig.makeUpType"
                  class="ml-4"
                >
                  <el-radio label="Z" size="large"> 自动补考 </el-radio>
                  <el-radio label="S" size="large"> 手动补考 </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="补考次数" prop="makeUpNumber">
                <span class="text">
                  <div class="margin-number-input">
                    <el-input-number
                      v-model="form.examConfig.makeUpNumber"
                      :min="0"
                      :max="999"
                    />
                  </div>
                  次 &nbsp;&nbsp;&nbsp;允许学员参加补考的次数
                </span>
              </el-form-item>
            </el-col>
            <!-- ========================= 考场环境 ===========================  -->
            <el-col :span="24">
              <el-tabs v-model="activeName" class="demo-tabs">
                <el-tab-pane label="考场环境" name="1" />
              </el-tabs>
            </el-col>
            <el-col :span="12">
              <el-form-item label="交卷控制" prop="submintMinimumNumber">
                <span class="text">
                  答卷时间少于
                  <div class="margin-number-input">
                    <el-input-number
                      v-model="form.examConfig.submintMinimumNumber"
                      :min="1"
                      :max="999"
                    />
                  </div>
                  分钟禁止交卷
                </span>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="人脸抓拍" prop="faceCapture">
                <el-radio-group
                  v-model="form.examConfig.faceCapture"
                  class="ml-4"
                >
                  <el-radio label="1" size="large"> 是 </el-radio>
                  <el-radio label="0" size="large"> 否 </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="切屏次数" prop="switchingTimes">
                <span class="text">
                  <div class="margin-number-input">
                    <el-input-number
                      v-model="form.examConfig.switchingTimes"
                      :min="0"
                      :max="999"
                    />
                  </div>
                  次(未设置切屏次数时为无限制切屏，输入0表示不允许切屏。)
                </span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="是否记录学分" prop="recordCredit">
                <el-radio-group
                  v-model="form.examConfig.recordCredit"
                  class="ml-4"
                >
                  <el-radio label="1" size="large"> 是 </el-radio>
                  <el-radio label="0" size="large"> 否 </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>

            <!-- ========================= 评卷规则 ===========================  -->
            <el-col :span="24">
              <el-tabs v-model="activeName" class="demo-tabs">
                <el-tab-pane label="评卷规则" name="1" />
              </el-tabs>
            </el-col>
            <el-col :span="24">
              <el-form-item label="多选题" prop="ruleMcq">
                <el-radio-group
                  v-model="form.examConfig.ruleMcq"
                  class="ml-4"
                  @change="ruleMcqChange"
                >
                  <el-radio label="A" size="large"> 全部答对给分 </el-radio>
                  <el-radio label="B" size="large">
                    按正确选项个数给分(题目分数 ÷ 正确项个数 ×
                    选对项个数)，答错不给分
                  </el-radio>
                  <el-radio label="C" size="large">
                    全部答对给全分,不完全答对给
                    <div class="margin-number-input">
                      <el-input-number
                        :disabled="form.examConfig.ruleMcq !== 'C'"
                        v-model="form.examConfig.ruleMcqScore"
                        :min="1"
                        :max="999"
                        :precision="1"
                        :step="0.5"
                      />
                    </div>
                    分
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="填空题" prop="ruleCloze">
                <el-radio-group
                  v-model="form.examConfig.ruleCloze"
                  class="ml-4"
                >
                  <el-radio label="A" size="large"> 全部答对给分 </el-radio>
                  <el-radio label="B" size="large">
                    按答对项个数给分(题目分数 ÷ 填空项个数 × 答对项个数)
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="评卷人">
                <el-input value="系统判定" disabled />
              </el-form-item>
            </el-col>
            <!-- ========================= 授权提醒设置 ===========================  -->
            <el-col :span="24">
              <el-tabs v-model="activeName" class="demo-tabs">
                <el-tab-pane label="授权提醒设置" name="1" />
              </el-tabs>
            </el-col>
            <el-col :span="24">
              <el-form-item label="提醒日期" prop="remindDateNumber">
                <span class="text">
                  过期前
                  <div class="margin-number-input">
                    <el-input-number
                      v-model="form.examConfig.remindDateNumber"
                      :min="0"
                      :max="999"
                    />
                  </div>
                  天发送(为0或空时为不发送)
                </span>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-form>
      <template #footer>
        <el-button @click="close">取消</el-button>
        <el-button type="primary" @click="save">确定</el-button>
      </template>
    </el-dialog>

    <!-- 选择试卷弹窗 -->
    <choosePaperDialog
      ref="choosePaperDialogRef"
      @fetch-data="choosePaperDone"
    />
    <!-- 选择评卷人弹窗 -->
    <ChoosePersonnelDialog
      ref="chooseRuleUserDialogRef"
      @fetch-data="chooseRuleUserDone"
      title="选择评卷人"
    />
    <!-- 设置证书弹窗 -->
    <ChooseCertificateDialog
      ref="chooseCertificateDialogRef"
      @fetch-data="chooseCertificateDone"
    />
  </div>
</template>

<script setup name="CreateExamDialog">
  import { addExam } from "@/api/onlineExam/exam"
  import choosePaperDialog from "@/views/onlineExam/examManage/examAdd/components/choosePaperDialog.vue"

  const emit = defineEmits(["fetch-data"])
  const { proxy } = getCurrentInstance()
  const dialogVisible = ref(false)
  const activeName = ref("1")
  const examDuration = ref("1") // 考试时长
  const participationTimes = ref("1") // 参加次数

  const data = reactive({
    form: {
      examConfig: {}
    },
    rules: {
      baseName: [
        { required: true, message: "试卷名称不能为空", trigger: "blur" }
      ],
      paperName: [
        { required: true, message: "考试用卷不能为空", trigger: "change" }
      ],
      signFlag: [
        { required: true, message: "是否签名不能为空", trigger: "blur" }
      ]
    },
    examConfigRules: {
      examDurationLimit: [
        { required: true, message: "考试时长不能为空", trigger: "blur" }
      ],
      limitNumber: [
        { required: true, message: "参加次数不能为空", trigger: "blur" }
      ],
      lowestScore: [
        { required: true, message: "最低成绩不能为空", trigger: "blur" }
      ],
      makeUpType: [
        { required: true, message: "补考类型不能为空", trigger: "blur" }
      ],
      ruleMcq: [
        { required: true, message: "请设置多选题规则", trigger: "blur" }
      ],
      ruleCloze: [
        { required: true, message: "请设置填空题规则", trigger: "blur" }
      ],
      ruleUserName: [
        { required: true, message: "评卷人不能为空", trigger: "change" }
      ],
      viewTime: [
        { required: true, message: "请设置查阅时间", trigger: "blur" }
      ],
      lookupType: [
        { required: true, message: "请设置查阅权限", trigger: "blur" }
      ],
      faceCapture: [
        { required: true, message: "请设置人脸抓拍", trigger: "blur" }
      ],
      recordCredit: [
        { required: true, message: "请设置是否记录学分", trigger: "blur" }
      ]
    }
  })
  const { form, rules, examConfigRules } = toRefs(data)

  //** 弹框打开事件 */
  const openDialog = async () => {
    dialogVisible.value = true
  }

  // 关闭弹框并重置操作
  const close = () => {
    form.value = {
      examConfig: {}
    }
    examDuration.value = "1"
    participationTimes.value = "1"
    dialogVisible.value = false
  }

  const examDurationChange = value => {
    if (value === "2") {
      form.value.examConfig.examDurationLimit = 0
    } else {
      form.value.examConfig.examDurationLimit = undefined
    }
  }
  const participationTimesChange = value => {
    if (value === "2") {
      form.value.examConfig.limitNumber = 0
    } else {
      form.value.examConfig.limitNumber = undefined
    }
  }
  const ruleMcqChange = value => {
    if (value !== "C") {
      form.value.examConfig.ruleMcqScore = undefined
    }
  }

  // 选择试卷
  const choosePaper = () => {
    proxy.$refs["choosePaperDialogRef"].openDialog()
  }
  // 试卷选择完成
  const choosePaperDone = item => {
    form.value.paperName = item.paperName
    form.value.paperId = item.paperId
  }
  // 评卷人选择完成
  const chooseRuleUserDone = item => {
    if (!item || item.length === 0) return
    form.value.examConfig.ruleUserName = item.map(user => user.userName)
    form.value.examConfig.ruleUserId = item.map(user => user.userId)
    form.value.examConfig.ruleUserName =
      form.value.examConfig.ruleUserName.join()
    form.value.examConfig.ruleUserId = form.value.examConfig.ruleUserId.join()
  }

  const save = () => {
    proxy.$refs["formRef"].validate(valid => {
      proxy.$refs["examConfigRef"].validate(valid2 => {
        if (valid && valid2) {
          if (
            form.value.examConfig.ruleMcq === "C" &&
            !form.value.examConfig.ruleMcqScore
          ) {
            return proxy.$modal.msgWarning("请完整填写多选题不完全答对给分数量")
          }
          addExam(form.value).then(res => {
            if (res.code === 200) {
              proxy.$modal.msgSuccess("新增成功")
              let emitData = {
                baseId: res.data,
                baseName: form.value.baseName
              }
              emit("fetch-data", emitData)

              close()
            }
          })
        }
      })
    })
  }

  const chooseCertificateDialogRef = ref()
  // 设置证书
  const chooseCertificate = () => {
    chooseCertificateDialogRef.value.openDialog()
  }
  // 证书选择完成
  const chooseCertificateDone = item => {
    form.value.certificateId = item.certTemplateId
    form.value.certificateName = item.templateName
  }

  defineExpose({
    openDialog
  })
</script>

<style lang="scss" scoped>
  :deep(.el-radio) {
    height: auto;
  }
  :deep(.el-input-number .el-input__wrapper) {
    padding-left: 50px;
    padding-right: 50px;
  }
  :deep(.el-form-item__content) {
    display: block;
  }
  :deep(.el-input) {
    display: inline-block;
    width: auto;
  }

  .margin-number-input {
    display: inline-block;
    margin: 0 5px;
  }

  .text {
    font-size: 14px;
    font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB,
      Microsoft YaHei, Arial, sans-serif;
    font-weight: 500;
    color: #606266;
  }

  .buttonCol {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 50px;
  }

  .choosePaperBtn {
    margin-left: 20px;
  }

  .extendCerBtn {
    position: absolute;
    left: 220px;
    top: 0;
  }
</style>
