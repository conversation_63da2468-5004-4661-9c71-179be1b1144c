<!--
 * @Description: 新增/修改课程套餐
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-08-25 13:52:05
 * @LastEditTime: 2024-03-05 13:51:07
-->

<template>
  <el-dialog :title="title" v-model="visible" width="1000px" append-to-body>
    <el-form ref="packageRef" :model="form" :rules="rules" label-width="80px">
      <el-form-item label="套餐编号" prop="packageCode">
        <el-input v-model="form.packageCode" placeholder="请输入套餐编号" />
      </el-form-item>
      <el-form-item label="套餐名" prop="name">
        <el-input v-model="form.name" placeholder="请输入套餐名" />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="form.status">
          <el-radio
            v-for="dict in sys_job_status"
            :key="dict.value"
            :label="dict.value"
            >{{ dict.label }}</el-radio
          >
        </el-radio-group>
      </el-form-item>
      <el-form-item label="选择课程">
        <el-button
          type="primary"
          class="choose_course_btn"
          @click="chooseCourse"
        >
          选择课程
        </el-button>

        <el-table :data="form.coursePackageDetailList">
          <el-table-column type="index" />
          <el-table-column label="课程名称" prop="courseName" />
          <el-table-column label="课程编号" prop="courseCode" width="150" />
          <el-table-column label="课程价格" prop="coursePrice" width="150">
            <template #default="scope">
              {{
                scope.row.coursePrice === null ||
                scope.row.coursePrice === undefined
                  ? ""
                  : `${scope.row.coursePrice}元`
              }}
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            width="120"
            class-name="small-padding fixed-width"
          >
            <template #default="scope">
              <el-button
                style="color: red"
                link
                type="primary"
                @click="handleDelete(scope.$index)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </el-form-item>
      <el-form-item label="课程总价">
        <div class="total-price">
          {{ totalPrice }}
        </div>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 课程选择弹窗 -->
  <ChooseCourseDialog
    :disabledList="disabledList"
    selectionType="multiple"
    ref="chooseCourseDialogRef"
    @fetch-data="chooseCourseDone"
  />
</template>

<script setup name="addEditPackageDialog">
  import {
    getPackageById,
    addPackage,
    updatePackage
  } from "@/api/system/package"

  const emit = defineEmits(["fetch-data"])

  const { proxy } = getCurrentInstance()
  const { sys_job_status } = proxy.useDict("sys_job_status")

  const visible = ref(false)
  const form = ref({
    coursePackageDetailList: []
  })
  const title = ref("新增套餐")

  const totalPrice = computed(() => {
    if (
      !form.value.coursePackageDetailList ||
      form.value.coursePackageDetailList.length === 0
    )
      return "0元"
    const totalValue = form.value.coursePackageDetailList?.reduce(
      (pre, cur) => {
        if (!cur.coursePrice) cur.coursePrice = 0
        return pre + cur.coursePrice
      },
      0
    )
    return totalValue + "元"
  })

  // 打开弹窗事件
  const openDialog = async id => {
    // 重置表单数据
    form.value = {
      coursePackageDetailList: []
    }

    if (id) {
      title.value = "修改套餐"
      const { data } = await getPackageById(id)
      Object.assign(form.value, data)
      form.value.status = form.value.status.toString()
    }
    visible.value = true
  }

  const rules = ref({
    name: [{ required: true, message: "套餐名不能为空", trigger: "blur" }],
    status: [{ required: true, message: "状态不能为空", trigger: "blur" }],
    packageCode: [
      { required: true, message: "套餐编号不能为空", trigger: "blur" }
    ]
  })

  /** 提交按钮 */
  function submitForm() {
    proxy.$refs["packageRef"].validate(async valid => {
      if (valid) {
        if (form.value.id != undefined) {
          const { code } = await updatePackage(form.value)
          if (code === 200) proxy.$modal.msgSuccess("修改成功")
        } else {
          const { code } = await addPackage(form.value)
          if (code === 200) proxy.$modal.msgSuccess("新增成功")
        }
        visible.value = false
        emit("fetch-data")
      }
    })
  }

  const cancel = () => {
    visible.value = false
    reset()
  }

  /** 表单重置 */
  function reset() {
    form.value = {
      coursePackageDetailList: []
    }
    title.value = "新增套餐"
    proxy.resetForm("packageRef")
  }

  let chooseCourseDialogRef = ref()
  // 选择课程
  const chooseCourse = () => {
    // const ids = form.value.coursePackageDetailList
    //   ?.map(item => item.courseId)
    //   .join()
    chooseCourseDialogRef.value.openDialog()
  }

  const chooseCourseDone = async row => {
    form.value.coursePackageDetailList = [
      ...form.value.coursePackageDetailList,
      ...row
    ]
    const resultMap = new Map()
    for (const item of form.value.coursePackageDetailList) {
      const { courseId } = item
      if (!resultMap.has(courseId)) {
        resultMap.set(courseId, item)
      }
    }

    // 将Map对象转换回数组形式
    form.value.coursePackageDetailList = Array.from(resultMap.values())
  }
  // 禁止选择项
  const disabledList = computed(() =>
    form.value.coursePackageDetailList.map(item => item.courseId)
  )

  const handleDelete = index => {
    form.value.coursePackageDetailList.splice(index, 1)
  }

  defineExpose({
    openDialog
  })
</script>

<style lang="scss" scoped>
  .choose_course_btn {
    margin-bottom: 20px;
  }

  .total-num {
    display: block;
  }
  .total-price {
    font-size: 18px;
    font-weight: bold;
  }
</style>
