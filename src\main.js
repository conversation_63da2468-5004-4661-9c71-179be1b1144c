/*
 * @Description: main.js
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-02-16 14:11:33
 * @LastEditTime: 2024-11-28 09:12:46
 */
import { createApp } from "vue"
import Cookies from "js-cookie"
import ElementPlus, { ElTable, ElTableColumn, ElDialog } from "element-plus"
import locale from "element-plus/lib/locale/lang/zh-cn" // 中文语言

import "uno.css" // UnoCSS styles
import "@/assets/styles/index.scss" // global css
import "@/font/iconfont.css"

import App from "./App"
import store from "./store"
import router from "./router"
import directive from "./directive" // directive
import elTableInfiniteScroll from "el-table-infinite-scroll"
import mitt from "mitt"

// 注册指令
import plugins from "./plugins" // plugins
import { setupCpn } from "@/components"
// svg图标
import "virtual:svg-icons-register"
import elementIcons from "@/components/SvgIcon/svgic<PERSON>"
import "./permission" // permission control
import "@/utils/console.js"
import { download } from "@/utils/request"
import { useDict } from "@/utils/dict"
import {
  parseTime,
  resetForm,
  addDateRange,
  handleTree,
  selectDictLabel,
  selectDictLabels,
  selectDictValue
} from "@/utils/common"

const app = createApp(App)

// 全局方法挂载
app.config.globalProperties.useDict = useDict
app.config.globalProperties.download = download
app.config.globalProperties.parseTime = parseTime
app.config.globalProperties.resetForm = resetForm
app.config.globalProperties.handleTree = handleTree
app.config.globalProperties.addDateRange = addDateRange
app.config.globalProperties.selectDictLabel = selectDictLabel
app.config.globalProperties.selectDictLabels = selectDictLabels
app.config.globalProperties.selectDictValue = selectDictValue
app.config.globalProperties.$mitt = mitt()

app.use(router)
app.use(store)
app.use(plugins)
app.use(elementIcons)
// el-table滚动加载
app.use(elTableInfiniteScroll)
// 全局组件挂载
setupCpn(app)

directive(app)

// 全局修改element-plus的样式
// 1.获取props
const TableProps = ElTable.props
const TableColumnProps = ElTableColumn.props
const DialogProps = ElDialog.props
// 2.修改默认props
// 2.1.全局el-table设置
TableProps.border = { type: Boolean, default: true } // 边框线
// 2.2.全局el-table-column设置
TableColumnProps.align = { type: String, default: "center" } // 居中
TableColumnProps.showOverflowTooltip = { type: Boolean, default: true } // 文本溢出
// 2.3.全局el-dialog设置
DialogProps.closeOnClickModal.default = false
DialogProps.center.default = true

// 去除谷歌浏览器的scroll、wheel等事件警告
;(function () {
  if (typeof EventTarget !== "undefined") {
    let func = EventTarget.prototype.addEventListener
    EventTarget.prototype.addEventListener = function (type, fn, capture) {
      this.func = func
      if (typeof capture !== "boolean") {
        capture = capture || {}
        capture.passive = false
      }
      this.func(type, fn, capture)
    }
  }
})()

// 使用element-plus 并且设置全局的大小
app.use(ElementPlus, {
  locale: locale,
  // 支持 large、default、small
  size: Cookies.get("size") || "default"
})

app.mount("#app")
