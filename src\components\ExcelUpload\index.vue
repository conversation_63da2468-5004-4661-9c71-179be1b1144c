<template>
  <el-dialog
    :title="`${title}导入`"
    v-model="dialogVisible"
    width="400px"
    append-to-body
  >
    <el-upload
      ref="uploadRef"
      :limit="1"
      accept=".xlsx, .xls"
      :headers="headers"
      :action="baseUrl + url + '?updateSupport=' + isUpdate"
      :disabled="disabled"
      :on-progress="handleFileUploadProgress"
      :on-success="handleFileSuccess"
      :auto-upload="false"
      drag
    >
      <el-icon class="el-icon--upload"><upload-filled /></el-icon>
      <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
      <template #tip>
        <div class="el-upload__tip text-center">
          <div class="el-upload__tip">
            <el-checkbox v-model="isUpdate" />是否更新已经存在的{{ title }}数据
          </div>
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link
            type="primary"
            :underline="false"
            style="font-size: 12px; vertical-align: baseline"
            @click="importTemplate"
            >下载模板</el-link
          >
        </div>
      </template>
    </el-upload>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="dialogVisible = false">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
  import { getToken } from "@/utils/auth"

  const props = defineProps({
    // 弹出层标题
    title: {
      type: String,
      default: ""
    },
    // 是否更新已经存在的数据
    updateSupport: {
      type: Number,
      default: 0
    },
    // 上传的地址
    url: {
      type: String,
      default: ""
    },
    // 模板下载地址
    templateUrl: {
      type: String,
      default: ""
    },
    // 模板名称前缀
    templateName: {
      type: String,
      default: ""
    }
  })

  const { proxy } = getCurrentInstance()
  const emit = defineEmits()

  const testTenant = localStorage.getItem("testTenant")
  const dialogVisible = ref(false)
  const disabled = ref(false)
  const baseUrl = import.meta.env.VITE_APP_BASE_API
  const headers = ref({
    Authorization: "Bearer " + getToken(),
    "Test-Tenant": testTenant
  })
  const isUpdate = ref(0)

  //** 弹框打开事件 */
  const openDialog = row => {
    dialogVisible.value = true
    isUpdate.value = props.updateSupport
  }

  //** 下载模板操作 */
  function importTemplate() {
    proxy.download(
      props.templateUrl,
      {},
      `${props.templateName}_template_${new Date().getTime()}.xlsx`
    )
  }
  /** 文件上传中处理 */
  const handleFileUploadProgress = (event, file, fileList) => {
    disabled.value = true
  }
  /** 文件上传成功处理 */
  const handleFileSuccess = (response, file, fileList) => {
    dialogVisible.value = false
    disabled.value = false
    proxy.$refs["uploadRef"].handleRemove(file)
    proxy.$alert(
      "<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" +
        response.msg +
        "</div>",
      "导入结果",
      { dangerouslyUseHTMLString: true }
    )
    emit("uploadSuccess")
  }
  /** 提交上传文件 */
  function submitFileForm() {
    proxy.$refs["uploadRef"].submit()
  }

  defineExpose({
    openDialog
  })
</script>
