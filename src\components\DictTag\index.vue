<!--
 * @Description: dictTag组件
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-02-16 14:11:33
 * @LastEditTime: 2024-08-02 10:47:01
-->
<template>
  <div>
    <template v-for="(item, index) in options">
      <template v-if="valueArr.includes(item.value)">
        <el-tag
          :disable-transitions="true"
          :key="item.value + ''"
          :index="index"
          :type="item.elTagType === 'primary' || item.elTagType === 'default' ? '' : item.elTagType"
          :class="item.elTagClass"
        >
          {{ item.label }}
        </el-tag>
      </template>
    </template>
  </div>
</template>

<script setup>
  const props = defineProps({
    // 数据
    options: {
      type: Array,
      default: null
    },
    // 当前的值
    value: [Number, String, Array]
  })

  const valueArr = computed(() => {
    if (props.value !== null && typeof props.value !== "undefined") {
      return Array.isArray(props.value) ? props.value : String(props.value)?.split(",")
    } else {
      return []
    }
  })
</script>

<style scoped>
  .el-tag + .el-tag {
    margin-left: 10px;
  }
</style>
