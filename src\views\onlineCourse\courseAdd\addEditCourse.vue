<!--
 * @Description: 新增/修改课程
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-04-06 15:54:25
 * @LastEditTime: 2024-12-11 14:41:41
-->
<template>
  <div class="app-container">
    <el-form :model="formData" ref="formRef" label-width="100px" :rules="rules">
      <el-form-item label="课程名称" prop="courseName">
        <el-input v-model="formData.courseName" :disabled="isDisabled" />
      </el-form-item>
      <el-form-item label="课程目录" prop="catalogueId">
        <el-tree-select
          ref="catalogueTreeRef"
          v-model="formData.catalogueId"
          :data="catalogueOptions"
          :props="{
            value: 'catalogueId',
            label: 'catalogueName',
            children: 'children',
            disabled: 'disabled'
          }"
          node-key="catalogueId"
          show-checkbox
          multiple
          value-key="catalogueId"
          placeholder="选择课程目录"
          check-strictly
          default-expand-all
          :disabled="isDisabled"
        />
      </el-form-item>
      <el-form-item label="课程编号" prop="courseCode">
        <el-input v-model="formData.courseCode" :disabled="isDisabled" />
      </el-form-item>
      <el-form-item label="课程类型" prop="courseType">
        <el-select
          v-model="formData.courseType"
          clearable
          style="width: 200px"
          :disabled="isDisabled"
          multiple
          @change="courseTypeChange"
        >
          <el-option
            v-for="dict in roles.includes('admin') || roles.includes('admin1')
              ? pedu_course_type
              : course_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="课程封面" prop="courseImage">
        <ImageUpload
          v-model="formData.courseImage"
          :limit="1"
          :disabled="isDisabled"
        />
      </el-form-item>
      <el-form-item label="课程等级" prop="courseLevel">
        <el-select
          v-model="formData.courseLevel"
          clearable
          style="width: 200px"
          :disabled="isDisabled"
        >
          <el-option
            v-for="dict in course_level_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="课程标签" prop="courseLabel">
        <el-select
          v-model="formData.courseLabel"
          clearable
          :disabled="isDisabled"
          style="width: 200px"
          multiple
        >
          <el-option
            v-for="dict in course_label_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="课程价格" prop="coursePrice">
        <el-input
          maxlength="8"
          @input="v => (formData.coursePrice = v.replace(/[^\d.]/g, ''))"
          v-model="formData.coursePrice"
          :disabled="isDisabled || formData.courseLabel?.includes('0')"
        >
          <template #append>元</template>
        </el-input>
      </el-form-item>
      <el-form-item label="讲师" prop="lecturer">
        <el-input v-model="formData.lecturer" :disabled="isDisabled" />
      </el-form-item>
      <el-form-item label="上架时间" prop="onShelfTime">
        <el-date-picker
          v-model="formData.onShelfTime"
          type="date"
          :disabled="isDisabled"
          placeholder="上架时间"
          value-format="YYYY-MM-DD"
          :disabled-date="disabledDate"
        />
      </el-form-item>
      <el-form-item label="下架时间" prop="offShelfTime">
        <el-date-picker
          v-model="formData.offShelfTime"
          type="date"
          :disabled="isDisabled"
          placeholder="下架时间"
          value-format="YYYY-MM-DD"
          :disabled-date="disabledDate"
        />
      </el-form-item>
      <el-form-item label="课程介绍" prop="courseIntroduction">
        <el-input
          v-model="formData.courseIntroduction"
          :rows="2"
          maxlength="100"
          type="textarea"
          :disabled="isDisabled"
        />
      </el-form-item>
      <!-- <el-form-item label="学分" prop="credits">
        <el-input-number
          v-model="formData.credits"
          :step="0.1"
          :precision="1"
          :disabled="isDisabled"
          :min="0"
          :max="999"
        />
      </el-form-item>
      <el-form-item label="学时" prop="creditHours">
        <el-input-number
          v-model="formData.creditHours"
          :step="0.1"
          :precision="1"
          :disabled="isDisabled"
          :min="0"
          :max="999"
        />
      </el-form-item>
      <el-form-item label="证书设置" prop="certificateName">
        <el-input v-model="formData.certificateName" disabled />
        <div class="extendCerBtn">
          <el-button
            type="primary"
            @click="chooseCertificate"
            v-if="!isDisabled"
          >
            设置证书
          </el-button>
        </div>
      </el-form-item>

      <el-form-item label="是否开启人脸" prop="needSnapshot">
        <el-radio-group v-model="formData.needSnapshot" :disabled="isDisabled">
          <el-radio-button label="0">不启用</el-radio-button>
          <el-radio-button label="1">启用</el-radio-button>
        </el-radio-group>
      </el-form-item> -->
      <el-form-item label="倍速播放" prop="multiplyPlayback">
        <el-radio-group
          v-model="formData.multiplyPlayback"
          :disabled="isDisabled"
        >
          <el-radio-button label="1">始终允许</el-radio-button>
          <el-radio-button label="0">不允许</el-radio-button>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="进度条拖动" prop="progressBarDrag">
        <el-radio-group
          v-model="formData.progressBarDrag"
          :disabled="isDisabled"
        >
          <el-radio-button label="1">始终允许</el-radio-button>
          <el-radio-button label="0">不允许</el-radio-button>
        </el-radio-group>
      </el-form-item>
      <!-- <el-form-item
        label="必修人员"
        prop="requiredUserName"
        label-width="106px"
      >
        <el-input
          v-model="formData.requiredUserName"
          :rows="6"
          type="textarea"
          disabled
        />
        <div class="extendButton">
          <el-button
            v-if="flag !== 'detail'"
            type="primary"
            @click="choosePersonnel('required')"
            >设置必修人员</el-button
          >
        </div>
      </el-form-item>
      <el-form-item
        label="选修人员"
        prop="electiveUserName"
        label-width="106px"
      >
        <el-input
          v-model="formData.electiveUserName"
          :rows="6"
          type="textarea"
          disabled
        />
        <div class="extendButton">
          <el-button
            v-if="flag !== 'detail'"
            type="primary"
            @click="choosePersonnel('elective')"
          >
            设置选修人员
          </el-button>
        </div>
      </el-form-item> -->
      <el-form-item label="关联的考试" prop="associatedExamsName">
        <el-input v-model="formData.associatedExamsName" disabled />
        <el-button
          type="primary"
          v-if="flag !== 'detail' && !isDisabled"
          class="createExamBtn"
          @click="createExam"
        >
          创建考试
        </el-button>
      </el-form-item>
      <el-form-item label="关联试题" prop="ids">
        <el-input v-model="formData.ids" disabled />
        <el-button
          v-if="flag !== 'detail'"
          type="primary"
          class="createExamBtn"
          @click="questionItemAdd"
          >选择试题</el-button
        >
      </el-form-item>
      <el-form-item label="用户群体" prop="userGroups">
        <el-select
          v-model="formData.userGroups"
          clearable
          style="width: 200px"
          :disabled="isDisabled"
          multiple
        >
          <el-option
            v-for="dict in course_user_groups"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="场所" prop="location">
        <el-select
          v-model="formData.location"
          clearable
          style="width: 200px"
          :disabled="isDisabled"
          multiple
        >
          <el-option
            v-for="dict in course_location"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="formData.remark"
          :rows="2"
          maxlength="200"
          type="textarea"
          :disabled="isDisabled"
        />
      </el-form-item>
      <!-- 课程章节视频相关 -->
      <el-form-item
        v-if="flag !== 'detail'"
        class="chapter"
        label="添加课程章节"
      >
        <el-button
          v-if="!isDisabled"
          type="primary"
          plain
          icon="Plus"
          @click="addVideo"
        >
          新增
        </el-button>
        <div class="item_error" v-show="isShowChapterRequiredTip"
          >必填项不能为空</div
        >
      </el-form-item>
      <el-row>
        <el-col :span="16">
          <el-table
            ref="tableRef"
            class="tableContent"
            :data="formData.coursePassageList"
            style="margin-bottom: 20px"
            row-key="coursePassageName"
          >
            <el-table-column type="expand">
              <template #default="scope">
                <el-table :border="false" :data="scope.row.videoFileList">
                  <el-table-column type="index"></el-table-column>
                  <el-table-column
                    prop="fileName"
                    :label="isWordCourse ? '文档名称' : '视频名称'"
                  >
                    <template #default="scope2">
                      <el-link
                        :underline="false"
                        @click="handlePreview(scope2.row)"
                      >
                        {{
                          scope2.row.fileName
                            ? scope2.row.fileName
                            : getFileName(scope2.row.fileUrl)
                        }}
                      </el-link>
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="passageDuration"
                    :label="isWordCourse ? '学习时长' : '视频时长'"
                  >
                    <template #default="scope2">
                      {{ formatSecondStr(scope2.row.passageDuration) }}
                    </template>
                  </el-table-column>
                  <el-table-column
                    label="操作"
                    width="120"
                    v-if="flag !== 'detail' && !isDisabled"
                  >
                    <template #default="scope2">
                      <el-button
                        link
                        type="primary"
                        size="small"
                        @click="setQA(scope2.row, scope.$index, scope2.$index)"
                        >设置问答</el-button
                      >
                    </template>
                  </el-table-column>
                </el-table>
              </template>
            </el-table-column>
            <el-table-column prop="coursePassageName" label="章节名称" />
            <el-table-column
              label="操作"
              width="120"
              v-if="flag !== 'detail' && !isDisabled"
            >
              <template #default="scope">
                <el-button
                  link
                  type="primary"
                  size="small"
                  @click="changeChapter(scope.row, scope.$index)"
                  >修改</el-button
                >
                <el-button
                  link
                  style="color: red"
                  type="primary"
                  size="small"
                  @click="delChapter(scope.$index)"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </el-col>
      </el-row>
      <!-- 审核相关 -->
      <el-row v-if="flag === 'detail' || flag === 'edit'">
        <el-col :span="16">
          <el-tabs v-model="activeName" class="demo-tabs">
            <el-tab-pane label="审批记录" name="1" />
          </el-tabs>
          <el-table style="margin-bottom: 20px" :data="reviewList">
            <el-table-column type="index" width="50" />
            <el-table-column prop="approvalType" label="审批类型" width="100" />
            <el-table-column prop="optType" label="审核结果" width="100" />
            <el-table-column prop="remark" label="审核意见" />
            <el-table-column prop="createBy" label="创建人" width="120" />
            <el-table-column prop="createTime" label="创建时间" width="180">
              <template #default="socpe">
                {{ dayjs(socpe.row.createTime).format("YYYY-MM-DD HH:mm:ss") }}
              </template>
            </el-table-column>
          </el-table>
        </el-col>
      </el-row>

      <el-form-item>
        <el-button v-if="flag !== 'detail'" type="primary" @click="submitForm">
          提交
        </el-button>
        <el-button @click="handleBack">返回</el-button>
        <el-button @click="reset" v-if="flag === 'add'">重置</el-button>
      </el-form-item>
    </el-form>
    <!-- 创建考试弹窗 -->
    <CreateExamDialog ref="createExamDialogRef" @fetch-data="createExamDone" />
    <!-- 必修人员/部门选择弹窗 -->
    <ChoosePersonnelOrDeptDialog
      ref="choosePersonnelOrDeptDialogRef"
      @fetch-data="choosePersonnelDone"
      :selectedPersonnelList="selectedPersonnelList"
    />
    <!-- 选择章节弹窗 -->
    <chooseVideoDialog
      ref="chooseVideoDialogRef"
      :courseId="route.query.courseId"
      @refreshDataList="passageChangeMethod"
    />
    <!-- 选择章节弹窗 -->
    <chooseWordDialog
      ref="chooseWordDialogRef"
      :courseId="route.query.courseId"
      @refreshDataList="passageChangeMethod"
    />
    <!-- 设置证书弹窗 -->
    <ChooseCertificateDialog
      ref="chooseCertificateDialogRef"
      @fetch-data="chooseCertificateDone"
    />
    <!-- 选择试题弹窗 -->
    <chooseExamQuestionDialog
      ref="chooseQuestionDialogRef"
      @fetch-data="chooseQuestionDone"
    />
    <!-- 视频预览弹窗 -->
    <el-dialog
      v-model="dialogVideoVisible"
      title="视频预览"
      width="960"
      @close="videoPlayDialogClose"
    >
      <video
        ref="videoRef"
        controlslist="nodownload"
        width="900"
        height="600"
        controls
        :src="videoItem.fileUrl"
      ></video>
    </el-dialog>
    <!-- 设置问答弹窗 -->
    <setQADialog
      ref="setQADialogRef"
      @refreshDataList="setQADone"
      :isWordCourse="isWordCourse"
    />
    <!-- 文件预览弹窗 -->
    <PreviewDialog ref="previewDialogRef" />
  </div>
</template>

<script setup name="addEditCourse">
  import { COURSE_TYPE } from "@/utils/constant.js"
  import { cloneDeep } from "lodash-es"
  import dayjs from "dayjs"
  import Sortable from "sortablejs"
  import { getFileName, formatSecondStr } from "@/utils/common.js"
  import {
    addCourse,
    getCourse,
    updateCourse,
    getCourseReviewRecords
  } from "@/api/onlineCourse/course.js"
  import { catalogueList } from "@/api/system/catalogue.js"
  import { listPassage } from "@/api/onlineCourse/passage.js"
  import catalogue from "@/utils/catalogue.js"
  import chooseVideoDialog from "./components/chooseVideoDialog"
  import chooseExamQuestionDialog from "./components/chooseExamQuestionDialog"
  import setQADialog from "./components/setQADialog"
  import useUserStore from "@/store/modules/user"
  import chooseWordDialog from "./components/chooseWordDialog"

  const userStore = useUserStore()
  const { name, roles } = storeToRefs(userStore)
  const emit = defineEmits(["updateCurrentView"])
  const route = useRoute()
  const { proxy } = getCurrentInstance()

  const {
    course_user_groups,
    course_location,
    course_type,
    pedu_course_type,
    video_type,
    course_label_type,
    course_level_type
  } = proxy.useDict(
    "course_user_groups",
    "course_location",
    "course_type",
    "pedu_course_type",
    "video_type",
    "course_label_type",
    "course_level_type"
  )

  const catalogueOptions = ref([])
  const flag = ref("add")
  const chooseVideoDialogRef = ref()
  const chooseWordDialogRef = ref()
  // 是否展示章节必传提示文字
  const isShowChapterRequiredTip = ref(false)
  const dialogVideoVisible = ref(false)
  const videoItem = ref()

  const formData = ref({
    domainName: "test",
    // needSnapshot: "0",
    multiplyPlayback: "1",
    progressBarDrag: "1",
    coursePassageList: []
  })
  const rules = ref({
    courseName: [
      { required: true, message: "必填项不能为空", trigger: "blur" }
    ],
    catalogueId: [
      { required: true, message: "必填项不能为空", trigger: "blur" }
    ],
    courseCode: [
      { required: true, message: "必填项不能为空", trigger: "blur" }
    ],
    courseType: [
      { required: true, message: "必填项不能为空", trigger: "blur" }
    ],
    courseLevel: [
      { required: true, message: "必填项不能为空", trigger: "blur" }
    ],
    courseLabel: [
      { required: true, message: "必填项不能为空", trigger: "blur" }
    ],
    courseIntroduction: [
      { required: true, message: "必填项不能为空", trigger: "blur" }
    ],
    onShelfTime: [
      { required: true, message: "必填项不能为空", trigger: "blur" }
    ],
    offShelfTime: [
      { required: true, message: "必填项不能为空", trigger: "blur" }
    ]
  })
  const tableRef = ref()
  // 创建拖拽实例
  const initSort = className => {
    const table = document.querySelector(
      `.${className} .el-table__body-wrapper tbody`
    )
    Sortable.create(table, {
      group: "shared",
      animation: 150,
      easing: "cubic-bezier(1, 0, 0, 1)",
      draggable: ".el-table__row",
      onStart: ({ oldDraggableIndex }) => {
        tableRef.value.toggleRowExpansion(
          formData.value.coursePassageList[oldDraggableIndex],
          false
        )
      },
      // 结束拖动事件
      onEnd: ({ newDraggableIndex, oldDraggableIndex }) => {
        setNodeSort(
          formData.value.coursePassageList,
          oldDraggableIndex,
          newDraggableIndex
        )
        formData.value.approveFlag = true
      }
    })
  }
  // 拖拽完成修改数据排序
  const setNodeSort = (data, oldIndex, newIndex) => {
    const currRow = data.splice(oldIndex, 1)[0]
    data.splice(newIndex, 0, currRow)
  }

  // 监听标签，如果有免费则将价格重新赋值为0，以解决因为先输入价格在选择免费后不重新赋值为0的逻辑
  watch(
    () => formData.value.courseLabel,
    value => {
      if (!value) return
      if (value.includes("0")) formData.value.coursePrice = 0
    }
  )

  /** 查询菜单下拉树结构 */
  const getTreeselect = async () => {
    catalogueOptions.value = []
    const res = await catalogueList({
      catalogueType: catalogue.COURSE_CATALOGUE
    })
    const catalogueTree = {
      catalogueId: 0,
      catalogueName: "主类目",
      children: [],
      disabled: true
    }
    catalogueTree.children = proxy.handleTree(res.rows, "catalogueId")
    catalogueOptions.value.push(catalogueTree)
  }

  // 数据提交
  const submitForm = () => {
    proxy.$refs["formRef"].validate(valid => {
      if (!valid) {
        window.scrollTo({ top: 0, behavior: "smooth" })
        return
      }
      // 章节视频非空校验
      if (formData.value.coursePassageList?.length === 0) {
        isShowChapterRequiredTip.value = true
        return proxy.$modal.msgWarning("请添加课程章节")
      }
      // 下架时间必须晚于上架时间
      if (
        new Date(formData.value.offShelfTime).getTime() <=
        new Date(formData.value.onShelfTime).getTime()
      ) {
        return proxy.$modal.msgWarning("下架时间必须晚于上架时间")
      }
      const submitData = cloneDeep(formData.value)
      const checkedNodes = proxy.$refs[`catalogueTreeRef`].getCheckedNodes()
      submitData.catalogueName =
        checkedNodes.map(item => item.catalogueName) || []

      // 处理数组数据-转换成逗号分隔字符串
      const processKeyArr = [
        "courseLabel",
        "courseType",
        // "requiredUserName",
        // "requiredUsers",
        // "electiveUserName",
        // "electiveUsers",
        "catalogueId",
        "catalogueName",
        "userGroups",
        "location"
      ]
      processKeyArr.forEach(key => {
        submitData[key] = Array.isArray(submitData[key])
          ? submitData[key].join()
          : submitData[key]
      })
      // 区分新增/修改 实际调用接口
      if (flag.value === "add") {
        delete submitData.domainName
        addCourse(submitData).then(res => {
          if (res.code === 200) {
            reset()
            proxy.$modal.msgSuccess("新增成功")
            proxy.$tab.closeOpenPage({
              path: "/onlineCourse/courseList"
            })
          }
        })
      } else {
        updateCourse(submitData).then(res => {
          if (res.code === 200) {
            reset()
            proxy.$modal.msgSuccess("修改成功")
            proxy.$tab.closeOpenPage({
              path: "/onlineCourse/courseList",
              query: {
                queryParams: route.query.queryParams
                  ? JSON.parse(JSON.stringify(route.query.queryParams))
                  : undefined
              }
            })
          }
        })
      }
    })
  }

  /** 表单重置 */
  function reset() {
    if (flag.value === "add") {
      formData.value = {
        domainName: "test",
        // needSnapshot: "0",
        multiplyPlayback: "1",
        progressBarDrag: "1",
        coursePassageList: []
      }
    } else {
      formData.value = {}
    }
  }

  // 创建考试
  let createExamDialogRef = ref()
  const createExam = () => {
    proxy.$refs["createExamDialogRef"].openDialog()
  }
  // 考试选择完成
  const createExamDone = item => {
    formData.value.associatedExams = item.baseId
    formData.value.associatedExamsName = item.baseName
  }

  const selectedPersonnelList = ref([])
  // 选择必修/选修人员
  const choosePersonnel = field => {
    if (field === "required") {
      selectedPersonnelList.value = formData.value.electiveUsers
    } else {
      selectedPersonnelList.value = formData.value.requiredUsers
    }
    proxy.$refs["choosePersonnelOrDeptDialogRef"].openDialog(
      formData.value[`${field}Users`],
      formData.value[`${field}UserName`],
      null,
      field
    )
  }
  // 必修/选修人员选择完成
  const choosePersonnelDone = (item, field) => {
    if (!item || item.length === 0) return
    if (flag.value === "edit") formData.value.userLinkFlag = true // 标识用户关联关系有被修改
    formData.value[`${field}Users`] = item.map(user => user.userId)
    formData.value[`${field}UserName`] = item.map(user => user.userName)
  }

  // 选择章节
  const addVideo = () => {
    if (!formData.value.courseType?.length)
      return proxy.$message.warning("请先选择课程类型")

    if (formData.value.courseType.includes(COURSE_TYPE.WORD_COURSE)) {
      chooseWordDialogRef.value.openDialog()
    } else {
      chooseVideoDialogRef.value.openDialog()
    }
  }

  // 获取章节列表
  const fetchPassageData = async () => {
    let queryData = {
      courseId: route.query.courseId,
      pageSize: 999
    }
    const { rows } = await listPassage(queryData)
    formData.value.coursePassageList = rows
  }

  // 修改章节
  const changeChapter = (row, index) => {
    if (formData.value.courseType.includes(COURSE_TYPE.WORD_COURSE)) {
      proxy.$refs["chooseWordDialogRef"].openDialog(row, index.toString())
    } else {
      proxy.$refs["chooseVideoDialogRef"].openDialog(row, index.toString())
    }
  }

  // 删除章节
  const delChapter = index => {
    proxy.$modal
      .confirm("是否确认删除？")
      .then(function () {
        formData.value.coursePassageList.splice(index, 1)
      })
      .then(() => {
        proxy.$modal.msgSuccess("删除成功")
        formData.value.approveFlag = true
      })
      .catch(() => {})
  }

  const chooseCertificateDialogRef = ref()
  // 设置证书
  const chooseCertificate = () => {
    chooseCertificateDialogRef.value.openDialog()
  }
  // 证书选择完成
  const chooseCertificateDone = item => {
    formData.value.certificateId = item.certTemplateId
    formData.value.certificateName = item.templateName
  }

  // 选择题目
  const questionItemAdd = () => {
    proxy.$refs["chooseQuestionDialogRef"].openDialog(formData.value.ids)
  }

  // 选择题目完成
  const chooseQuestionDone = (ids, questionList) => {
    if (flag.value === "edit") formData.value.questionLinkFlag = true // 标识试题关联关系有被修改
    formData.value.ids = ids
    formData.value.courseQuestionLinkList = questionList
  }

  // 是否禁用修改
  const isDisabled = computed(() => {
    if (flag.value === "detail") return true
    if (
      formData.value.domainName ||
      name.value === formData.value.createBy ||
      name.value === "admin"
    ) {
      return false
    } else {
      return true
    }
  })

  const previewDialogRef = ref()
  const handlePreview = item => {
    // 如果是文档课程
    if (isWordCourse.value) {
      previewDialogRef.value.fileLoad(item.fileUrl)
    } else {
      videoItem.value = item
      dialogVideoVisible.value = true
    }
  }

  const passageChangeMethod = (passageItem, index) => {
    if (index) {
      formData.value.coursePassageList[Number(index)] = passageItem
    } else {
      formData.value.coursePassageList.push(passageItem)
    }
    expandRowsWithInnerData()
    formData.value.approveFlag = true
  }

  const handleBack = () => {
    proxy.$tab.closeOpenPage({
      path: "/onlineCourse/courseList",
      query: {
        queryParams: route.query.queryParams
          ? JSON.parse(JSON.stringify(route.query.queryParams))
          : undefined
      }
    })
  }
  // 只能选择今天及之后的日期
  const disabledDate = time => {
    return time.getTime() < Date.now() - 8.64e7
  }

  const activeName = ref("1")
  const reviewList = ref([])
  // 获取审批记录列表
  const fetchReviewData = async () => {
    const { data } = await getCourseReviewRecords(route.query.courseId)
    reviewList.value = data
  }

  const videoRef = ref(null)
  const videoPlayDialogClose = () => {
    videoRef.value && videoRef.value.pause()
  }

  // 设置问答
  const setQADialogRef = ref()
  const setQA = (row, passageIndex, videoIndex) => {
    setQADialogRef.value.openDialog(row, passageIndex, videoIndex)
  }

  const setQADone = (list, passageIndex, videoIndex) => {
    formData.value.approveFlag = true
    formData.value.coursePassageList[passageIndex].videoFileList[
      videoIndex
    ].courseQuestionLinkList = list
  }

  // 手动展开有视频的章节行
  const expandRowsWithInnerData = () => {
    formData.value.coursePassageList.forEach(row => {
      if (row.videoFileList && row.videoFileList.length > 0) {
        tableRef.value.toggleRowExpansion(row, true)
      }
    })
  }

  // 添加计算属性判断是否为文档课程
  const isWordCourse = computed(() => {
    return formData.value.courseType?.includes(COURSE_TYPE.WORD_COURSE)
  })

  onMounted(async () => {
    await getTreeselect()
    // 初始化时需要特殊处理的key数组
    const getProcessKeyArr = [
      "courseLabel",
      "courseType",
      "catalogueName",
      "catalogueId",
      "userGroups",
      "location"
    ]
    if (route.query.flag !== "add") {
      flag.value = route.query.flag
      const res = await getCourse(route.query.courseId)
      formData.value = res.data
      // 批量将逗号分割的字符串转为数组
      getProcessKeyArr.forEach(key => {
        formData.value[key] = formData.value[key]
          ? formData.value[key].split(",")
          : []
      })
      // 将目录ID数组中的元素都转为Number  解决回显问题
      formData.value.catalogueId = formData.value.catalogueId.map(Number)
      nextTick(() => {
        proxy.$refs["catalogueTreeRef"].setCheckedKeys(
          formData.value.catalogueId
        )
      })
      // 关联试题
      if (
        formData.value.courseQuestionLinkList &&
        formData.value.courseQuestionLinkList.length > 0
      ) {
        formData.value.ids = formData.value.courseQuestionLinkList.map(
          item => item.questionId
        )
      }
      await fetchPassageData()
      fetchReviewData()
      expandRowsWithInnerData()
    }
    initSort("tableContent")
  })

  // 课程类型变化
  const courseTypeChange = () => {
    // 如果已经有章节数据,需要提示用户
    if (formData.value.coursePassageList?.length > 0) {
      proxy.$modal
        .confirm("切换课程类型将清空已添加的章节数据，是否继续？")
        .then(() => {
          handleCourseTypeChange()
          // 清空章节数据
          formData.value.coursePassageList = []
        })
        .catch(() => {
          // 取消操作,恢复之前的课程类型
          formData.value.courseType = formData.value.courseType.filter(
            type => type === COURSE_TYPE.WORD_COURSE
          )
        })
    } else {
      handleCourseTypeChange()
    }
  }

  // 处理课程类型变化的具体逻辑
  const handleCourseTypeChange = () => {
    // 如果选择了文档课程
    if (isWordCourse.value) {
      // 如果同时还选择了其他课程类型,则只保留文档课程
      if (formData.value.courseType.length > 1) {
        formData.value.courseType = [COURSE_TYPE.WORD_COURSE]
        proxy.$modal.msgWarning("文档课程不能与其他课程类型同时选择")
      }
    } else {
      // 如果选择了其他课程类型,则从选项中移除文档课程
      formData.value.courseType = formData.value.courseType.filter(
        type => type !== COURSE_TYPE.WORD_COURSE
      )
    }
  }
</script>

<style lang="scss" scoped>
  :deep(.el-radio-group) {
    display: flex;
  }
  :deep(.el-input-number .el-input__wrapper) {
    padding-left: 50px;
    padding-right: 50px;
  }
  :deep(.el-form-item__content) {
    display: block;
  }
  :deep(.el-input) {
    display: inline-block;
    width: auto;
  }
  :deep(.el-textarea__inner) {
    display: inline-block;
    width: 400px;
  }

  .extendButton {
    position: absolute;
    left: 420px;
    top: 50px;
  }

  .extendCerBtn {
    position: absolute;
    left: 220px;
    top: 0;
  }
  .createExamBtn {
    margin-left: 20px;
  }

  .chapter:before {
    content: "*";
    color: var(--el-color-danger);
    margin-top: 7px;
  }

  .item_error {
    color: var(--el-color-danger);
    font-size: 12px;
    line-height: 1;
    padding-top: 2px;
    position: absolute;
    top: 100%;
    left: 0;
  }

  .file-name {
    height: 30px;
    margin-left: 30px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    > :last-child {
      margin-right: 30px;
      color: #589cfd;
      cursor: pointer;
    }
  }
</style>
