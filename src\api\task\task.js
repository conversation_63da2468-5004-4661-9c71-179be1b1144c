/*
 * @Description:任务实时报表API
 * @Author: <PERSON><PERSON>
 * @LastEditors: <PERSON><PERSON>
 * @Date: 2023-08-11 09:24:53
 * @LastEditTime: 2023-08-11 10:02:25
 */
import request from "@/utils/request"

// 查询课程列表
export function taskList(query) {
  return request({
    url: "/course/task/statistics/list",
    method: "get",
    params: query
  })
}

// 查询培训任务详细
export function getTaskDetail(taskId) {
  return request({
    url: "/course/task/" + taskId,
    method: "get"
  })
}
