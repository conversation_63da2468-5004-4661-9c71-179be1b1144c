<!--
 * @Description: 选择试题弹窗
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-05-11 13:16:25
 * @LastEditTime: 2023-05-30 17:07:47
-->
<template>
  <div class="app-container">
    <el-dialog
      v-model="dialogVisible"
      title="添加题目"
      width="50%"
      top="110px"
      @close="close"
      :key="new Date().getTime()"
      center
    >
      <el-form
        :model="queryParams"
        ref="queryRef"
        :inline="true"
        v-show="showSearch"
      >
        <el-form-item label="试题类型">
          <el-select
            v-model="queryParams.questionType"
            clearable
            placeholder="试题类型"
          >
            <el-option
              v-for="dict in questionnaire_question_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="题干">
          <el-input
            v-model="queryParams.questionName"
            placeholder="题干"
            clearable
            style="width: 240px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-table
        ref="questionAddTableRef"
        v-loading="loading"
        :data="tableData"
        row-key="traningQuestionId"
      >
        <el-table-column type="selection" width="55" reserve-selection />
        <el-table-column
          prop="traningQuestionId"
          label="Id"
          width="80"
        ></el-table-column>
        <el-table-column label="试题类型" width="100" prop="questionType">
          <template #default="scope">
            <dict-tag
              :options="questionnaire_question_type"
              :value="scope.row.questionType"
            />
          </template>
        </el-table-column>
        <el-table-column label="题干" prop="questionName"> </el-table-column>
      </el-table>

      <pagination
        class="dialog-pagination"
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
      <template #footer>
        <el-button @click="close">取消</el-button>
        <el-button type="primary" @click="save">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="chooseQuestionDialog">
import { listQuestion } from "@/api/trainingImplement/questionnaire.js"

const emit = defineEmits(["fetch-data"])
const { proxy } = getCurrentInstance()
const { questionnaire_question_type } = proxy.useDict(
  "questionnaire_question_type"
)

const tableData = ref([])
const loading = ref(true)
const showSearch = ref(true)
const dialogVisible = ref(false)
const total = ref(0)

const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10
  },
  selectedRows: []
})

const { queryParams, selectedRows } = toRefs(data)

//** 弹框打开事件 */
const openDialog = async idString => {
  dialogVisible.value = true
  selectedRows.value = []
  let questionIdArr
  if (idString) {
    questionIdArr = Array.isArray(idString)
      ? idString
      : idString.includes(",")
      ? idString.split(",")
      : [idString]
  }

  questionIdArr?.forEach(traningQuestionId => {
    selectedRows.value.push(traningQuestionId)
  })
  await getList()
  if (selectedRows.value) {
    nextTick(() => {
      tableData.value.forEach((item, index) => {
        if (selectedRows.value.find(v => v == item.traningQuestionId)) {
          proxy.$refs["questionAddTableRef"].toggleRowSelection(
            proxy.$refs["questionAddTableRef"].data[index],
            true
          )
        }
      })
    })
  }
}
/** 查询目录列表 */
const getList = async () => {
  loading.value = true
  const response = await listQuestion(queryParams.value)
  if (response.code === 200) {
    tableData.value = response.rows || []
    total.value = response.total
    loading.value = false
  }
}
/** 搜索按钮操作 */
function handleQuery() {
  getList()
}
/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef")
  handleQuery()
}
// 关闭弹框并重置操作
const close = () => {
  proxy.resetForm("queryRef")
  dialogVisible.value = false
}
const save = () => {
  const selectionRows = proxy.$refs["questionAddTableRef"].getSelectionRows()
  const idArray = selectionRows.map(item => item.traningQuestionId)
  emit("fetch-data", idArray)
  proxy.$modal.msgSuccess("操作成功")
  close()
}

defineExpose({
  openDialog
})
</script>

<style lang="scss" scoped>
:deep(.pagination-container .el-pagination) {
  position: static;
}
</style>
