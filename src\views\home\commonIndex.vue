<!--
 * @Description: 首页
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-03-03 17:06:07
 * @LastEditTime: 2023-12-12 16:59:13
-->
<template>
  <div class="home">
    <el-row :gutter="20">
      <el-col :span="24">
        <div class="home-title">今日数据一览</div>
        <ul class="list-item">
          <li>
            <p>今日学习人数</p>
            <span class="num" style="color: #8287fc">{{
              dashboardData.todayLearners
            }}</span>
            <span
              class="icon"
              style="
                background: linear-gradient(to bottom, #cb87ee, #947fed);
                box-shadow: 0 0 6px #8287fc;
              "
            >
              <el-icon size="20">
                <i class="iconfont icon-xuexiyuandi"></i>
              </el-icon>
            </span>
          </li>
          <li>
            <p>本月学习人数</p>
            <span class="num" style="color: #f46539">{{
              dashboardD<PERSON>.thisM<PERSON><PERSON><PERSON>
            }}</span>
            <span
              class="icon"
              style="
                background: linear-gradient(to bottom, #e2ad91, #ec7649);
                box-shadow: 0 0 6px #ec7649;
              "
            >
              <el-icon size="20">
                <i class="iconfont icon-bisai-"></i>
              </el-icon>
            </span>
          </li>
          <li>
            <p>全部任务</p>
            <span class="num" style="color: #53d4c8">{{
              dashboardData.totalTask
            }}</span>
            <span
              class="icon"
              style="
                background: linear-gradient(to bottom, #6de7c2, #24d3c3);
                box-shadow: 0 0 6px #24d3c3;
              "
            >
              <el-icon size="20">
                <i class="iconfont icon-renwu"></i>
              </el-icon>
            </span>
          </li>
          <li>
            <p>进行中任务</p>
            <span class="num" style="color: #ed9d32">{{
              dashboardData.inProgressTask
            }}</span>
            <span
              class="icon"
              style="
                background: linear-gradient(to bottom, #fe7b63, #f86135);
                box-shadow: 0 0 6px #f86135;
              "
            >
              <el-icon size="20">
                <i class="iconfont icon-shouye"></i>
              </el-icon>
            </span>
          </li>
          <li>
            <p>未开始任务</p>
            <span class="num" style="color: #6bc97b">{{
              dashboardData.notStartedTask
            }}</span>
            <span
              class="icon"
              style="
                background: linear-gradient(to bottom, #11c8fc, #0384e8);
                box-shadow: 0 0 6px #0384e8;
              "
            >
              <el-icon size="20">
                <i class="iconfont icon-shiyongwendang"></i>
              </el-icon>
            </span>
          </li>
          <li>
            <p>已结束任务</p>
            <span class="num" style="color: #5e8aee">{{
              dashboardData.completedTask
            }}</span>
            <span
              class="icon"
              style="
                background: linear-gradient(to bottom, #ff96c0, #fe5dae);
                box-shadow: 0 0 6px #fe5dae;
              "
            >
              <el-icon size="20">
                <i
                  class="iconfont icon-weikaishi"
                  style="left: 11px !important"
                ></i>
              </el-icon>
            </span>
          </li>
          <li>
            <p>已超期任务</p>
            <span class="num" style="color: #c181fd">{{
              dashboardData.expiredTask
            }}</span>
            <span
              class="icon"
              style="
                background: linear-gradient(to bottom, #ff8d7b, #fe555c);
                box-shadow: 0 0 6px #fe555c;
              "
            >
              <el-icon size="20">
                <i class="iconfont icon-chaoshi"></i>
              </el-icon>
            </span>
          </li>
        </ul>
      </el-col>
    </el-row>
    <el-row :gutter="20" style="margin-top: 20px">
      <el-col :span="14"> <targetList /></el-col>
      <el-col :span="10"> <targetView /></el-col>
    </el-row>
    <el-row :gutter="20" style="margin-top: 20px">
      <el-col :span="14"> <learnSpread /></el-col>
      <el-col :span="10"> <learnRank /></el-col>
    </el-row>
  </div>
</template>

<script setup>
  import targetList from "./components/targetList.vue"
  import targetView from "./components/targetView.vue"
  import learnSpread from "./components/learnSpread.vue"
  import learnRank from "./components/learnRank.vue"
  import { getTaskDashboard } from "@/api/home/<USER>"

  const dashboardData = reactive({
    todayLearners: "",
    thisMonthLearners: "",
    totalTask: "",
    inProgressTask: "",
    notStartedTask: "",
    completedTask: "",
    expiredTask: "",
    almostOverTask: ""
  })
  getTaskDashboard().then(res => {
    dashboardData.todayLearners = res.data.todayLearners
    dashboardData.thisMonthLearners = res.data.thisMonthLearners
    dashboardData.totalTask = res.data.totalTask
    dashboardData.inProgressTask = res.data.inProgressTask
    dashboardData.notStartedTask = res.data.notStartedTask
    dashboardData.completedTask = res.data.completedTask
    dashboardData.expiredTask = res.data.expiredTask
    dashboardData.almostOverTask = res.data.almostOverTask
  })
</script>

<style lang="scss" scoped>
  .iconfont {
    font-size: 20px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-2px, -2px);
    color: #fff;
  }
  .home {
    background: #f3f5ff;
    padding: 20px;
    color: #222d61;
  }
  .home-title {
    line-height: 40px;
    font-weight: bold;
    font-size: 20px;
    text-indent: 10px;
    margin-bottom: 10px;
  }
  .list-item {
    white-space: nowrap;
    display: flex;

    > li {
      flex: 1;
      padding: 16px 20px;
      margin: 0 10px;
      position: relative;
      display: inline-block;
      background: #fff;
      line-height: 30px;
      font-weight: bold;
      border-radius: 10px;
    }
    .icon {
      position: absolute;
      bottom: 20px;
      right: 20px;
      width: 36px;
      height: 36px;
      border-radius: 100%;
    }
    .num {
      margin-top: 10px;
      font-weight: bold;
      display: inline-block;
      font-size: 32px;
    }
  }
</style>
