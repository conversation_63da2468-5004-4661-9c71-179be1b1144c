<!--
 * @Description: 租户列表
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-06-13 15:09:09
 * @LastEditTime: 2025-07-16 09:31:30
-->
<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
    >
      <el-form-item label="租户名" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入租户名"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="联系人" prop="contactName">
        <el-input
          v-model="queryParams.contactName"
          placeholder="请输入联系人"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="联系手机" prop="contactMobile">
        <el-input
          v-model="queryParams.contactMobile"
          placeholder="请输入联系手机"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
          maxlength="11"
          @input="v => (queryParams.contactMobile = v.replace(/[^\d.]/g, ''))"
        />
      </el-form-item>
      <el-form-item label="租户状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="租户状态"
          clearable
          style="width: 200px"
        >
          <el-option
            v-for="dict in sys_normal_disable"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间" style="width: 308px">
        <el-date-picker
          v-model="dateRange"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb-8px">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport"
          >导出</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="Refresh" @click="openDialog"
          >域名刷新</el-button
        >
      </el-col>
      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="tenantList">
      <el-table-column label="租户名" prop="name" width="260" />
      <el-table-column label="联系人" prop="contactName" />
      <el-table-column label="联系手机" prop="contactMobile" />
      <el-table-column label="账号额度" prop="accountCount" width="90">
        <template #default="scope">
          <el-tag>
            {{ scope.row.accountCount }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="过期时间" prop="expireTime">
        <template #default="scope">
          <div :class="getExpireStatusClass(scope.row.expireTime)" class="expire-time-cell">
            {{ scope.row.expireTime }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="绑定域名" prop="domain" />
      <el-table-column label="租户状态" prop="status" width="80">
        <template #default="scope">
          <dict-tag :options="sys_normal_disable" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="创建时间" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        width="180"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-button
            link
            type="primary"
            icon="Edit"
            @click="handleUpdate(scope.row)"
            >修改</el-button
          >
          <el-button
            link
            type="primary"
            icon="Delete"
            @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
    <!-- 新增/修改租户弹窗 -->
    <AddEditTenantDialog
      ref="addEditTenantDialogRef"
      @refreshDataList="getList"
    />
    <!-- 刷新域名弹窗 -->
    <RefreshUrlDialog ref="refreshUrlDialogRef" @refreshDataList="getList" />
  </div>
</template>

<script setup name="tenantList">
  import AddEditTenantDialog from "./components/addEditTenantDialog.vue"
  import RefreshUrlDialog from "./components/refreshUrlDialog.vue"
  import { listTenant, delTenant } from "@/api/system/tenant"

  const { proxy } = getCurrentInstance()
  const { sys_normal_disable } = proxy.useDict("sys_normal_disable")

  const dateRange = ref([])
  const tenantList = ref([])
  const loading = ref(true)
  const showSearch = ref(true)
  const total = ref(0)
  let env = import.meta.env.VITE_APP_ENV

  const data = reactive({
    queryParams: {
      pageNum: 1,
      pageSize: 10
    }
  })
  const { queryParams } = toRefs(data)

  /** 查询租户列表 */
  function getList() {
    loading.value = true
    listTenant(proxy.addDateRange(queryParams.value, dateRange.value)).then(
      response => {
        tenantList.value = response.rows
        total.value = response.total
        loading.value = false
      }
    )
  }
  /** 搜索按钮操作 */
  function handleQuery() {
    queryParams.value.pageNum = 1
    getList()
  }
  /** 重置按钮操作 */
  function resetQuery() {
    proxy.resetForm("queryRef")
    handleQuery()
  }
  const addEditTenantDialogRef = ref(null)
  /** 新增按钮操作 */
  async function handleAdd() {
    addEditTenantDialogRef.value.openDialog()
  }
  /** 修改按钮操作 */
  async function handleUpdate(row) {
    addEditTenantDialogRef.value.openDialog(row.id)
  }
  /** 删除按钮操作 */
  function handleDelete(row) {
    proxy.$modal
      .confirm('是否确认删除租户编号为"' + row.id + '"的数据项？')
      .then(function () {
        return delTenant(row.id)
      })
      .then(() => {
        getList()
        proxy.$modal.msgSuccess("删除成功")
      })
      .catch(() => {})
  }
  /** 导出按钮操作 */
  function handleExport() {
    proxy.download(
      "system/tenant/export",
      {
        ...queryParams.value
      },
      `tenant_${new Date().getTime()}.xlsx`
    )
  }
  const refreshUrlDialogRef = ref(null)
  const openDialog = () => {
    refreshUrlDialogRef.value.openDialog()
  }

  // 计算过期状态类名
  const getExpireStatusClass = (expireTime) => {
    if (!expireTime) return 'expire-normal'
    
    const currentDate = new Date()
    const expireDate = new Date(expireTime)
    
    // 如果已过期（当前日期大于过期日期）
    if (currentDate > expireDate) {
      return 'expire-overdue'
    }
    
    // 计算一个月后的日期
    const oneMonthLater = new Date()
    oneMonthLater.setMonth(oneMonthLater.getMonth() + 1)
    
    // 如果在一个月内过期
    if (expireDate <= oneMonthLater) {
      return 'expire-warning'
    }
    
    // 正常状态
    return 'expire-normal'
  }

  getList()
</script>

<style lang="scss" scoped>
  .expire-time-cell {
    padding: 6px 12px;
    border-radius: 4px;
    font-weight: 500;
    transition: all 0.2s ease;
  }

  .expire-overdue {
    background-color: #fef0f0;
    color: #f56c6c;
    border: 1px solid #fab6b6;
  }

  .expire-warning {
    background-color: #fdf6ec;
    color: #e6a23c;
    border: 1px solid #f5dab1;
  }

  .expire-normal {
    background-color: transparent;
    color: inherit;
  }
</style>
