<!--
 * @Description: 新增/修改短视频
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-04-10 17:21:57
 * @LastEditTime: 2023-05-31 10:17:22
-->
<template>
  <el-dialog
    v-model="visible"
    :title="!dataForm.vlogId ? '新增' : '修改'"
    :close-on-click-modal="false"
    center
  >
    <el-form
      ref="dataFormRef"
      :model="dataForm"
      :rules="dataVideos"
      label-width="130px"
      @keyup.enter="submitHandle()"
    >
      <el-row :gutter="10">
        <el-col :span="24">
          <el-form-item label="视频播放地址" prop="url">
            <FileUpload
              v-model="dataForm.url"
              :fileSize="200"
              :fileType="['wmv', 'mp4', 'flv', 'avi', 'rmvb', 'mpg']"
              @onVideoUploadSuccess="handleVideoUploadSuccess"
            />
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="视频封面" prop="cover">
            <ImageUpload v-model="dataForm.cover" :limit="1" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="视频标题" prop="title">
            <el-input v-model="dataForm.title" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="submitHandle()">确定</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import { getVideo, addVideo, updateVideo } from "@/api/video/video.js"

const emit = defineEmits(["refreshDataList"])

const { proxy } = getCurrentInstance()

const visible = ref(false)
const dataFormRef = ref()
let dataForm = ref({})

// 打开弹窗事件
const openDialog = async id => {
  // 重置表单数据
  dataForm.value = {}

  if (id) {
    const { data } = await getVideo(id)
    Object.assign(dataForm.value, data)
  }
  visible.value = true
}

const dataVideos = ref({
  ruleType: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
  cycle: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
  maxNumber: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
  singleScore: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
  isUsing: [{ required: true, message: "必填项不能为空", trigger: "blur" }]
})

// 表单提交
const submitHandle = () => {
  dataFormRef.value.validate(valid => {
    if (!valid) {
      return false
    }
    if (!dataForm.value.vlogId) {
      addVideo(dataForm.value).then(res => {
        if (res.code === 200) {
          proxy.$modal.msgSuccess("新增成功")
          visible.value = false
          emit("refreshDataList")
        }
      })
    } else {
      updateVideo(dataForm.value).then(res => {
        if (res.code === 200) {
          proxy.$modal.msgSuccess("修改成功")
          visible.value = false
          emit("refreshDataList")
        }
      })
    }
  })
}

// 上传成功
const handleVideoUploadSuccess = event => {
  dataForm.value.width = event.width
  dataForm.value.height = event.height
}

defineExpose({
  openDialog
})
</script>
