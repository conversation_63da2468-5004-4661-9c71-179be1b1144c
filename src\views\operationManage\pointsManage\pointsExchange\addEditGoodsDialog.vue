<template>
  <el-dialog
    v-model="visible"
    :title="!dataForm.goodsId ? '新增' : '修改'"
    :close-on-click-modal="false"
    center
  >
    <el-form
      ref="dataFormRef"
      :model="dataForm"
      :rules="dataRules"
      label-width="130px"
      @keyup.enter="submitHandle()"
    >
      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item label="赠品名称" prop="goodsName">
            <el-input v-model="dataForm.goodsName"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="22">
          <el-form-item label="赠品图片" prop="photoUrl">
            <ImageUpload v-model="dataForm.photoUrl" :limit="1" />
          </el-form-item>
        </el-col>
        <el-col :span="22">
          <el-form-item label="商品介绍" prop="intro">
            <el-input type="textarea" v-model="dataForm.intro"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="16">
          <el-form-item label="商品价格" prop="goodsPrice">
            <el-input-number :min="0" v-model="dataForm.goodsPrice" :step="1" />
          </el-form-item>
        </el-col>
        <el-col :span="11">
          <el-form-item label="开始时间" prop="startDate">
            <el-date-picker
              v-model="dataForm.startDate"
              type="date"
              placeholder="开始时间"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
        </el-col>
        <el-col :span="11">
          <el-form-item label="结束时间" prop="endDate">
            <el-date-picker
              v-model="dataForm.endDate"
              type="date"
              placeholder="结束时间"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="是否推荐" prop="recommend">
            <el-radio-group v-model="dataForm.recommend">
              <template v-for="(item, index) in goods_recommend_type">
                <el-radio-button :label="item.value">
                  {{ item.label }}
                </el-radio-button>
              </template>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="兑换积分数" prop="integral">
            <el-input-number :min="0" v-model="dataForm.integral" :step="1" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="可兑换数" prop="availableQty">
            <el-input-number
              :min="0"
              v-model="dataForm.availableQty"
              :step="1"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="最多兑换 /每人" prop="limitQty">
            <el-input-number :min="0" v-model="dataForm.limitQty" :step="1" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="已兑换数量" prop="exchangeQty">
            <el-input-number
              :min="0"
              v-model="dataForm.exchangeQty"
              :step="1"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="赠品上架状态" prop="goodsStatus">
            <el-radio-group v-model="dataForm.goodsStatus">
              <template v-for="(item, index) in goods_type">
                <el-radio-button :label="item.value">
                  {{ item.label }}
                </el-radio-button>
              </template>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="22">
          <el-form-item label="兑换规则说明" prop="exchangeRule">
            <el-input
              type="textarea"
              v-model="dataForm.exchangeRule"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="submitHandle()">确定</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import {
  getExchange,
  addExchange,
  updateExchange
} from "@/api/opertaionManage/goods.js"

const emit = defineEmits(["refreshDataList"])

const { proxy } = getCurrentInstance()

const visible = ref(false)
const dataFormRef = ref()
let dataForm = ref({})
const { goods_type, goods_recommend_type } = proxy.useDict(
  "goods_type",
  "goods_recommend_type"
)

// 打开弹窗事件
const openDialog = async id => {
  // 重置表单数据
  dataForm.value = {}

  if (id) {
    const { data } = await getExchange(id)
    Object.assign(dataForm.value, data)
  }
  visible.value = true
}

const dataRules = ref({
  goodsName: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
  photoUrl: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
  integral: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
  exchangeQty: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
  goodsStatus: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
  availableQty: [{ required: true, message: "必填项不能为空", trigger: "blur" }]
})

// 表单提交
const submitHandle = () => {
  dataFormRef.value.validate(valid => {
    if (!valid) {
      return false
    }
    if (!dataForm.value.goodsId) {
      addExchange(dataForm.value).then(res => {
        if (res.code === 200) {
          proxy.$modal.msgSuccess("新增成功")
          visible.value = false
          emit("refreshDataList")
        }
      })
    } else {
      updateExchange(dataForm.value).then(res => {
        if (res.code === 200) {
          proxy.$modal.msgSuccess("修改成功")
          visible.value = false
          emit("refreshDataList")
        }
      })
    }
  })
}

defineExpose({
  openDialog
})
</script>
