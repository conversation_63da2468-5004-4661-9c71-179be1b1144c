/*
 * @Description: 资料管理模块相关api
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-03-08 11:44:11
 * @LastEditTime: 2023-03-13 10:31:40
 */
import request from "@/utils/request"

// 查询资料列表
export function listMeans(query) {
  return request({
    url: "/course/manage/list",
    method: "get",
    params: query
  })
}

// 查询资料详细
export function getMeans(paperId) {
  return request({
    url: "/course/manage/" + paperId,
    method: "get"
  })
}

// 新增资料
export function addMeans(data) {
  return request({
    url: "/course/manage",
    method: "post",
    data: data
  })
}

// 修改资料
export function updateMeans(data) {
  return request({
    url: "/course/manage",
    method: "put",
    data: data
  })
}

// 删除资料
export function delMeans(paperId) {
  return request({
    url: "/course/manage/" + paperId,
    method: "delete"
  })
}
