/*
 * @Description: 积分管理相关API
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-04-10 16:31:10
 * @LastEditTime: 2023-04-10 17:00:05
 */

import request from "@/utils/request"
// 查询积分规则列表
export function listRule(query) {
  return request({
    url: "/devops/rule/list",
    method: "get",
    params: query
  })
}

// 查询积分规则详细
export function getRule(ruleId) {
  return request({
    url: "/devops/rule/" + ruleId,
    method: "get"
  })
}

// 新增积分规则
export function addRule(data) {
  return request({
    url: "/devops/rule",
    method: "post",
    data: data
  })
}

// 修改积分规则
export function updateRule(data) {
  return request({
    url: "/devops/rule",
    method: "put",
    data: data
  })
}

// 删除积分规则
export function delRule(ruleId) {
  return request({
    url: "/devops/rule/" + ruleId,
    method: "delete"
  })
}

// 积分兑换记录列表
export function listRecord(query) {
  return request({
    url: "/devops/log/list",
    method: "get",
    params: query
  })
}

// 删除积分兑换记录
export function delRecord(ruleId) {
  return request({
    url: "/devops/log/" + ruleId,
    method: "delete"
  })
}
