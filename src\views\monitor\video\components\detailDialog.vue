<!--
 * @Description: 视频监控详情弹窗
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-12-06 15:31:08
 * @LastEditTime: 2024-12-06 16:15:15
-->
<template>
  <div class="app-container">
    <el-dialog
      v-model="dialogVisible"
      title="培训任务名称"
      width="70%"
      top="110px"
      @close="close"
      :key="new Date().getTime()"
      center
    >
      <el-form
        :model="queryParams"
        ref="queryRef"
        :inline="true"
        v-show="showSearch"
        label-width="68px"
      >
        <el-form-item label="任务名称" prop="subTaskName">
          <el-input
            v-model="queryParams.subTaskName"
            placeholder="请输入任务名称"
            clearable
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="是否匹配" prop="isMatch">
          <el-select
            v-model="queryParams.isMatch"
            placeholder="请选择"
            clearable
          >
            <el-option
              v-for="item in is_or_not"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-table :data="tableData" style="width: 100%">
        <el-table-column type="index" label="序号" width="50" />
        <el-table-column prop="subTaskName" label="任务名称" />
        <el-table-column prop="captureTime" label="时间" />
        <el-table-column label="本人照片">
          <template #default="scope">
            <el-image
              class="w-100px h-100px"
              :src="scope.row.faceImg"
              :preview-src-list="[scope.row.faceImg]"
            />
          </template>
        </el-table-column>
        <el-table-column label="抓拍照片">
          <template #default="scope">
            <el-image
              class="w-100px h-100px"
              :src="scope.row.capturePhoto"
              :preview-src-list="[scope.row.capturePhoto]"
            />
          </template>
        </el-table-column>
        <el-table-column prop="isMatch" label="是否匹配">
          <template #default="scope">
            <dict-tag :options="is_or_not" :value="scope.row.isMatch" />
          </template>
        </el-table-column>
      </el-table>

      <pagination
        class="dialog-pagination"
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
        :autoScroll="false"
      />
      <template #footer>
        <el-button @click="close">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="detailDialog">
  import { videoMonitorDetailList } from "@/api/monitor/video"
  import useUserStore from "@/store/modules/user"

  const { proxy } = getCurrentInstance()
  const { is_or_not } = useDict("is_or_not")
  const userStore = useUserStore()

  const dialogVisible = ref(false)
  const showSearch = ref(true)
  const total = ref(0)
  const tableData = ref([])

  const data = reactive({
    queryParams: {
      pageNum: 1,
      pageSize: 10,
      taskName: undefined,
      isMatch: undefined,
      taskId: undefined,
      userId: userStore.userId
    }
  })

  const { queryParams } = toRefs(data)

  /** 查询列表 */
  const getList = async () => {
    const res = await videoMonitorDetailList(queryParams.value)
    if (res.code === 200) {
      tableData.value = res.rows
      total.value = res.total
    }
  }

  /** 搜索按钮操作 */
  function handleQuery() {
    queryParams.value.pageNum = 1
    getList()
  }

  /** 重置按钮操作 */
  function resetQuery() {
    proxy.resetForm("queryRef")
    handleQuery()
  }

  /** 关闭弹窗 */
  const close = () => {
    dialogVisible.value = false
    proxy.resetForm("queryRef")
    queryParams.value.pageNum = 1
  }

  /** 打开弹窗 */
  const openDialog = taskId => {
    dialogVisible.value = true
    queryParams.value.taskId = taskId
    getList()
  }

  defineExpose({
    openDialog
  })
</script>

<style lang="scss" scoped>
  :deep(.pagination-container .el-pagination) {
    position: static;
  }
</style>
