<!--
 * @Description: 学时统计
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-06-08 16:50:00
 * @LastEditTime: 2024-02-26 13:28:00
-->

<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
    >
      <el-form-item label="姓名" prop="userName">
        <el-input
          v-model="queryParams.userName"
          placeholder="请输入姓名"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="部门" prop="deptName">
        <el-input
          v-model="queryParams.deptName"
          placeholder="请输入部门"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="学习时间" prop="dateRange">
        <el-date-picker
          v-model="dateRange"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="selectDate"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="请选择任务状态"
          clearable
          style="width: 200px"
        >
          <el-option
            v-for="dict in sys_job_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        label="所属分公司"
        prop="domainName"
        v-if="roles.includes('taibao_admin') || user.userId === 1"
      >
        <el-tree-select
          v-model="queryParams.domainName"
          :data="deptOptions"
          :props="{ value: 'domainName', label: 'label', children: 'children' }"
          value-key="id"
          placeholder="请选择所属分公司"
          check-strictly
          clearable
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb-8px">
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport">
          导出
        </el-button>
      </el-col>
      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="tableData">
      <el-table-column label="姓名" prop="userName" />
      <el-table-column label="所属部门" prop="deptName" />
      <el-table-column label="状态" prop="status">
        <template #default="scope">
          <dict-tag :options="sys_job_status" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="在线课程">
        <el-table-column label="总学习时长" prop="learnTime">
          <template #default="scope">
            {{ formatSeconds(scope.row.learnTime) }}
          </template>
        </el-table-column>
        <el-table-column label="指派数" prop="assignCourseCount" />
        <el-table-column label="完成数" prop="assignCompleteCount" />
      </el-table-column>
      <el-table-column label="考试">
        <el-table-column label="安排数" prop="examCount" />
        <el-table-column label="完成数" prop="examPassCount" />
        <el-table-column label="通过数" prop="examPassCount" />
      </el-table-column>
      <el-table-column label="问卷">
        <el-table-column label="安排数" prop="questionnaireCount" />
        <el-table-column label="参与数" prop="quesCompleteCount" />
      </el-table-column>
      <el-table-column
        label="操作"
        width="130"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-button
            link
            type="primary"
            icon="View"
            @click="handleDetail(scope.row)"
          >
            查看详情
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script setup name="hourStatistics">
  import dayjs from "dayjs"
  import { getTrainingTaskStatistics } from "@/api/trainingProfile/statistics.js"
  import { formatSeconds } from "@/utils/common.js"
  import { deptTreeSelect } from "@/api/system/user"
  import useUserStore from "@/store/modules/user"

  const { roles, user } = storeToRefs(useUserStore())
  const { proxy } = getCurrentInstance()
  const { sys_job_status } = proxy.useDict("sys_job_status")

  const tableData = ref([])
  const loading = ref(true)
  const showSearch = ref(true)
  const total = ref(0)
  const dateRange = ref([])
  const emit = defineEmits(["updateCurrentView"])

  const data = reactive({
    queryParams: {
      pageNum: 1,
      pageSize: 10
    }
  })

  const { queryParams } = toRefs(data)

  /** 查询目录列表 */
  function getList() {
    loading.value = true
    getTrainingTaskStatistics(queryParams.value).then(response => {
      tableData.value = response.rows || []
      total.value = response.total
      loading.value = false
    })
  }
  /** 搜索按钮操作 */
  function handleQuery() {
    getList()
  }
  /** 重置按钮操作 */
  function resetQuery() {
    dateRange.value = []
    proxy.resetForm("queryRef")
    handleQuery()
  }

  const handleDetail = row => {
    emit("updateCurrentView", "personalProfile", row)
  }

  const selectDate = () => {
    if (dateRange.value != null) {
      queryParams.value.queryTimeFrom = dateRange.value[0]
      queryParams.value.queryTimeTo = dateRange.value[1]
    } else {
      queryParams.value.queryTimeFrom = ""
      queryParams.value.queryTimeTo = ""
    }
  }

  /** 导出按钮操作 */
  function handleExport() {
    proxy.download(
      "reportforms/report/export/hoursList",
      {
        ...queryParams.value
      },
      `学时统计_${dayjs().format("YYYY-MM-DD")}.xlsx`
    )
  }
  const deptOptions = ref([])
  /** 查询部门下拉树结构 */
  const getBranchOfficeTree = async () => {
    const response = await deptTreeSelect({
      parentId: 100
    })
    deptOptions.value = response.data
  }

  getList()
  if (roles.value.includes("taibao_admin") || user.value.userId === 1) {
    getBranchOfficeTree()
  }
</script>
