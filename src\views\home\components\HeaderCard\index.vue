<!--
 * @Description: 首页头部
 * @Author: <PERSON><PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-08-11 14:50:23
 * @LastEditTime: 2023-10-18 16:03:26
-->
<template>
  <div class="header-card">
    <el-card :bordered="false">
      <template #header> <div class="title">大数据看板</div> </template>
      <div class="header" v-if="taskBoardData">
        <div
          class="item"
          :class="activeIndex === undefined ? 'active' : ''"
          @click="changeActiveIndex(undefined)"
        >
          <div>全部任务</div>
          <div>{{ taskBoardData.totalTask || 0 }}项</div>
        </div>
        <div
          class="item"
          :class="activeIndex === '2' ? 'active' : ''"
          @click="changeActiveIndex('2')"
        >
          <div>进行中任务</div>
          <div>{{ taskBoardData.inProgressTask || 0 }}项</div>
        </div>
        <div
          class="item"
          :class="activeIndex === '1' ? 'active' : ''"
          @click="changeActiveIndex('1')"
        >
          <div>未开始任务</div>
          <div>{{ taskBoardData.notStartedTask || 0 }}项</div>
        </div>
        <div
          class="item"
          :class="activeIndex === '3' ? 'active' : ''"
          @click="changeActiveIndex('3')"
        >
          <div>已完成任务</div>
          <div>{{ taskBoardData.completedTask || 0 }}项</div>
        </div>
        <div
          class="item"
          :class="activeIndex === '4' ? 'active' : ''"
          @click="changeActiveIndex('4')"
        >
          <div>已超期任务</div>
          <div>{{ taskBoardData.expiredTask || 0 }}项</div>
        </div>
      </div>
    </el-card>
    <el-table :data="tableData" class="table">
      <el-table-column label="序号" prop="taskId"></el-table-column>
      <el-table-column label="任务名称" prop="taskName" />
      <el-table-column label="发起部门" prop="startDeptName" />
      <el-table-column label="开始时间" prop="startTime" />
      <el-table-column label="结束时间" prop="endTime" />
      <el-table-column label="任务状态" prop="taskStatus">
        <template #default="scope">
          <dict-tag
            :options="training_task_status"
            :value="scope.row.taskStatus"
          />
        </template>
      </el-table-column>
      <el-table-column label="操作" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="View" @click="jumpTo(scope.row)"
            >查看</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <pagination
      class="pagination"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script setup>
  import { listTrainingTask } from "@/api/trainingImplement/trainingTask.js"
  import { getTaskDashboard } from "@/api/home/<USER>"

  const { proxy } = getCurrentInstance()
  const { training_task_status } = proxy.useDict("training_task_status")

  const activeIndex = ref()
  const tableData = ref([])
  const total = ref(0)
  const taskBoardData = ref({})

  const data = reactive({
    queryParams: {
      pageNum: 1,
      pageSize: 10
    }
  })

  const { queryParams } = toRefs(data)

  function getList(index) {
    let queryData = {
      taskStatus: index,
      ...queryParams.value
    }
    listTrainingTask(queryData).then(response => {
      tableData.value = response.rows || []
      total.value = response.total
    })
  }

  const changeActiveIndex = index => {
    if (activeIndex.value !== index) {
      activeIndex.value = index
      getList(index)
    }
  }

  const getTaskBoardData = async () => {
    const res = await getTaskDashboard()
    taskBoardData.value = res.data
  }

  const jumpTo = row => {
    proxy.$tab.openPage({
      path: "/trainingImplement/trainingTask",
      query: row
    })
  }

  getList()
  getTaskBoardData()
</script>

<style scoped lang="scss">
  .header-card {
    background-color: #fff;
  }
  .title {
    padding: 8px;
    font-size: 19px;
    font-weight: bolder;
  }
  .header {
    display: flex;
    align-items: center;
    justify-content: space-evenly;

    .item {
      cursor: pointer;
      background-color: pink;
      width: 18%;
      display: flex;
      justify-content: space-evenly;
      height: 100%;
      flex-direction: column;
      height: 90px;
      padding: 0 30px;
      border-radius: 10px;
      color: white;
      transition: box-shadow 0.2s ease-in-out;
      > :first-child {
        font-size: 18px;
      }
      > :last-child {
        font-size: 30px;
        font-weight: bold;
      }
    }
    .active {
      box-shadow: 0 0 10px 2px rgba(0, 0, 255, 0.5);
    }

    > :nth-child(1) {
      background-color: #516ae6;
    }
    > :nth-child(2) {
      background-color: #ff870f;
    }
    > :nth-child(3) {
      background-color: #0089ff;
    }
    > :nth-child(4) {
      background-color: #30c283;
    }
    > :nth-child(5) {
      background-color: #607dff;
    }
    > :nth-child(6) {
      background-color: skyblue;
    }
  }

  .table {
    width: 98%;
    margin: 20px auto;
    margin-bottom: 50px;
  }

  .pagination {
    padding-bottom: 50px;
  }
</style>
