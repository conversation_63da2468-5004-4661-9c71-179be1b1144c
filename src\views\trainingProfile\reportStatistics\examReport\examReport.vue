<!--
 * @Description: 考试报表统计
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-08-09 13:54:32
 * @LastEditTime: 2025-01-17 11:39:32
-->
<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
    >
      <el-form-item label="考试名称" prop="examName">
        <el-input
          v-model="queryParams.examName"
          placeholder="请输入考试名称"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="考试时间" prop="dateRange">
        <el-date-picker
          v-model="dateRange"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="selectDate"
        ></el-date-picker>
      </el-form-item>

      <el-form-item
        label="所属分公司"
        prop="tenantId"
        v-if="roles.includes('taibao_admin') || user.userId === 1"
      >
        <el-tree-select
          v-model="queryParams.tenantId"
          :data="deptOptions"
          :props="{ value: 'domainName', label: 'label', children: 'children' }"
          value-key="id"
          placeholder="请选择所属分公司"
          check-strictly
          clearable
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb-8px">
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport">
          导出
        </el-button>
      </el-col>
      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="tableData">
      <el-table-column label="序号" width="60" type="index" />
      <el-table-column label="考试名称" prop="examName" />
      <el-table-column label="考试开始时间 / 考试结束时间" width="350">
        <template #default="scope">
          {{ scope.row.startTime }} / {{ scope.row.endTime }}
        </template>
      </el-table-column>
      <el-table-column label="创建时间" prop="createTime" width="150"/>
      <el-table-column label="考试时长" prop="examDuration" width="100">
        <template #default="scope"> {{ scope.row.examDuration }}分钟 </template>
      </el-table-column>
      <el-table-column label="安排人数" prop="arrangedCount" width="80" />
      <el-table-column label="参考人数" prop="examedCount" width="80" />
      <el-table-column label="通过人数" prop="passCount" width="80" />
      <el-table-column label="通过率" prop="passingRate" width="90">
        <template #default="scope">
          {{ parseFloat(scope.row.passingRate * 100)?.toFixed(2) || 0 }}%
        </template>
      </el-table-column>
      <el-table-column label="平均成绩" prop="avgScore" width="110">
        <template #default="scope">
          {{ parseFloat(scope.row.avgScore?.toFixed(2)) || 0 }}分
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        width="120"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-button
            link
            type="primary"
            icon="View"
            @click="handleDetail(scope.row)"
          >
            明细
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script setup name="hourStatistics">
  import dayjs from "dayjs"
  import { examReportList } from "@/api/onlineExam/exam.js"
  import { deptTreeSelect } from "@/api/system/user"
  import useUserStore from "@/store/modules/user"

  const { roles, user } = storeToRefs(useUserStore())
  const { proxy } = getCurrentInstance()
  const tableData = ref([])
  const loading = ref(true)
  const showSearch = ref(true)
  const total = ref(0)
  const dateRange = ref([])
  const emit = defineEmits(["updateCurrentView"])

  const data = reactive({
    queryParams: {
      pageNum: 1,
      pageSize: 10
    }
  })

  const { queryParams } = toRefs(data)

  /** 查询目录列表 */
  function getList() {
    loading.value = true
    examReportList(queryParams.value).then(response => {
      tableData.value = response.rows || []
      total.value = response.total
      loading.value = false
    })
  }
  /** 搜索按钮操作 */
  function handleQuery() {
    getList()
  }
  /** 重置按钮操作 */
  function resetQuery() {
    dateRange.value = []
    proxy.resetForm("queryRef")
    handleQuery()
  }

  const handleDetail = row => {
    emit("updateCurrentView", "studentExamDetails", row)
  }

  const selectDate = () => {
    if (dateRange.value != null) {
      queryParams.value.queryTimeFrom = dateRange.value[0]
      queryParams.value.queryTimeTo = dateRange.value[1]
    } else {
      queryParams.value.queryTimeFrom = ""
      queryParams.value.queryTimeTo = ""
    }
  }

  /** 导出按钮操作 */
  function handleExport() {
    proxy.download(
      "exam/arrange/export/examList",
      {
        ...queryParams.value
      },
      `考试报表_${dayjs().format("YYYY-MM-DD")}.xlsx`
    )
  }
  const deptOptions = ref([])
  /** 查询部门下拉树结构 */
  const getBranchOfficeTree = async () => {
    const response = await deptTreeSelect({
      parentId: 100
    })
    deptOptions.value = response.data
  }

  getList()
  if (roles.value.includes("taibao_admin") || user.value.userId === 1) {
    getBranchOfficeTree()
  }
</script>
