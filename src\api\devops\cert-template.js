import request from "@/utils/request"

// 查询证书列表
export function listCert(query) {
  return request({
    url: "/devops/template/list",
    method: "get",
    params: query
  })
}

// 删除课程
export function delCert(certTemplateId) {
  return request({
    url: "/devops/template/" + certTemplateId,
    method: "delete"
  })
}

// 查询证书详细
export function getCert(certTemplateId) {
  return request({
    url: "/devops/template/" + certTemplateId,
    method: "get"
  })
}

// 新增证书
export function addCert(data) {
  return request({
    url: "/devops/template",
    method: "post",
    data: data
  })
}

// 修改证书
export function updateCert(data) {
  return request({
    url: "/devops/template",
    method: "put",
    data: data
  })
}

// 编辑证书位置
export function configCert(data) {
  return request({
    url: "/devops/config/addCertList",
    method: "post",
    data
  })
}

// 显示证书位置
export function getCertInfo(certTemplateId) {
  return request({
    url: "/devops/config/getConfigInfos/" + certTemplateId,
    method: "get"
  })
}
