<!--
 * @Description: 短视频管理
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-04-10 17:21:58
 * @LastEditTime: 2023-04-12 15:32:53
-->
<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
    >
      <el-form-item label="视频标题" prop="title">
        <el-input
          v-model="queryParams.title"
          placeholder="视频标题"
          clearable
          style="width: 240px"
          @keyup.enter="getList"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="getList"
          >搜索</el-button
        >
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb-8px">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleUpdate">
          新增
        </el-button>
      </el-col>
      <right-toolbar
        :search="false"
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="tableData">
      <el-table-column prop="cover" label="视频封面">
        <template #default="scope">
          <el-popover placement="right" :width="400" trigger="hover">
            <img :src="scope.row.cover" width="375" height="375" />
            <template #reference>
              <img
                :src="scope.row.cover"
                style="max-height: 60px; max-width: 60px"
              />
            </template>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column prop="title" label="视频标题"></el-table-column>
      <el-table-column prop="width" label="视频宽度"></el-table-column>
      <el-table-column prop="height" label="视频高度"> </el-table-column>
      <el-table-column
        label="操作"
        width="250"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-button
            link
            type="primary"
            icon="Download"
            @click="handleDownload(scope.row)"
            >视频下载</el-button
          >
          <el-button
            link
            type="primary"
            icon="Edit"
            @click="handleUpdate(scope.row)"
            >修改</el-button
          >
          <el-button
            link
            type="primary"
            icon="Delete"
            @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 弹窗, 新增 / 修改 -->
    <addEditDialog
      ref="addEditDialogRef"
      @refreshDataList="getList"
    ></addEditDialog>
  </div>
</template>

<script setup name="videoList">
import addEditDialog from "./addEditDialog.vue"
import { listVideo, delVideo } from "@/api/video/video.js"

const { proxy } = getCurrentInstance()
const { devops_integral_rule_type, devops_integral_cycle_type } = proxy.useDict(
  "devops_integral_rule_type",
  "devops_integral_cycle_type"
)

const tableData = ref([])
const loading = ref(false)
const showSearch = ref(true)
const total = ref(0)
const addEditDialogRef = ref()

const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10
  }
})

const { queryParams } = toRefs(data)

/** 查询目录列表 */
function getList() {
  loading.value = true
  listVideo(queryParams.value).then((response) => {
    tableData.value = response.rows || []
    total.value = response.total
    loading.value = false
  })
}
/** 修改按钮操作 */
function handleUpdate(row) {
  addEditDialogRef.value.openDialog(row?.vlogId)
}
/** 删除按钮操作 */
function handleDelete(row) {
  proxy.$modal
    .confirm(
      '是否确认删除类型为"' +
        proxy.selectDictLabel(devops_integral_rule_type.value, row.ruleType) +
        '"的数据项?'
    )
    .then(function () {
      return delVideo(row.vlogId)
    })
    .then(() => {
      getList()
      proxy.$modal.msgSuccess("删除成功")
    })
    .catch(() => {})
}

function resetQuery() {
  proxy.resetForm("queryRef")
  getList()
}

const handleDownload = (row) => {
  window.open(row.url, "_blank")
}

getList()
</script>
<style lang="scss" scoped></style>
