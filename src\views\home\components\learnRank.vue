<!--
 * @Description: 首页-学习排名
 * @Author: <PERSON><PERSON><PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-09-08 15:40:09
 * @LastEditTime: 2023-10-18 15:02:40
-->
<template>
  <div class="target-view">
    <div>
      <span class="title">学习排名</span>
      <div style="float: right">
        <ul class="timeSlot">
          <li
            v-for="item in dateList"
            @click="changeDate(item.value)"
            :class="activeVal === item.value ? 'active' : ''"
          >
            {{ item.label }}
          </li>
        </ul>
      </div>
    </div>
    <div class="view-con">
      <el-table
        :data="tableData"
        stripe
        height="270"
        style="width: 100%"
        :border="false"
      >
        <el-table-column label="排名" type="index" width="50" />
        <el-table-column label="姓名" prop="userName" align="center" />
        <el-table-column label="所在部门" prop="deptName" align="center" />
        <el-table-column label="学习时长" align="center">
          <template #default="scope">
            {{ formatSeconds(scope.row.lastDeltaDuration) }}
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script setup>
  import { formatSeconds } from "@/utils/common"
  import { getHoursRank } from "@/api/home/<USER>"

  const activeVal = ref("0")
  const dateList = ref([
    { label: "今日", value: "0" },
    { label: "近1个月", value: "1" },
    { label: "近1年", value: "2" }
  ])
  const changeDate = val => {
    activeVal.value = val
    getData()
  }

  const tableData = ref([])
  const getData = () => {
    getHoursRank({ sortRange: activeVal.value }).then(res => {
      tableData.value = res.rows.slice(0, 10)
    })
  }
  getData()
</script>

<style scoped lang="scss">
  .timeSlot {
    display: inline-block;
    .active {
      background: #867ffd;
      color: #fff;
    }
    > li {
      display: inline-block;
      width: 80px;
      line-height: 34px;
      background: #fff;
      border-radius: 5px;
      margin: 0 6px;
      text-align: center;
      font-size: 14px;
      cursor: pointer;
    }
  }
  .title {
    line-height: 40px;
    font-weight: bold;
    margin-left: 10px;
  }
  .view-con {
    background: #fff;
    padding: 14px 20px;
    border-radius: 10px;
    height: 298px;
  }
</style>
