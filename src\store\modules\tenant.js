/*
 * @Description: 租户相关store
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-08-16 14:43:00
 * @LastEditTime: 2023-12-25 15:59:31
 */
import { querySysTenantInfo } from "@/api/system/tenant"

const useTenantStore = defineStore("tenant", {
  state: () => ({
    tenantLogo: "",
    tenantBgImg: "",
    tenantName: "",
    domainName: ""
  }),
  actions: {
    async queryTenantInfo() {
      const { data } = await querySysTenantInfo()
      this.tenantLogo =
        data?.tenantIcon ||
        "https://beckwelldb.obs.cn-east-3.myhuaweicloud.com/logo1.png"
      this.tenantBgImg =
        data?.backgroundImage ||
        "https://training-voc.obs.cn-north-4.myhuaweicloud.com/background.jpg"
      this.tenantName = data?.name
      this.domainName = data?.domain
    }
  }
})

export default useTenantStore
