/*
 * @Description: permission
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-02-16 14:11:33
 * @LastEditTime: 2024-09-25 09:12:57
 */
import router from "./router"
import { ElMessage } from "element-plus"
import NProgress from "nprogress"
import "nprogress/nprogress.css"
import { getToken, setToken } from "@/utils/auth"
import { isHttp } from "@/utils/validate"
import { isRelogin } from "@/utils/request"
import useUserStore from "@/store/modules/user"
import useSettingsStore from "@/store/modules/settings"
import usePermissionStore from "@/store/modules/permission"
import useTenantStore from "@/store/modules/tenant"
import { getSubdomain } from "@/utils/common.js"
import { sysLogin } from "@/api/login"
import { productionEnvList } from "@/utils/constant"

NProgress.configure({ showSpinner: false })

let isAlreadyAutoLogin = false
const whiteList = ["/login", "/register"]
const env = import.meta.env.VITE_APP_ENV

router.beforeEach(async (to, from, next) => {
  NProgress.start()
  const tenantStore = useTenantStore()
  const userStore = useUserStore()
  const subDomain = getSubdomain(false)
  // 获取传入参数 针对其他页面跳转
  const urlParams = new URLSearchParams(window.location.search)
  // 从其他页面跳进来
  if (urlParams.get("isfrom") && !isAlreadyAutoLogin) {
    const parseQuery = Object.fromEntries(urlParams)
    try {
      const res = await sysLogin(parseQuery)
      setToken(res.data.access_token)
      isAlreadyAutoLogin = true
      await userStore.getInfo()
      const accessRoutes = await usePermissionStore().generateRoutes()
      accessRoutes.forEach(route => {
        if (!isHttp(route.path)) {
          router.addRoute(route)
        }
      })
      next(urlParams.get("path"))
    } catch (err) {
      await userStore.logOut()
      ElMessage.error(err)
      next("/login")
    }
  }

  // 租户进入
  if (subDomain && !tenantStore.tenantLogo) {
    tenantStore.queryTenantInfo()
  }
  // 从用户端跳转至管理端来登录
  if (
    to.path === "/login" &&
    window.location.href.indexOf("redirect=http") >= 0
  ) {
    next()
  }

  // 用户端携带token跳转至管理端逻辑
  else if (window.location.href.indexOf("token") >= 0) {
    // 如果token存在
    // 如果url中包含token   split？分割成数组，取第二个
    let queryString = window.location.href.split("?")[1]
    let urlNoParams = window.location.href.split("?")[0]
    // 创建URLSearchParams对象
    let urlParams = new URLSearchParams(queryString)
    let token = urlParams.get("token")
    localStorage.setItem("token", token)
    setToken(token)
    if (!productionEnvList.includes(env)) {
      let testTenant = urlParams.get("testTenant")
      if (testTenant) {
        localStorage.setItem("testTenant", testTenant)
      }
    }
    // 重定向地址去除url当中一大长串的token
    window.location.href = urlNoParams
    next()
  }
  // 页面跳转/刷新 判断是否有当前登录人信息逻辑；没有则调用getInfo()
  else if (getToken()) {
    to.meta.title && useSettingsStore().setTitle(to.meta.title)
    /* has token*/
    if (to.path === "/login") {
      next({ path: "/" })
      NProgress.done()
    } else {
      if (userStore.roles.length === 0) {
        try {
          isRelogin.show = true
          await userStore.getInfo()
          // 判断当前用户是否已拉取完user_info信息
          isRelogin.show = false
          const accessRoutes = await usePermissionStore().generateRoutes()
          // 根据roles权限生成可访问的路由表
          accessRoutes.forEach(route => {
            if (!isHttp(route.path)) {
              router.addRoute(route) // 动态添加可访问路由表
            }
          })
          next({ ...to, replace: true }) // hack方法 确保addRoutes已完成
        } catch (err) {
          await userStore.logOut()
          ElMessage.error(err)
          next({ path: "/" })
        }
      } else {
        next()
      }
    }
  }
  // 无token逻辑
  else {
    if (whiteList.indexOf(to.path) !== -1) {
      // 在免登录白名单，直接进入
      next()
    } else {
      next(`/login?redirect=${to.fullPath}`) // 否则全部重定向到登录页
      NProgress.done()
    }
  }
})

router.afterEach(() => {
  NProgress.done()
})
