/*
 * @Description: 常量枚举与map映射关系
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-05-08 11:27:20
 * @LastEditTime: 2024-11-12 14:03:15
 */

export const FILE_TYPE = {
  1: [
    "doc",
    "docx",
    "xls",
    "xlsx",
    "ppt",
    "pptx",
    "txt",
    "pdf",
    "jpg",
    "png",
    "gif"
  ], // 文档格式
  2: ["wmv", "mp4", "flv", "avi", "rmvb", "mpg"], // 视频格式
  3: ["mp3", "wav"], // 音频格式
  4: ["zip", "rar", "7z", "gz"] // 压缩格式
}

export const unepTenantList = ["unep", "yingji", "eduxd", "jinwaitan"]

export const extraTenantFieldList = ["hookTimer"]

// 生产环境列表
export const productionEnvList = ["production", "hwproduction", "xdproduction"]

// 审核角色权限列表
export const reviewRoleList = [
  "course_first_review_admin",
  "course_second_review_admin",
  "course_final_review_admin"
]

// 课程类型
export const COURSE_TYPE = {
  VIDEO_COURSE: "S", // 视频课程
  WORD_COURSE: "W", // 文档课程
  "2D_COURSE": "2", // 2D课程
  "3D_COURSE": "3", // 3D课程
  RECORED_COURSE: "4", // 录播课程
  XJ_COURSE: "X" // 宣教课程
}
// 课程类型对应的文件类型
export const FILE_COURSE_TYPE_MAP = {
  [COURSE_TYPE.VIDEO_COURSE]: [
    "wmv",
    "mp4",
    "flv",
    "avi",
    "rmvb",
    "mpg",
    "m4v"
  ],
  [COURSE_TYPE.WORD_COURSE]: ["doc", "docx", "pdf", "ppt", "pptx"]
}
