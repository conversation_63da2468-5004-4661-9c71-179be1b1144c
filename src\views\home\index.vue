<!--
 * @Description: 首页
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-03-03 17:06:07
 * @LastEditTime: 2024-05-07 11:37:11
-->
<template>
  <div>
    <UnepIndex v-if="unepTenantList.includes(domainName)" />
    <TownIndex v-else-if="domainName === 'town'" />
    <CommonIndex v-else />
  </div>
</template>

<script setup>
  import CommonIndex from "@/views/home/<USER>"
  import UnepIndex from "@/views/home/<USER>"
  import TownIndex from "@/views/home/<USER>"
  import useUserStore from "@/store/modules/user"
  import useTenantStore from "@/store/modules/tenant"
  import { unepTenantList } from "@/utils/constant"

  const { proxy } = getCurrentInstance()
  const tenantStore = useTenantStore()
  const userStore = useUserStore()
  const { domainName } = storeToRefs(tenantStore)
  // 是否包含审核角色
  const isReviewRoleCom = computed(() => {
    return userStore.isReviewRole
  })

  onMounted(() => {
    if (isReviewRoleCom.value) {
      proxy.$tab.closeOpenPage({
        path: "/onlineCourse/courseReview"
      })
    }
  })
</script>

<style lang="scss" scoped></style>
