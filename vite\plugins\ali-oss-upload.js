/*
 * @Description: 阿里云OSS打包后自动上传
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-06-19 14:52:35
 * @LastEditTime: 2023-06-19 15:02:00
 */
import viteAliOssPlugin from "vite-ali-oss-plugin"

export default function createAliOssUpload() {
  return viteAliOssPlugin({
    region: "oss-cn-shanghai.aliyuncs.com", //oss地区
    accessKeyId: "LTAI5tPTtcYFxr8iaJiA4ZA7", //你的osskeyid值
    accessKeySecret: "******************************", //你的ossKeySecret值
    bucket: "beckwell-training-web", //创建的oss存储仓库名称
    overwrite: true, // 是否删除原文件
    endpoint: "oss-cn-shanghai.aliyuncs.com"
  })
}
