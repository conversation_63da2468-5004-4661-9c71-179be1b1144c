<!--
 * @Description: 课程管理-标签管理
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-08-25 10:38:56
 * @LastEditTime: 2024-08-02 09:28:02
-->
<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
    >
      <el-form-item label="标签名称" prop="dictLabel">
        <el-input
          v-model="queryParams.dictLabel"
          placeholder="请输入标签名称"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb-8px">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd"
          >新增</el-button
        >
      </el-col>
      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="dataList">
      <el-table-column label="序号" type="index" width="70" />
      <el-table-column label="标签名称" prop="dictLabel">
        <template #default="scope">
          <span
            v-if="scope.row.listClass == '' || scope.row.listClass == 'default'"
            >{{ scope.row.dictLabel }}</span
          >
          <el-tag
            v-else
            :type="scope.row.listClass == 'primary' ? '' : scope.row.listClass"
            >{{ scope.row.dictLabel }}</el-tag
          >
        </template>
      </el-table-column>
      <el-table-column label="标签键值" prop="dictValue" />
      <el-table-column label="排序" prop="dictSort" />
      <el-table-column label="状态" prop="status">
        <template #default="scope">
          <dict-tag :options="sys_normal_disable" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="备注" prop="remark" />
      <el-table-column label="创建时间" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        width="160"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-button
            link
            type="primary"
            icon="Edit"
            @click="handleUpdate(scope.row)"
            >修改</el-button
          >
          <el-button
            link
            type="primary"
            icon="Delete"
            @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改参数配置对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="dataRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="标签类型">
          <el-input v-model="form.dictType" :disabled="true" />
        </el-form-item>
        <el-form-item label="标签名称" prop="dictLabel">
          <el-input
            maxlength="10"
            v-model="form.dictLabel"
            placeholder="请输入标签名称"
          />
        </el-form-item>
        <el-form-item label="标签键值" prop="dictValue">
          <el-input v-model="form.dictValue" placeholder="请输入标签键值" />
        </el-form-item>
        <!-- <el-form-item label="样式属性" prop="cssClass">
          <el-input v-model="form.cssClass" placeholder="请输入样式属性" />
        </el-form-item> -->
        <el-form-item label="显示排序" prop="dictSort">
          <el-input-number
            v-model="form.dictSort"
            controls-position="right"
            :min="0"
          />
        </el-form-item>
        <el-form-item label="回显样式" prop="listClass">
          <el-select v-model="form.listClass">
            <el-option
              v-for="item in listClassOptions"
              :key="item.value"
              :label="item.label + '(' + item.value + ')'"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="dict in sys_normal_disable"
              :key="dict.value"
              :label="dict.value"
              >{{ dict.label }}</el-radio
            >
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            show-word-limit
            maxlength="50"
            v-model="form.remark"
            type="textarea"
            placeholder="请输入内容"
          ></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="tagManage">
  import useDictStore from "@/store/modules/dict"
  import {
    listData,
    getData,
    delData,
    addData,
    updateData
  } from "@/api/system/dict/data"

  const { proxy } = getCurrentInstance()
  const { sys_normal_disable } = proxy.useDict("sys_normal_disable")

  const dataList = ref([])
  const open = ref(false)
  const loading = ref(true)
  const showSearch = ref(true)
  const ids = ref([])
  const total = ref(0)
  const title = ref("")
  // 标签名称回显样式
  const listClassOptions = ref([
    { value: "primary", label: "主要" },
    { value: "success", label: "成功" },
    { value: "info", label: "信息" },
    { value: "warning", label: "警告" },
    { value: "danger", label: "危险" }
  ])

  const data = reactive({
    form: {},
    queryParams: {
      pageNum: 1,
      pageSize: 10,
      dictType: "course_label_type"
    },
    rules: {
      dictLabel: [
        { required: true, message: "标签名称不能为空", trigger: "blur" }
      ],
      dictValue: [
        { required: true, message: "标签键值不能为空", trigger: "blur" }
      ],
      dictSort: [
        { required: true, message: "标签顺序不能为空", trigger: "blur" }
      ]
    }
  })

  const { queryParams, form, rules } = toRefs(data)

  /** 查询标签列表 */
  function getList() {
    loading.value = true
    listData(queryParams.value).then(response => {
      dataList.value = response.rows
      total.value = response.total
      loading.value = false
    })
  }
  /** 取消按钮 */
  function cancel() {
    open.value = false
    reset()
  }
  /** 表单重置 */
  function reset() {
    form.value = {
      dictCode: undefined,
      dictLabel: undefined,
      dictValue: undefined,
      // cssClass: undefined,
      listClass: "default",
      dictSort: 0,
      status: "0",
      remark: undefined
    }
    proxy.resetForm("dataRef")
  }
  /** 搜索按钮操作 */
  function handleQuery() {
    queryParams.value.pageNum = 1
    getList()
  }
  /** 重置按钮操作 */
  function resetQuery() {
    proxy.resetForm("queryRef")
    handleQuery()
  }
  /** 新增按钮操作 */
  function handleAdd() {
    reset()
    open.value = true
    title.value = "添加标签数据"
    form.value.dictType = queryParams.value.dictType
  }
  /** 修改按钮操作 */
  function handleUpdate(row) {
    reset()
    const dictCode = row.dictCode || ids.value
    getData(dictCode).then(response => {
      form.value = response.data
      open.value = true
      title.value = "修改标签数据"
    })
  }
  /** 提交按钮 */
  function submitForm() {
    proxy.$refs["dataRef"].validate(valid => {
      if (valid) {
        if (form.value.dictCode != undefined) {
          updateData(form.value).then(response => {
            useDictStore().removeDict(queryParams.value.dictType)
            proxy.$modal.msgSuccess("修改成功")
            open.value = false
            getList()
          })
        } else {
          addData(form.value).then(response => {
            useDictStore().removeDict(queryParams.value.dictType)
            proxy.$modal.msgSuccess("新增成功")
            open.value = false
            getList()
          })
        }
      }
    })
  }
  /** 删除按钮操作 */
  function handleDelete(row) {
    const dictCodes = row.dictCode || ids.value
    proxy.$modal
      .confirm('是否确认删除标签编码为"' + dictCodes + '"的数据项？')
      .then(function () {
        return delData(dictCodes)
      })
      .then(() => {
        getList()
        proxy.$modal.msgSuccess("删除成功")
        useDictStore().removeDict(queryParams.value.dictType)
      })
      .catch(() => {})
  }

  getList()
</script>
