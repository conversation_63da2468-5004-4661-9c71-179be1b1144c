<!--
 * @Description: 证书坐标配置
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-06-12 16:07:45
 * @LastEditTime: 2023-07-11 11:55:35
-->
<template>
  <div class="app-container certificate-container">
    <div class="title">
      <el-row>
        <el-col :span="20"> 将配置信息拖入证书相应位置： </el-col>
        <el-col :span="4">
          <el-button type="warning" @click="handleClose">关闭</el-button>
          <el-button type="primary" @click="handleSubmit">提交</el-button>
        </el-col>
      </el-row>
    </div>
    <div id="pos-con">
      <div id="config-con">
        <ul id="ope-con">
          <li
            v-for="(item, index) in opeList"
            :key="index"
            :style="{ left: index * 120 + 20 + 'px' }"
            :infoId="item.id"
          >
            {{ item.name }}
          </li>
        </ul>
        <div id="edit-con">
          <input
            v-show="isEdit"
            type="text"
            id="custom-input"
            v-model="customText"
            placeholder="自定义属性"
            @blur="changeEdit"
            maxlength="20"
            ref="customRef"
          />
          <ul id="custom-con">
            <li
              v-show="!isEdit"
              id="custom-dom"
              :style="{
                color: customText ? '#606266' : '#F56C6C'
              }"
            >
              <span @click.self="changeEdit">{{
                customText ? customText : "点击编辑"
              }}</span>
            </li>
          </ul>
        </div>

        <div id="cer-con">
          <img :src="templateImg" alt="" />
          <div id="verticalLine"></div>
          <div id="horizontalLine"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup name="certificateConfig">
  import { configCert, getCertInfo } from "@/api/devops/cert-template.js"
  import { nextTick, onMounted } from "vue"

  const route = useRoute()
  const { certTemplateId, templateImg } = route.query
  // 存在问题，暂时搁置
  // const opeList = ref([
  //   { name: "姓名", id: 1 },
  //   { name: "性别", id: 2 }
  // ])

  const { proxy } = getCurrentInstance()

  const isEdit = ref(false)
  const customText = ref()
  const customRef = ref()
  const ope = ref()
  const con = ref()
  const vLine = ref()
  const hLine = ref()
  const outList = ref([])
  const inList = ref([])
  const opeListDom = ref([])
  const customCon = ref()

  // 自定义边框移动进入图片
  const customInList = ref([])
  onMounted(() => {
    ope.value = document.getElementById("ope-con")
    opeListDom.value = Array.from(ope.value.children)
    con.value = document.getElementById("config-con")
    vLine.value = document.getElementById("verticalLine")
    hLine.value = document.getElementById("horizontalLine")
    customCon.value = document.getElementById("custom-con")
    certInfo()
    /* staticDrag() */
    customDrag()
  })
  const certInfo = () => {
    getCertInfo(certTemplateId).then(res => {
      if (res.code === 200) {
        let configData = res.data
        // 先判断是否姓名性别在里面

        outList.value = [...opeListDom.value]
        inList.value = []
        for (let item of configData) {
          /* if (item.attribute === "姓名") {
          outList.value = outList.value.filter(item => {
            return item.innerText !== "姓名"
          })
          inList.value.push(opeListDom.value[0])
          opeListDom.value[0].style.left = item.latitude + "px"
          opeListDom.value[0].style.top = item.longitude + "px"
        } else if (item.attribute === "性别") {
          outList.value = outList.value.filter(item => {
            return item.innerText !== "性别"
          })
          inList.value.push(opeListDom.value[1])
          opeListDom.value[1].style.left = item.latitude + "px"
          opeListDom.value[1].style.top = item.longitude + "px"
        } else { */
          let addDom = document.createElement("li")
          addDom.setAttribute("class", "add-dom")
          addDom.style.left = item.latitude + "px"
          addDom.style.top = item.longitude + "px"
          addDom.innerText = item.attribute
          customCon.value.appendChild(addDom)
          customInList.value.push(addDom)
          /*  } */
        }
        /*   outList.value.forEach((item, index) => {
        item.style.top = "14px"
        item.style.left = index * 120 + 20 + "px"
      }) */
      }
    })
  }
  const staticDrag = () => {
    ope.value.onmousedown = function (ev) {
      if (ev.target.tagName !== "LI") return
      ev.preventDefault()
      let box = ev.target
      // 当前容器左侧父节点间距
      let oleft = getLeft(box, box.offsetLeft)
      let otop = getTop(box, box.offsetTop)
      // 当前容器父容器左侧距离
      let opleft = getLeft(box.parentNode, box.parentNode.offsetLeft)
      let optop = getTop(box.parentNode, box.parentNode.offsetTop)
      // 鼠标位置错位
      let disX = ev.clientX - oleft
      let disY = ev.clientY - otop
      let xarr = []
      let yarr = []
      let allList = [...inList.value, ...customInList.value]
      allList.forEach(item => {
        let left = Number(item.style.left.replace("px", ""))
        let top = Number(item.style.top.replace("px", ""))
        xarr.push(left)
        xarr.push(left + 78)
        yarr.push(top)
        yarr.push(top + 28)
      })
      document.onmousemove = function (ev) {
        ev.preventDefault()
        box.style.borderColor = "#F56C6C"
        // 计算当前容器内的位置
        let x = ev.clientX - disX - opleft
        let y = ev.clientY - disY - optop
        // 12为两个边框距离
        x = x <= 0 ? 0 : x
        x =
          x >= con.value.offsetWidth - box.offsetWidth
            ? con.value.offsetWidth - box.offsetWidth - 12
            : x
        y = y <= 0 ? 0 : y
        y =
          y >= con.value.offsetHeight - box.offsetHeight
            ? con.value.offsetHeight - box.offsetHeight - 12
            : y

        let isV = false
        let isH = false
        for (let item of xarr) {
          if (Math.abs(item - x) < 10) {
            vLine.value.style.left = item + "px"
            vLine.value.style.opacity = "1"
            isV = true
            break
          }
        }
        for (let item of yarr) {
          if (Math.abs(item - y) < 10) {
            hLine.value.style.top = item + "px"
            hLine.value.style.opacity = "1"
            isH = true
            break
          }
        }
        if (!isV) {
          vLine.value.style.opacity = "0"
        }
        if (!isH) {
          hLine.value.style.opacity = "0"
        }

        box.style.left = x + "px"
        box.style.top = y + "px"
      }

      document.onmouseup = function (ev) {
        ev.preventDefault()
        document.onmousemove = null
        document.onmouseup = null
        // 拖拽完成恢复颜色
        box.style.borderColor = "#409eff"
        // 重置拖拽栏
        outList.value = []
        inList.value = []
        opeListDom.value.forEach(item => {
          let top = item.style.top
          if (top && Number(top.replace("px", "")) > 60) {
            inList.value.push(item)
          } else {
            outList.value.push(item)
          }
        })
        outList.value.forEach((item, index) => {
          item.style.top = "14px"
          item.style.left = index * 120 + 20 + "px"
        })
        vLine.value.style.opacity = "0"
        hLine.value.style.opacity = "0"
      }
    }
  }

  const customDrag = () => {
    customCon.value.onmousedown = function (ev) {
      if (ev.target.tagName !== "LI") return
      ev.preventDefault()
      let top = Number(
        window.getComputedStyle(ev.target, null)["top"].replace("px", "")
      )
      // 判断是否需要新增元素
      let box
      if (top > 60) {
        box = ev.target
      } else {
        let addDom = document.createElement("li")
        addDom.setAttribute("class", "add-dom")
        let left = Number(
          window.getComputedStyle(ev.target, null)["left"].replace("px", "")
        )

        addDom.style.left = left + "px"
        addDom.style.top = top + "px"
        addDom.innerText = customText.value ? customText.value : ""

        customCon.value.appendChild(addDom)
        box = addDom
      }

      // 当前容器左侧父节点间距
      let oleft = getLeft(box, box.offsetLeft)
      let otop = getTop(box, box.offsetTop)
      // 当前容器父容器左侧距离
      let opleft = getLeft(box.parentNode, box.parentNode.offsetLeft)
      let optop = getTop(box.parentNode, box.parentNode.offsetTop)
      // 鼠标位置错位
      let disX = ev.clientX - oleft
      let disY = ev.clientY - otop
      let xarr = []
      let yarr = []
      let allList = [...inList.value, ...customInList.value]
      allList.forEach(item => {
        let left = Number(item.style.left.replace("px", ""))
        let top = Number(item.style.top.replace("px", ""))
        xarr.push(left)
        xarr.push(left + 78)
        yarr.push(top)
        yarr.push(top + 28)
      })
      document.onmousemove = function (ev) {
        ev.preventDefault()
        box.style.borderColor = "#F56C6C"
        // 计算当前容器内的位置
        let x = ev.clientX - disX - opleft
        let y = ev.clientY - disY - optop
        // 12为两个边框距离
        x = x <= 0 ? 0 : x
        x =
          x >= con.value.offsetWidth - box.offsetWidth
            ? con.value.offsetWidth - box.offsetWidth - 12
            : x
        y = y <= 0 ? 0 : y
        y =
          y >= con.value.offsetHeight - box.offsetHeight
            ? con.value.offsetHeight - box.offsetHeight - 12
            : y

        let isV = false
        let isH = false
        for (let item of xarr) {
          if (Math.abs(item - x) < 10) {
            vLine.value.style.left = item + "px"
            vLine.value.style.opacity = "1"
            isV = true
          }
        }
        for (let item of yarr) {
          if (Math.abs(item - y) < 10) {
            hLine.value.style.top = item + "px"
            hLine.value.style.opacity = "1"
            isH = true
          }
        }
        if (!isV) {
          vLine.value.style.opacity = "0"
        }
        if (!isH) {
          hLine.value.style.opacity = "0"
        }
        box.style.left = x + "px"
        box.style.top = y + "px"
      }

      document.onmouseup = function (ev) {
        ev.preventDefault()
        document.onmousemove = null
        document.onmouseup = null

        if (box.style.top.replace("px", "") < 60) {
          box.remove()
        }
        customInList.value = []
        Array.from(customCon.value.children).forEach(item => {
          let top = item.style.top
          if (top && Number(top.replace("px", "")) > 60) {
            customInList.value.push(item)
          }
        })
        customCon.value.onmousedown = null
        box.style.borderColor = "#409eff"
        vLine.value.style.opacity = "0"
        hLine.value.style.opacity = "0"
        // 会引起位置偏移 ?待研究
        /*  customText.value = "" */
        customDrag()
      }
    }
  }
  const getLeft = (dom, oleft) => {
    if (dom.parentNode && dom.parentNode.tagName !== "BODY") {
      oleft = oleft + dom.parentNode.offsetLeft
      return getLeft(dom.parentNode, oleft)
    }
    return oleft
  }
  const getTop = (dom, otop) => {
    if (dom.parentNode && dom.parentNode.tagName !== "BODY") {
      otop = otop + dom.parentNode.offsetTop
      return getTop(dom.parentNode, otop)
    }
    return otop
  }
  const handleSubmit = () => {
    let allList = [...inList.value, ...customInList.value]
    let configArr = []
    allList.forEach(item => {
      configArr.push({
        attribute: item.innerText,
        latitude: item.style.left.replace("px", ""),
        longitude: item.style.top.replace("px", ""),
        certTemplateId
      })
    })
    configCert(configArr).then(res => {
      if (res.code === 200) {
        proxy.$modal.msgSuccess("提交成功")
        proxy.$tab.closeOpenPage({
          path: "/operationManage/certificates/certificateList"
        })
      }
    })
  }

  const changeEdit = () => {
    isEdit.value = !isEdit.value
    if (isEdit.value) {
      // 编辑聚焦
      nextTick(() => {
        customRef.value.focus()
      })
    }
  }

  const handleClose = () => {
    proxy.$tab.closeOpenPage({
      path: "/operationManage/certificates/certificateList"
    })
  }
</script>
<style lang="scss">
  .certificate-container {
    user-select: none;
    .add-dom {
      position: absolute;
      width: 80px;
      cursor: move;
      line-height: 24px;
      height: 32px;
      list-style: none;
      text-align: center;

      border: 4px dashed #409eff;
      font-size: 12px;
      color: #606266;
      font-weight: bold;
      overflow: hidden;
      text-overflow: ellipsis;
      z-index: 100;
    }
    #custom-input {
      position: absolute;
      left: 20px;
      top: 14px;
      line-height: 26px;
      width: 90px;
      text-indent: 4px;
      outline: none;
      border: 2px solid #606266;
      display: block;
    }
    #custom-con {
      margin: 0;
      padding: 0;
    }
    #custom-dom {
      position: absolute;
      white-space: nowrap;
      left: 20px;
      top: 14px;
      width: 80px;
      padding: 4px;
      background: #f2f3f5;
      font-weight: bold;
      cursor: move;
      color: #606266;
      border: 4px dashed #67c23a;

      font-size: 12px;
      text-align: center;
      overflow: hidden;
      text-overflow: ellipsis;
      > span {
        cursor: pointer;
        min-width: 60px;
        display: block;
        overflow: hidden;
        text-overflow: ellipsis;
        width: 100%;
        line-height: 16px;
      }
    }

    .title {
      line-height: 32px;
      font-size: 14px;
    }
    #config-con {
      width: 100%;
      position: relative;
      top: 10px;
      left: 0;
      border: 6px solid #f56c6c;
      overflow: hidden;
    }
    #pos-con {
      margin: auto;
      width: 980px;
    }
    #ope-con {
      display: block;
      height: 60px;
      margin: 0px;
      padding: 0;
      background: #f2f3f5;
      border-bottom: 6px solid #f56c6c;
      > li {
        width: 80px;
        cursor: move;
        line-height: 24px;
        list-style: none;
        text-align: center;
        position: absolute;
        z-index: 10;
        border: 4px dashed #409eff;
        font-size: 12px;
        color: #606266;
        top: 14px;
        font-weight: bold;
      }
    }
    #cer-con {
      text-align: center;
      > img {
        width: 100%;
        display: block;
      }
    }

    #verticalLine {
      border-left: 2px dashed #f56c6c;
      width: 0px;
      position: absolute;
      height: 100%;
      left: 100px;
      z-index: 20;
      top: 60px;
      opacity: 0;
    }

    #horizontalLine {
      border-top: 2px dashed #f56c6c;
      width: 100%;
      position: absolute;
      height: 0;
      left: 0px;
      z-index: 20;
      top: 0px;
      opacity: 0;
    }
  }
</style>
