/*
 * @Description: 套餐实施套餐相关API
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-08-25 16:43:22
 * @LastEditTime: 2023-08-28 15:00:39
 */
import request from "@/utils/request"

// 查询套餐实施列表
export function listPackageImp(query) {
  return request({
    url: "/system/implement/list",
    method: "get",
    params: query
  })
}

// 查询套餐实施详细
export function getPackageImp(courseId) {
  return request({
    url: "/system/implement/" + courseId,
    method: "get"
  })
}

// 新增/修改套餐实施
export function addEditPackageImp(data) {
  return request({
    url: "/system/implement/saveOrUpdate",
    method: "post",
    data: data
  })
}

// 删除套餐实施
export function delPackageImp(courseId) {
  return request({
    url: "/system/implement/" + courseId,
    method: "delete"
  })
}
