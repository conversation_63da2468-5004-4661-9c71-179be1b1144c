<!--
 * @Description: 报表统计-考试报表统计
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-08-09 13:53:51
 * @LastEditTime: 2023-08-10 16:15:46
-->
<template>
  <section class="index-container">
    <component
      :is="currentView"
      :row="row"
      @updateCurrentView="updateCurrentView"
    ></component>
  </section>
</template>

<script setup name="examReportIndex">
  import examReport from "./examReport.vue"
  import studentExamDetails from "./studentExamDetails.vue"
  import examPaperDetails from "./examPaperDetails.vue"

  const row = ref({})
  const currentView = shallowRef(examReport)

  const updateCurrentView = (view, item) => {
    row.value = item
    if (view === "studentExamDetails") {
      currentView.value = studentExamDetails
    } else if (view === "examReport") {
      currentView.value = examReport
    } else if (view === "examPaperDetails") {
      currentView.value = examPaperDetails
    }
  }
</script>
