/*
 * @Description:
 * @Author: <PERSON><PERSON><PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-09-11 13:30:17
 * @LastEditTime: 2023-10-18 16:03:17
 */
import request from "@/utils/request"

// 任务列表
export function taskList(params) {
  return request({
    url: "/course/task/list",
    method: "get",
    params
  })
}

// 今日数据一览
// queryTimeFrom  queryTimeTo
export function getTaskDashboard(params) {
  return request({
    url: "/course/task/dashboard",
    method: "get",
    params
  })
}

// 学习情况分布
// sortRange		0-近一个月  1-近六个月  2-近一年（字符类型）
// deptId          部门ID,取系统管理-部门管理的列
export function learnDistribution(params) {
  return request({
    url: "/course/study-log/learnDistribution",
    method: "get",
    params
  })
}

// 学习排名
// sortRange		0-今日  1-近一个月  2-近一年（字符类型）
export function getHoursRank(params) {
  return request({
    url: "/course/study-log/getHoursRank",
    method: "get",
    params
  })
}
