<!--
 * @Description: 新增试题页面
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-02-27 16:44:46
 * @LastEditTime: 2023-05-31 09:57:19
-->
<template>
  <div class="app-container">
    <el-form :model="form" ref="formRef" label-width="100px" :rules="rules">
      <el-form-item label="试题类型" prop="questionType">
        <el-select
          v-model="form.questionType"
          clearable
          placeholder="试题类型"
          @change="questionTypeChange"
        >
          <el-option
            v-for="dict in questionnaire_question_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="题干" prop="questionName">
        <el-input
          style="width: 500px"
          v-model="rawForm.questionName"
          @focus="inputClick(form.questionName, 'questionName')"
        />
      </el-form-item>

      <!-- 单选题或多选题 -->
      <template v-if="form.questionType === 'S' || form.questionType === 'M'">
        <el-form-item label="选项" porp="item1">
          <el-form-item
            v-for="(item, index) in items"
            :label="item.prefix"
            :key="item.prefix"
            label-width="50px"
            class="question-item-label"
          >
            <el-input
              v-model="item.prefix"
              style="width: 50px; display: inline-block"
            />
            <el-input
              v-model="rawForm[`item${index + 1}`]"
              @focus="inputClick(form[`item${index + 1}`], `item${index + 1}`)"
              class="question-item-content-input"
            />
            <el-button
              v-if="index !== 0"
              type="danger"
              class="question-item-remove"
              @click="questionItemRemove(index)"
              >删除</el-button
            >
          </el-form-item>
          <el-button
            type="primary"
            class="question-item-remove"
            @click="questionItemAdd()"
            >添加选项</el-button
          >
        </el-form-item>
      </template>
      <!-- 判断题 -->
      <template v-else-if="form.questionType === 'J'">
        <el-form-item label="选项" prop="item1">
          <el-form-item
            :label="item.prefix"
            :key="item.prefix"
            v-for="(item, index) in items"
            label-width="50px"
            class="question-item-label"
          >
            <el-input v-model="item.prefix" style="width: 50px" disabled />
            <el-input
              v-model="rawForm[`item${index + 1}`]"
              @focus="inputClick(form[`item${index + 1}`], `item${index + 1}`)"
              class="question-item-content-input"
              disabled
            />
          </el-form-item>
        </el-form-item>
      </template>

      <el-form-item label="解析" prop="analysis">
        <el-input
          v-model="rawForm.analysis"
          @focus="inputClick(form.analysis, 'analysis')"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="submitForm">提交</el-button>
        <el-button @click="reset">重置</el-button>
      </el-form-item>
    </el-form>

    <richEditorDialog ref="richEditorDialogRef" @ok="handleRichEditorContent" />
  </div>
</template>

<script setup name="QuestionAdd">
  import {
    addQuestion,
    getQuestion,
    updateQuestion
  } from "@/api/trainingImplement/questionnaire.js"
  import richEditorDialog from "./components/richEditorDialog"

  const { proxy } = getCurrentInstance()
  const { questionnaire_question_type } = proxy.useDict(
    "questionnaire_question_type"
  )
  const route = useRoute()

  const items = ref([])

  const data = reactive({
    form: {},
    rawForm: {},
    rules: {
      questionType: [
        { required: true, message: "试题类型不能为空", trigger: "change" }
      ],
      questionName: [
        { required: true, message: "题干不能为空", trigger: "change" }
      ],
      item1: [{ required: true, message: "选项不能为空", trigger: "change" }],
      analysis: [{ required: true, message: "分析不能为空", trigger: "change" }]
    }
  })
  const { form, rules, rawForm } = toRefs(data)

  const flag = ref("add")
  if (Object.keys(route.query).length !== 0) {
    getQuestion(route.query.traningQuestionId).then(res => {
      form.value = res.data
      rawForm.value = res.data

      if (
        form.value?.questionType === "M" ||
        form.value?.questionType === "S"
      ) {
        for (let i = 1; i < 11; i++) {
          if (form.value[`item${i}`]) {
            // 获取从A开始的英文字母
            const letter = String.fromCharCode(64 + i)
            items.value.push({ prefix: letter, content: "" })
          }
        }
      } else if (form.value?.questionType === "J") {
        items.value = [
          { id: null, prefix: "A", content: "是" },
          { id: null, prefix: "B", content: "否" }
        ]
        form.value.item1 = "是"
        rawForm.value.item1 = "是"
        form.value.item2 = "否"
        rawForm.value.item2 = "否"
      }
      flag.value = "edit"
    })
  }

  const inputClick = (valueHtml, parameterKey) => {
    proxy.$refs["richEditorDialogRef"].show(
      valueHtml,
      parameterKey,
      form.value.questionType
    )
  }

  const handleRichEditorContent = (htmlValue, key, rawValue) => {
    form.value[key] = htmlValue
    rawForm.value[key] = rawValue
  }

  const questionTypeChange = type => {
    resetRichEditor()
    items.value = []
    if (type === "S" || type === "M") {
      items.value = [
        { prefix: "A", content: "" },
        { prefix: "B", content: "" },
        { prefix: "C", content: "" },
        { prefix: "D", content: "" }
      ]
    } else if (type === "J") {
      items.value = [
        { id: null, prefix: "A", content: "是" },
        { id: null, prefix: "B", content: "否" }
      ]
      form.value.item1 = "是"
      rawForm.value.item1 = "是"
      form.value.item2 = "否"
      rawForm.value.item2 = "否"
    } else if (type === "F") {
      items.value = []
    }
  }

  const resetRichEditor = () => {
    rawForm.value = {}
    for (let i = 1; i < 11; i++) {
      form.value[`item${i}`] = undefined
    }
    form.value.questionName = undefined
    form.value.analysis = undefined
  }

  const questionItemRemove = index => {
    items.value.splice(index, 1)
  }
  const questionItemAdd = () => {
    if (items.value.length >= 10) {
      proxy.$modal.msgError("最多添加10个选项")
      return
    }
    items.value.push({ prefix: getNextLetter(), content: "" })
  }

  const getNextLetter = () => {
    if (items.value.length === 0) return
    const prevLetter = items.value[items.value.length - 1].prefix
    if (!prevLetter) return
    return String.fromCharCode(prevLetter.charCodeAt(0) + 1)
  }

  const submitForm = () => {
    proxy.$refs["formRef"].validate(valid => {
      if (valid) {
        if (flag.value === "add") {
          addQuestion(form.value).then(res => {
            if (res.code === 200) {
              reset()
              proxy.$modal.msgSuccess("新增成功")
              proxy.$tab.closeOpenPage({
                path: "/trainingImplement/questionnaire/questionnaireQuestion/questionnaireQuestionList"
              })
            }
          })
        } else {
          updateQuestion(form.value).then(res => {
            if (res.code === 200) {
              reset()
              proxy.$modal.msgSuccess("修改成功")
              proxy.$tab.closeOpenPage({
                path: "/trainingImplement/questionnaire/questionnaireQuestion/questionnaireQuestionList"
              })
            }
          })
        }
      }
    })
  }

  /** 表单重置 */
  function reset() {
    form.value = {}
    rawForm.value = {}
  }
</script>
<style lang="scss" scoped>
  :deep(.el-form-item__content) {
    display: block;
  }
  :deep(.el-input) {
    display: inline-block;
    width: auto;
  }
  :deep(.el-input__wrapper) {
    width: 100%;
  }
</style>
