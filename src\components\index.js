/*
 * @Description: auto import/register global component
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-08-10 11:38:06
 * @LastEditTime: 2023-08-10 13:38:05
 */
export function setupCpn(app) {
  // 加载组件
  const Components = import.meta.globEager("./**/index.vue")
  // 全局注册过多组件可能会引起堵塞
  for (const key in Components) {
    const name = key.replace(/\.\/([A-Za-z]+)\/index\.vue/, "$1")
    const component = Components[key].default
    app.component(name, component)
  }
}
