<!--
 * @Description: 选择人员
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-11-15 16:07:48
 * @LastEditTime: 2025-06-26 10:14:08
-->

<template>
  <div class="person-tab-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
      class="search-form"
    >
      <el-form-item label="用户名称" prop="userName">
        <el-input
          v-model="queryParams.userName"
          placeholder="请输入用户名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item label="手机号码" prop="phonenumber">
        <el-input
          v-model="queryParams.phonenumber"
          placeholder="请输入手机号码"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item> -->
      <el-form-item label="部门" prop="deptId">
        <el-tree-select
          v-model="queryParams.deptId"
          :data="deptOptions"
          :default-expanded-keys="[100]"
          :props="{ value: 'id', label: 'label', children: 'children' }"
          value-key="id"
          placeholder="请选择部门"
          check-strictly
          clearable
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <div class="action-bar">
      <el-button
        type="primary"
        plain
        icon="Plus"
        @click="handleAdd"
        :disabled="selectionList.length === 0"
        size="small"
      >
        添加选中人员
      </el-button>
    </div>

    <div class="table-container">
      <el-table
        ref="choosePersonnelTableRef"
        :data="tableData"
        row-key="userId"
        @selection-change="handleSelectionChange"
        height="100%"
        size="small"
      >
        <el-table-column
          reserve-selection
          type="selection"
          width="55"
          :selectable="selectable"
        />
        <el-table-column label="用户编号" key="userId" prop="userId" />
        <el-table-column label="用户名称" key="userName" prop="userName" />
        <el-table-column label="用户昵称" key="nickName" prop="nickName" />
        <el-table-column label="部门" key="deptName" prop="dept.deptName" />
        <el-table-column
          label="手机号码"
          key="phonenumber"
          prop="phonenumber"
          width="120"
        />
        <el-table-column label="创建时间" prop="createTime" width="160">
          <template #default="scope">
            <span>{{ parseTime(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="pagination-wrapper">
      <pagination
        class="dialog-pagination"
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
        :autoScroll="false"
      />
    </div>
  </div>
</template>

<script setup name="ChoosePersonnelOrDeptDialog">
  import { listUser } from "@/api/system/user"

  const emit = defineEmits(["selection-change"])
  const { proxy } = getCurrentInstance()

  const tableData = ref([])
  const showSearch = ref(true)
  const total = ref(0)
  const dateRange = ref([])
  const choosePersonnelTableRef = ref()

  const data = reactive({
    queryParams: {
      pageNum: 1,
      pageSize: 10
    },
    selectedIds: []
  })
  const { queryParams } = toRefs(data)

  const props = defineProps({
    deptOptions: {
      type: Array,
      default: () => []
    },
    selectedPersonnelList: {
      type: Array,
      default: () => []
    }
  })

  onMounted(() => {
    getList()
  })

  /** 查询目录列表 */
  const getList = async () => {
    const res = await listUser(
      proxy.addDateRange(queryParams.value, dateRange.value)
    )
    if (res.code === 200) {
      tableData.value = res.rows
      total.value = res.total
    }
  }
  /** 搜索按钮操作 */
  function handleQuery() {
    getList()
  }
  /** 重置按钮操作 */
  function resetQuery() {
    dateRange.value = []
    proxy.resetForm("queryRef")
    handleQuery()
  }
  const selectionList = ref([])
  function handleSelectionChange(selection) {
    selectionList.value = selection
  }

  const handleAdd = () => {
    emit("selection-change", selectionList.value)
    proxy.$refs["choosePersonnelTableRef"].clearSelection()
  }

  const selectable = row => {
    if (
      !props.selectedPersonnelList ||
      props.selectedPersonnelList.length === 0
    )
      return true
    if (props.selectedPersonnelList.includes(row.userId)) {
      return false // 禁用
    } else {
      return true // 可选
    }
  }
</script>

<style lang="scss" scoped>
  .person-tab-container {
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .search-form {
    flex-shrink: 0;
    padding: 12px 0 0;
    background: #fafbfc;
    border-radius: 6px;

    :deep(.el-form-item) {
      margin-bottom: 8px;
    }

    :deep(.el-input) {
      width: 160px;
    }

    :deep(.el-tree-select) {
      width: 160px;
    }

    :deep(.el-date-editor) {
      width: 240px;
    }
  }

  .action-bar {
    flex-shrink: 0;
    padding: 8px 0;
    margin-bottom: 8px;
  }

  .table-container {
    flex: 1;
    overflow: hidden;
    border-radius: 6px;
  }

  .pagination-wrapper {
    flex-shrink: 0;
    display: flex;
    justify-content: center;
    padding: 4px 0;
  }

  :deep(.pagination-container .el-pagination) {
    position: static;
  }
  :deep(.pagination-container) {
    padding-top: 20px;
    margin: 0;
    background-color: #F8F9FA !important;
  }

  .dialog-pagination {
    margin-bottom: 30px;
  }
</style>
