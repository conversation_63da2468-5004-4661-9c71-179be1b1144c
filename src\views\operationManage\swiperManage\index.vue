<!--
 * @Description: 轮播图管理列表
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-03-08 11:28:06
 * @LastEditTime: 2023-09-13 16:44:31
-->
<template>
  <div class="app-container">
    <el-row :gutter="10" class="mb-8px">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleUpdate"
          >新增</el-button
        >
      </el-col>
      <right-toolbar
        :search="false"
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="tableData">
      <el-table-column prop="name" label="轮播图类型名称"></el-table-column>
      <el-table-column prop="realName" label="展示名称"></el-table-column>
      <el-table-column prop="flag" label="标识符"></el-table-column>
      <el-table-column prop="updateBy" label="更新者"></el-table-column>
      <el-table-column prop="updateTime" label="更新时间"></el-table-column>
      <el-table-column
        label="操作"
        width="250"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <!-- 首页轮播图永远取id为2030的这条数据 新增租户时数据库也会同步这条数据至新租户 -->
          <template v-if="scope.row.carouselId !== 2030">
            <el-button
              link
              type="primary"
              icon="Edit"
              @click="handleUpdate(scope.row)"
              >修改</el-button
            >
            <el-button
              link
              type="primary"
              icon="Delete"
              @click="handleDelete(scope.row)"
              >删除</el-button
            >
          </template>

          <el-button
            type="primary"
            link
            icon="Tools"
            @click="showItemsDataHandle(scope.row)"
          >
            数据管理
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 弹窗, 新增 / 修改 -->
    <addEditDialog
      ref="addEditDialogRef"
      @refreshDataList="getList"
    ></addEditDialog>

    <!-- 数据项配置 -->
    <el-drawer
      v-if="itemsDataVisible"
      v-model="itemsDataVisible"
      :title="itemsDataTitle"
      :size="1100"
      :close-on-press-escape="false"
      :close-on-click-modal="true"
    >
      <items-data :items-type-id="itemsTypeId"></items-data>
    </el-drawer>
  </div>
</template>

<script setup name="swiperManageList">
  import addEditDialog from "./addEditDialog.vue"
  import itemsData from "./components/CmsImgIndex.vue"
  import { listSwiper, delSwiper } from "@/api/opertaionManage/swiper.js"

  const { proxy } = getCurrentInstance()

  const tableData = ref([])
  const loading = ref(true)
  const showSearch = ref(false)
  const total = ref(0)
  const addEditDialogRef = ref()

  const data = reactive({
    queryParams: {
      pageNum: 1,
      pageSize: 10
    }
  })

  const { queryParams } = toRefs(data)

  /** 查询目录列表 */
  function getList() {
    loading.value = true
    listSwiper(queryParams.value).then(response => {
      tableData.value = response.rows || []
      total.value = response.total
      loading.value = false
    })
  }
  /** 修改按钮操作 */
  function handleUpdate(row) {
    addEditDialogRef.value.openDialog(row.carouselId)
  }
  /** 删除按钮操作 */
  function handleDelete(row) {
    proxy.$modal
      .confirm('是否确认删除名称为"' + row.name + '"的数据项?')
      .then(function () {
        return delSwiper(row.carouselId)
      })
      .then(() => {
        getList()
        proxy.$modal.msgSuccess("删除成功")
      })
      .catch(() => {})
  }

  const itemsDataVisible = ref(false)
  const itemsDataTitle = ref()
  const itemsTypeId = ref()
  const showItemsDataHandle = row => {
    itemsDataVisible.value = true
    itemsTypeId.value = row.carouselId
    itemsDataTitle.value = "轮播图管理 【 " + row.name + " 】"
  }

  getList()
</script>
<style lang="scss" scoped></style>
