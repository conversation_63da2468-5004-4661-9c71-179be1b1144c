<!--
 * @Description: 证书选择弹窗
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-06-09 13:33:15
 * @LastEditTime: 2023-11-16 16:31:03
-->
<template>
  <div class="app-container">
    <el-dialog
      v-model="dialogVisible"
      title="选择证书"
      width="50%"
      top="110px"
      @close="close"
      :key="new Date().getTime()"
      center
    >
      <el-form
        :model="queryParams"
        ref="queryRef"
        :inline="true"
        v-show="showSearch"
      >
        <el-form-item label="证书名称" prop="templateName">
          <el-input
            v-model="queryParams.templateName"
            placeholder="请输入证书名称"
            clearable
            style="width: 240px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-table
        ref="chooseCertificateTableRef"
        :data="tableData"
        highlight-current-row
        @current-change="handleCurrentChange"
      >
        <el-table-column label="证书封面" prop="templateImg">
          <template #default="scope">
            <el-popover placement="right" :width="400" trigger="hover">
              <img :src="scope.row.templateImg" width="375" height="375" />
              <template #reference>
                <img
                  :src="scope.row.templateImg"
                  style="max-height: 60px; max-width: 60px"
                />
              </template>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column label="证书名称" prop="templateName" />
        <el-table-column label="有效时间（年）" prop="effectiveTime" />
        <el-table-column label="编号规则" prop="codeRules" />
        <el-table-column label="证书介绍" prop="templateIntroduction" />
      </el-table>

      <pagination
        class="dialog-pagination"
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
        :autoScroll="false"
      />
      <template #footer>
        <el-button @click="close">取消</el-button>
        <el-button type="primary" @click="save">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="ChooseCertificateDialog">
  import { listCert } from "@/api/devops/cert-template.js"

  const emit = defineEmits(["fetch-data"])
  const { proxy } = getCurrentInstance()

  const tableData = ref([])
  const showSearch = ref(true)
  const dialogVisible = ref(false)
  const total = ref(0)

  const data = reactive({
    queryParams: {
      pageNum: 1,
      pageSize: 10
    }
  })

  const { queryParams } = toRefs(data)

  //** 弹框打开事件 */
  const openDialog = async () => {
    dialogVisible.value = true
    await getList()
  }

  /** 查询目录列表 */
  const getList = async () => {
    const res = await listCert(queryParams.value)
    if (res.code === 200) {
      tableData.value = res.rows
      total.value = res.total
    }
  }

  /** 搜索按钮操作 */
  function handleQuery() {
    getList()
  }
  /** 重置按钮操作 */
  function resetQuery() {
    proxy.resetForm("queryRef")
    handleQuery()
  }
  // 关闭弹框并重置操作
  const close = () => {
    currentRow.value = {}
    proxy.resetForm("queryRef")
    dialogVisible.value = false
  }
  const save = () => {
    if (!currentRow.value.certTemplateId) {
      return proxy.$modal.msgWarning("请选择证书")
    }
    emit("fetch-data", currentRow.value)
    proxy.$modal.msgSuccess("操作成功")
    close()
  }

  const currentRow = ref({})
  const handleCurrentChange = val => {
    currentRow.value = val
  }

  defineExpose({
    openDialog
  })
</script>

<style lang="scss" scoped>
  :deep(.pagination-container .el-pagination) {
    position: static;
  }
</style>
