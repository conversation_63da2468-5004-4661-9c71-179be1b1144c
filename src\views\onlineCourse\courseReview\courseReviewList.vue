<!--
 * @Description: 课程审核列表
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-09-06 08:47:52
 * @LastEditTime: 2025-05-27 10:00:44
-->
<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
    >
      <el-form-item label="课程编号" prop="courseCode">
        <el-input
          v-model="queryParams.courseCode"
          placeholder="请输入课程编号"
          clearable
          style="width: 250px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="课程名称" prop="courseName">
        <el-input
          v-model="queryParams.courseName"
          placeholder="请输入课程名称"
          clearable
          style="width: 250px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="课程目录" prop="catalogueId">
        <el-tree-select
          v-model="queryParams.catalogueId"
          :data="catalogueOptions"
          :props="{
            value: 'catalogueId',
            label: 'catalogueName',
            children: 'children',
            disabled: 'disabled'
          }"
          clearable
          value-key="catalogueId"
          placeholder="选择课程目录"
          check-strictly
          default-expand-all
        />
      </el-form-item>
      <el-form-item label="课程标签" prop="courseLabel">
        <el-select
          v-model="queryParams.courseLabel"
          placeholder="课程标签"
          clearable
          style="width: 250px"
        >
          <el-option
            v-for="dict in course_label_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        label="课程状态"
        prop="courseStatus"
        v-if="!isReviewRoleCom"
      >
        <el-select
          v-model="queryParams.courseStatus"
          placeholder="课程状态"
          clearable
          style="width: 250px"
        >
          <el-option
            v-for="dict in course_approval_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb-8px">
      <!-- <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Check"
          :disabled="multiple"
          @click="handleBatchReiview"
        >
          批量审核通过
        </el-button>
      </el-col> -->

      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="tableData"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="50" />
      <el-table-column label="课程封面" prop="courseImage" width="110">
        <template #default="scope">
          <el-popover placement="right" :width="400" trigger="hover">
            <img :src="scope.row.courseImage" width="375" height="375" />
            <template #reference>
              <img
                :src="scope.row.courseImage"
                style="max-height: 60px; max-width: 60px"
              />
            </template>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column label="课程编号" prop="courseCode" width="100" />
      <el-table-column label="课程名称" prop="courseName" min-width="150">
        <template #default="scope">
          <span class="listLink" @click="previewVideo(scope.row)">
            {{ scope.row.courseName }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="所属目录" prop="catalogueName" width="135" />
      <el-table-column label="课程等级" prop="courseLevel" width="150">
        <template #default="scope">
          <el-rate v-model="scope.row.courseLevel" disabled />
        </template>
      </el-table-column>
      <el-table-column label="课程标签" prop="courseLabel" width="150">
        <template #default="scope">
          <span v-if="scope.row.courseLabel">
            <el-tag
              v-for="(labelValue, index) in scope.row.courseLabel.split(',')"
              :key="index"
              :type="getLabelType(labelValue)"
              style="margin-right: 5px; margin-bottom: 5px;"
            >
              {{ getLabelName(labelValue) }}
            </el-tag>
          </span>
        </template>
      </el-table-column>
      <el-table-column label="用户群体" prop="userGroupsName" width="150" />
      <el-table-column label="场所" prop="location" width="220">
        <template #default="scope">
          <dict-tag :options="course_location" :value="scope.row.location" />
        </template>
      </el-table-column>
      <el-table-column label="备注" prop="remark" width="150" />
      <el-table-column label="课程类型" prop="courseTypeName" />
      <el-table-column label="所属套餐" prop="belongPackage" width="150" />
      <el-table-column
        v-if="!domainName.value"
        label="上传租户"
        prop="uploadTenant"
      />
      <el-table-column label="课程价格" prop="coursePrice">
        <template #default="scope">
          {{
            scope.row.coursePrice === null ||
            scope.row.coursePrice === undefined
              ? ""
              : `${scope.row.coursePrice}元`
          }}
        </template>
      </el-table-column>
      <el-table-column label="上架时间" prop="onShelfTime" width="110">
        <template #default="scope">
          {{
            scope.row.onShelfTime &&
            dayjs(scope.row.onShelfTime).format("YYYY-MM-DD")
          }}
        </template>
      </el-table-column>
      <el-table-column label="下架时间" prop="offShelfTime" width="110">
        <template #default="scope">
          {{
            scope.row.onShelfTime &&
            dayjs(scope.row.offShelfTime).format("YYYY-MM-DD")
          }}
        </template>
      </el-table-column>
      <el-table-column label="课程状态" prop="courseStatus" width="100">
        <template #default="scope">
          <dict-tag
            :options="course_approval_status"
            :value="scope.row.courseStatus"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        width="150"
        class-name="small-padding fixed-width"
        fixed="right"
      >
        <template #default="scope">
          <el-button
            link
            type="primary"
            icon="View"
            @click="handleDetail(scope.row)"
            >查看</el-button
          >
          <el-button
            v-if="isReviewRoleCom"
            link
            type="primary"
            icon="UserFilled"
            @click="handleReview(scope.row)"
          >
            {{
              proxy
                .selectDictLabel(course_approval_status, scope.row.courseStatus)
                .slice(-2)
            }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
    <preivewVideoDialog ref="preivewVideoDialogRef" />
  </div>
</template>

<script setup name="courseReviewList">
  import preivewVideoDialog from "../courseList/components/preivewVideoDialog"
  import dayjs from "dayjs"
  import {
    getCourseReviewList,
    batchApprovalCourse
  } from "@/api/onlineCourse/course"
  import { catalogueList } from "@/api/system/catalogue.js"
  import catalogue from "@/utils/catalogue.js"
  import useUserStore from "@/store/modules/user"
  import useTenantStore from "@/store/modules/tenant"

  const tenantStore = useTenantStore()
  const { domainName } = storeToRefs(tenantStore)
  const { proxy } = getCurrentInstance()
  const emit = defineEmits(["updateCurrentView"])
  const props = defineProps({
    row: {
      type: Object,
      default: () => ({})
    }
  })

  const userStore = useUserStore()
  const { roles } = storeToRefs(userStore)
  const tableData = ref([])
  const loading = ref(true)
  const showSearch = ref(true)
  const total = ref(0)
  const catalogueOptions = ref([])
  const selectedList = ref([])
  const single = ref(true)
  const multiple = ref(true)
  const {
    course_location,
    pedu_course_type,
    optional_settings,
    course_label_type,
    course_approval_status
  } = proxy.useDict(
    "course_location",
    "pedu_course_type",
    "optional_settings",
    "course_label_type",
    "course_approval_status"
  )
  console.log("course_label_type", course_label_type)

  const data = reactive({
    queryParams: {
      pageNum: 1,
      pageSize: 10
    }
  })

  const { queryParams } = toRefs(data)
  // 是否包含审核角色
  const isReviewRoleCom = computed(() => {
    return userStore.isReviewRole
  })

  /** 查询目录列表 */
  function getList() {
    loading.value = true
    let courseStatusList = []
    if (isReviewRoleCom.value) {
      if (roles.value.includes("course_first_review_admin")) {
        courseStatusList.push("1")
      }
      if (roles.value.includes("course_second_review_admin")) {
        courseStatusList.push("4")
      }
      if (roles.value.includes("course_final_review_admin")) {
        courseStatusList.push("5")
      }
    }
    let courseStatus = queryParams.value.courseStatus
      ? queryParams.value.courseStatus
      : courseStatusList.length > 0
      ? courseStatusList.join(",")
      : "1,4,5"
    let queryData = {
      ...queryParams.value,
      courseStatus,
      sortField: "a.update_time",
      sortOrder: "desc"
    }
    getCourseReviewList(queryData).then(response => {
      tableData.value = response.rows || []
      tableData.value.forEach(item => {
        item.courseLevel = Number(item.courseLevel)
      })
      total.value = response.total
      loading.value = false
    })
  }
  /** 查询目录下拉树结构 */
  function getTreeselect() {
    catalogueOptions.value = []
    catalogueList({ catalogueType: catalogue.COURSE_CATALOGUE }).then(
      response => {
        const courseCatalogue = {
          catalogueId: 0,
          catalogueName: "主类目",
          children: [],
          disabled: true
        }
        courseCatalogue.children = proxy.handleTree(
          response.rows,
          "catalogueId"
        )
        catalogueOptions.value.push(courseCatalogue)
      }
    )
  }
  /** 搜索按钮操作 */
  function handleQuery() {
    getList()
  }
  /** 重置按钮操作 */
  function resetQuery() {
    proxy.resetForm("queryRef")
    handleQuery()
  }

  const handleDetail = row => {
    emit("updateCurrentView", "courseReview", {
      flag: "detail",
      ...row,
      queryParams: queryParams.value
    })
  }
  // 审批按钮操作
  function handleReview(row) {
    emit("updateCurrentView", "courseReview", {
      flag: "review",
      ...row,
      queryParams: queryParams.value
    })
  }
  // 视频预览
  const previewVideo = row => {
    proxy.$refs["preivewVideoDialogRef"].openDialog(row)
  }

  // 获取标签名称
  const getLabelName = (value) => {
    const label = course_label_type.value.find(item => item.value === value)
    return label ? label.label : value
  }

  // 获取标签类型
  const getLabelType = (value) => {
    const label = course_label_type.value.find(item => item.value === value)
    return label ? label.elTagType : 'default'
  }

  /** 选择条数  */
  function handleSelectionChange(selection) {
    selectedList.value = selection.map(item => ({
      courseId: item.courseId,
      courseStatus: item.courseStatus,
      approvalResults: 0
    }))
    single.value = selection.length != 1
    multiple.value = !selection.length
  }

  // 批量审批通过
  function handleBatchReiview() {
    proxy.$modal
      .confirm("是否确认批量审核通过所选数据项?")
      .then(function () {
        return batchApprovalCourse(selectedList.value)
      })
      .then(() => {
        getList()
        proxy.$modal.msgSuccess("操作成功")
      })
  }

  onMounted(() => {
    if (Object.keys(props.row).length > 0) {
      queryParams.value = props.row
    }
    getList()
    getTreeselect()
  })
</script>

<style lang="scss" scoped>
  :deep(.el-table .cell) {
    white-space: pre-line;
  }
</style>
