<!--
 * @Description: 短视频管理
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-04-10 17:21:58
 * @LastEditTime: 2023-04-12 15:32:53
-->
<template>
  <div class="app-container">
    <el-row :gutter="10" class="mb-8px">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="openForm('create')">
          新增
        </el-button>
      </el-col>
    </el-row>
    <el-table
      v-loading="loading"
      :data="list"
      :stripe="true"
      :show-overflow-tooltip="true"
    >
      <!-- <el-table-column label="关联企业" align="center" prop="enterpriseId" /> -->
      <el-table-column
        label="产品/服务名称"
        align="center"
        prop="productName"
      />
      <el-table-column
        label="产品/服务简介及价格"
        align="center"
        width="600"
        prop="productIntro"
      />
      <el-table-column
        label="对促进会会员优惠/免费说明"
        align="center"
        prop="discountInfo"
      />
      <el-table-column
        label="操作"
        width="250"
        class-name="small-padding fixed-width"
        fixed="right"
      >
        <template #default="scope">
          <el-button
            link
            type="primary"
            icon="Edit"
            @click="openForm('update', scope.row.id)"
            >修改</el-button
          >
          <el-button
            link
            type="primary"
            icon="Delete"
            @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
    <!-- 弹窗, 新增 / 修改 -->
    <productForm ref="productFormRef" @refreshDataList="getList"></productForm>
  </div>
</template>

<script setup name="videoList">
  import productForm from "./productForm.vue"
  import { listProduct, delProduct } from "@/api/enterpriseInfo/product.js"
  import {
    ElNotification,
    ElMessageBox,
    ElMessage,
    ElLoading
  } from "element-plus"

  const props = defineProps({
    enterpriseId: {
      type: Number
    }
  })

  const { proxy } = getCurrentInstance()
  const list = ref([])
  const loading = ref(false)
  const total = ref(0)
  const productFormRef = ref()

  const queryParams = reactive({
    pageNo: 1,
    pageSize: 10,
    enterpriseId: undefined
  })

  /** 监听主表的关联字段的变化，加载对应的子表数据 */
  watch(
    () => props.enterpriseId,
    val => {
      if (!val) {
        return
      }
      queryParams.enterpriseId = val
      handleQuery()
    },
    { immediate: true, deep: true }
  )

  /** 搜索按钮操作 */
  const handleQuery = () => {
    queryParams.pageNo = 1
    getList()
  }

  /** 查询列表 */
  const getList = async () => {
    loading.value = true
    try {
      const data = await listProduct(queryParams)
      list.value = data.rows
      total.value = data.total
    } finally {
      loading.value = false
    }
  }

  const openForm = (type, id) => {
    if (!props.enterpriseId) {
      ElMessage({ message: "请选择一个企业信息", type: "error" })
      return
    }
    productFormRef.value.openDialog(type, id, props.enterpriseId)
  }

  /** 删除按钮操作 */
  const handleDelete = async row => {
    proxy.$modal
      .confirm('是否确认删除名称为"' + row.productName + '"的数据项?')
      .then(function () {
        return delProduct(row.id)
      })
      .then(() => {
        getList()
        proxy.$modal.msgSuccess("删除成功")
      })
      .catch(() => {})
  }
</script>
<style lang="scss" scoped></style>
