<!--
 * @Description: 登录页面
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-11-07 16:46:03
 * @LastEditTime: 2024-05-24 11:47:37
-->
<template>
  <div class="login-index">
    <UnepLogin
      v-if="domainName !== 'eduxd' && unepTenantList.includes(domainName)"
    />
    <TownLogin v-else-if="domainName === 'town'" />
    <AqscLogin v-else-if="domainName === 'aqsc'" />
    <YygfLogin v-else-if="domainName === 'yygf'" />
    <Login v-else />
  </div>
</template>

<script setup>
  import { unepTenantList } from "@/utils/constant"
  import Login from "./login.vue"
  import UnepLogin from "./unepLogin.vue"
  import TownLogin from "./townLogin.vue"
  import AqscLogin from "./aqscLogin.vue"
  import YygfLogin from "./yygfLogin.vue"
  import useTenantStore from "@/store/modules/tenant"

  const tenantStore = useTenantStore()
  const { domainName } = storeToRefs(tenantStore)
</script>

<style lang="scss" scoped>
  .login-index {
    height: 100%;
    width: 100%;
  }
</style>
