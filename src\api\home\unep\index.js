/*
 * @Description:
 * @Author: <PERSON><PERSON><PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-09-11 13:30:17
 * @LastEditTime: 2024-05-10 15:23:55
 */
import request from "@/utils/request"

// queryTimeFrom queryTimeTo
export function unepDashboard(params) {
  return request({
    url: "/reportforms/report/unep-dashboard",
    method: "get",
    params
  })
}

// 平台登录数据
// queryTimeFrom queryTimeTo
export function unepLoginData(params) {
  return request({
    url: "/reportforms/report/unep-loginData",
    method: "get",
    params
  })
}

// 学习排名
export function getUnepStudyRank(params) {
  return request({
    url: "/system/user/getUnepStudyRank",
    method: "get",
    params
  })
}

// 西电学习排名
export function getXdStudyRank(params) {
  return request({
    url: "/system/user/getXdStudyRank",
    method: "get",
    params
  })
}
