import request from "@/utils/request"
// =====================  目录管理  =====================
// 查询目录列表
export function catalogueList(query) {
  return request({
    url: "/system/catalogue/list",
    method: "get",
    params: query
  })
}

// 新增目录
export function addCatalogue(data) {
  return request({
    url: "/system/catalogue",
    method: "post",
    data: data
  })
}

// 修改目录
export function updateCatalogue(data) {
  return request({
    url: "/system/catalogue",
    method: "put",
    data: data
  })
}

// 删除目录
export function delCatalogue(catalogueIds) {
  return request({
    url: "/system/catalogue/" + catalogueIds,
    method: "delete"
  })
}

// 查询目录详细
export function getCatalogue(catalogueId) {
  return request({
    url: "/system/catalogue/" + catalogueId,
    method: "get"
  })
}
