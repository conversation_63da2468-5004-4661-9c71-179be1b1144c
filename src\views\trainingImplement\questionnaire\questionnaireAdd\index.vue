<!--
 * @Description: 新增/修改调查问卷
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-05-10 16:11:58
 * @LastEditTime: 2024-05-07 14:18:55
-->

<template>
  <div class="app-container">
    <el-form
      :model="form"
      ref="formRef"
      label-width="130px"
      :rules="rules"
      :inline="true"
    >
      <el-row :gutter="10">
        <el-col :span="24">
          <el-form-item label="问卷目录" prop="catalogueId">
            <el-tree-select
              v-model="form.catalogueId"
              :data="catalogueOptions"
              :props="{
                value: 'catalogueId',
                label: 'catalogueName',
                children: 'children',
                disabled: 'disabled'
              }"
              value-key="catalogueId"
              placeholder="选择问卷目录"
              check-strictly
              @current-change="catalogueSelect"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="调查标题" prop="surveyTitle">
            <el-input v-model="form.surveyTitle" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="提醒规则" prop="reminderRules">
            <span class="text">
              过期前<el-input-number
                v-model="form.reminderRules"
                :min="0"
                :max="999"
              />天提醒 （为0时为不发送）
            </span>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="问卷封面" prop="questionnaireCover">
            <ImageUpload v-model="form.questionnaireCover" :limit="1" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="调查说明">
            <el-input
              v-model="form.surveyDescription"
              :autosize="{ minRows: 2, maxRows: 4 }"
              type="textarea"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="是否可查看结果" prop="isResult">
            <el-select
              v-model="form.isResult"
              placeholder="是否可查看结果"
              clearable
              style="width: 200px"
            >
              <el-option
                v-for="dict in is_or_not"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="积分" prop="integral">
            <el-input-number
              v-model="form.integral"
              :step="0.1"
              :precision="1"
              :min="0"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="调查试题" prop="questions">
            <el-input v-model="form.questions" disabled />
            <el-button
              type="primary"
              class="chooseQuestionnaireUser"
              @click="questionItemAdd"
              >选择调查试题</el-button
            >
          </el-form-item>
        </el-col>
        <el-col :span="24" class="buttonCol">
          <el-form-item>
            <el-button type="primary" @click="submitForm">提交</el-button>
            <el-button @click="handleBack">返回</el-button>
            <el-button @click="reset">重置</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <chooseQuestionDialog
      ref="chooseQuestionDialogRef"
      @fetch-data="chooseQuestionDone"
    />
  </div>
</template>

<script setup name="questionnaireAdd">
  import chooseQuestionDialog from "./components/chooseQuestionDialog.vue"
  import {
    addQuestionnaire,
    getQuestionnaire,
    updateQuestionnaire
  } from "@/api/trainingImplement/questionnaire"
  import { catalogueList } from "@/api/system/catalogue.js"
  import catalogue from "@/utils/catalogue.js"

  const route = useRoute()
  const { proxy } = getCurrentInstance()
  const { is_or_not } = proxy.useDict("is_or_not")

  const catalogueOptions = ref([])
  const flag = ref("add")

  const data = reactive({
    form: {},
    rules: {
      catalogueId: [
        { required: true, message: "问卷目录不能为空", trigger: "change" }
      ],
      surveyTitle: [
        { required: true, message: "调查标题不能为空", trigger: "change" }
      ],
      questionnaireCover: [
        { required: true, message: "调查标题不能为空", trigger: "change" }
      ],
      reminderRules: [
        { required: true, message: "提醒规则不能为空", trigger: "change" }
      ],
      isResult: [
        { required: true, message: "是否可查看结果不能为空", trigger: "change" }
      ],
      questions: [
        { required: true, message: "调查试题不能为空", trigger: "change" }
      ]
    }
  })
  const { form, rules } = toRefs(data)

  if (Object.keys(route.query).length !== 0) {
    getQuestionnaire(route.query.questionnaireId).then(res => {
      form.value = res.data
      flag.value = "edit"
    })
  }

  /** 查询菜单下拉树结构 */
  function getTreeselect() {
    catalogueOptions.value = []
    catalogueList({ catalogueType: catalogue.QUESTIONNAIRE_CATALOGUE }).then(
      response => {
        const catalogueTree = {
          catalogueId: 0,
          catalogueName: "主类目",
          children: [],
          disabled: true
        }
        catalogueTree.children = proxy.handleTree(response.rows, "catalogueId")
        catalogueOptions.value.push(catalogueTree)
      }
    )
  }
  const submitForm = () => {
    proxy.$refs["formRef"].validate(valid => {
      form.value.questions = Array.isArray(form.value.questions)
        ? form.value.questions.join()
        : form.value.questions
      if (valid) {
        if (flag.value === "add") {
          addQuestionnaire(form.value).then(res => {
            if (res.code === 200) {
              reset()
              proxy.$modal.msgSuccess("新增成功")
              proxy.$tab.closeOpenPage({
                path: "/questionnaire/questionnaireList"
              })
            }
          })
        } else {
          updateQuestionnaire(form.value).then(res => {
            if (res.code === 200) {
              reset()
              proxy.$modal.msgSuccess("修改成功")
              proxy.$tab.closeOpenPage({
                path: "/questionnaire/questionnaireList"
              })
            }
          })
        }
      }
    })
  }

  const catalogueSelect = (data, node) => {
    form.value.catalogueName = data.catalogueName
  }

  /** 表单重置 */
  function reset() {
    form.value = {}
  }

  // 选择题目
  const questionItemAdd = () => {
    proxy.$refs["chooseQuestionDialogRef"].openDialog(form.value.questions)
  }
  // 选择题目完成
  const chooseQuestionDone = questionList => {
    form.value.questions = questionList
  }
  const handleBack = () => {
    proxy.$tab.closeOpenPage({ path: "/questionnaire/questionnaireList" })
  }

  getTreeselect()
</script>
<style lang="scss" scoped>
  :deep(.el-input-number .el-input__wrapper) {
    padding-left: 50px;
    padding-right: 50px;
  }
  :deep(.el-textarea__inner) {
    width: 300px;
  }
  :deep(.el-input) {
    display: inline-block;
    width: auto;
  }

  .text {
    font-size: 14px;
    font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB,
      Microsoft YaHei, Arial, sans-serif;
    font-weight: 500;
    color: #606266;
  }

  .buttonCol {
    margin-left: 100px;
  }

  .chooseQuestionnaireUser {
    margin-left: 20px;
  }
</style>
