<!--
 * @Description: 选择人员或部门弹窗
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-07-26 15:10:47
 * @LastEditTime: 2025-06-26 10:10:13
-->
<template>
  <el-dialog
    v-model="dialogVisible"
    title="选择人员"
    width="80%"
    @close="close"
    :key="new Date().getTime()"
    center
    class="personnel-dialog"
  >
    <div class="personnel-container">
      <!-- 左侧选择区域 -->
      <div class="selection-area">
        <div class="tabs-container">
          <el-tabs class="selection-tabs" v-model="activeTab">
            <el-tab-pane label="按人员" name="1">
              <choosePersonTab
                ref="choosePersonTabRef"
                :deptOptions="deptOptions"
                @selection-change="addUserList"
                :selectedPersonnelList="selectedPersonnelList"
              />
            </el-tab-pane>
            <el-tab-pane label="按部门" name="2">
              <chooseDeptTab
                ref="chooseDeptTabRef"
                :deptOptions="deptOptions"
                @selection-change="addUserList"
                :selectedPersonnelList="selectedPersonnelList"
              />
            </el-tab-pane>
          </el-tabs>
          <el-button
            type="primary"
            plain
            size="small"
            icon="Plus"
            @click="handleSelectAll"
            class="select-all-btn"
          >
            选择全部
          </el-button>
        </div>
      </div>

      <!-- 右侧已选择人员区域 -->
      <div class="selected-area">
        <div class="area-header">
          <h4 class="area-title">
            已选择人员
            <span class="count-badge" v-if="personnelList.length > 0">
              {{ personnelList.length }}
            </span>
          </h4>
          <el-button
            type="danger"
            plain
            size="small"
            icon="Delete"
            @click="clearAll"
            :disabled="personnelList.length === 0"
          >
            清空
          </el-button>
        </div>

        <div class="selected-content">
          <el-empty
            v-if="personnelList.length === 0"
            description="暂未选择任何人员"
            :image-size="80"
          >
            <template #image>
              <el-icon size="80" color="#c0c4cc">
                <User />
              </el-icon>
            </template>
          </el-empty>

          <el-scrollbar v-else height="100%" class="selected-list">
            <div class="selected-grid-container">
              <div
                v-for="(item, index) in personnelList"
                :key="item.userId"
                class="selected-item-card"
              >
                <div class="card-content">
                  <el-avatar :size="24" class="item-avatar">
                    {{ item.userName.substring(0, 1) }}
                  </el-avatar>
                  <div class="item-info">
                    <div class="item-name">{{ item.userName }}</div>
                    <div class="item-id">{{ item.userId }}</div>
                  </div>
                </div>
                <el-button
                  type="danger"
                  text
                  size="small"
                  icon="Close"
                  @click="deleteTag(index)"
                  class="item-remove"
                />
              </div>
            </div>
          </el-scrollbar>
        </div>
      </div>
    </div>

    <!-- 底部操作区域 -->
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="close" size="large">取消</el-button>
        <el-button
          type="primary"
          @click="save"
          size="large"
          :disabled="personnelList.length === 0"
        >
          确定选择
          <span v-if="personnelList.length > 0">
            ({{ personnelList.length }}人)
          </span>
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup name="ChoosePersonnelOrDeptDialog">
  import { listUser } from "@/api/system/user"
  import { uniqBy } from "lodash-es"
  import { deptTreeSelect } from "@/api/system/user"
  import chooseDeptTab from "./components/chooseDeptTab"
  import choosePersonTab from "./components/choosePersonTab"
  import { User } from "@element-plus/icons-vue"

  const emit = defineEmits(["fetch-data"])
  const { proxy } = getCurrentInstance()
  const personnelList = ref([])
  const activeTab = ref("1")
  const dialogVisible = ref(false)
  const deptOptions = ref(undefined)

  const data = reactive({
    selectedIds: [],
    selectedLabels: [],
    selectedDepts: []
  })
  const { selectedLabels, selectedIds, selectedDepts } = toRefs(data)

  const props = defineProps({
    needDeptId: {
      type: Boolean,
      default: false
    },
    selectedPersonnelList: {
      type: Array,
      default: () => []
    }
  })

  const choosePersonTabRef = ref()
  const field = ref()
  //** 弹框打开事件 */
  const openDialog = async (ids, labels, depts, fieldParam) => {
    activeTab.value = "1"
    dialogVisible.value = true
    getDeptTree()
    if (ids) {
      selectedIds.value = Array.isArray(ids) ? ids : ids.split(",")
    }
    if (labels) {
      selectedLabels.value = Array.isArray(labels) ? labels : labels.split(",")
    }
    if (depts) {
      selectedDepts.value = Array.isArray(depts) ? depts : depts.split(",")
    }
    if (fieldParam !== undefined || fieldParam !== null)
      field.value = fieldParam

    if (
      selectedIds.value.length > 0 &&
      selectedLabels.value.length > 0 &&
      selectedIds.value.length === selectedLabels.value.length
    ) {
      personnelList.value = []
      for (let i = 0; i < selectedIds.value.length; i++) {
        const obj = {
          userId: selectedIds.value[i],
          userName: selectedLabels.value[i],
          deptId: props.needDeptId ? selectedDepts.value[i] : undefined
        }
        personnelList.value.push(obj)
      }
    }
  }
  /** 查询部门下拉树结构 */
  function getDeptTree() {
    deptTreeSelect().then(response => {
      deptOptions.value = response.data
    })
  }

  // 关闭弹框并重置操作
  const close = () => {
    proxy.resetForm("queryRef")
    personnelList.value = []
    selectedIds.value = []
    selectedLabels.value = []
    selectedDepts.value = []
    field.value = undefined
    dialogVisible.value = false
  }
  const save = async () => {
    if (personnelList.value.length === 0) {
      return proxy.$modal.msgWarning("请至少添加一名人员")
    }
    emit("fetch-data", personnelList.value, field.value)
    proxy.$modal.msgSuccess("操作成功")
    close()
  }

  const clearAll = () => {
    const count = personnelList.value.length
    personnelList.value = []
    if (count > 0) {
      proxy.$message.success(`已清空 ${count} 名人员`)
    }
  }

  // 子组件点击添加后回调事件
  const addUserList = extraUserList => {
    const prevLength = personnelList.value.length
    personnelList.value = uniqBy(
      personnelList.value.concat(extraUserList),
      "userId"
    )
    const newLength = personnelList.value.length
    if (newLength > prevLength) {
      proxy.$message.success(`成功添加 ${newLength - prevLength} 名人员`)
    }
  }

  // 删除单个人员
  const deleteTag = index => {
    const removedUser = personnelList.value[index]
    personnelList.value.splice(index, 1)
    proxy.$message.success(`已移除 ${removedUser.userName}`)
  }

  const handleSelectAll = async () => {
    proxy.$modal.confirm("确认选择全部人员吗?").then(async () => {
      const res = await listUser({
        pageSize: 9999
      })
      emit("fetch-data", res.rows, field.value)
      proxy.$modal.msgSuccess("操作成功")
      close()
    })
  }

  defineExpose({
    openDialog
  })
</script>

<style lang="scss" scoped>
  .personnel-dialog {
    :deep(.el-dialog__body) {
      padding: 20px;
    }
  }

  .personnel-container {
    display: flex;
    gap: 0;
    height: 600px;
    margin: -20px;
    padding: 20px;
    background: #ffffff;
    border-radius: 12px;
  }

  .selection-area {
    flex: 2;
    border-right: 2px solid #f0f2f5;
    padding-right: 20px;
    background: linear-gradient(135deg, #fafbfc 0%, #f8f9fa 100%);
    border-radius: 8px;
    padding: 20px;
    margin-right: 20px;
  }

  .tabs-container {
    height: 100%;
    position: relative;
  }

  .select-all-btn {
    position: absolute;
    top: 8px;
    right: 12px;
    z-index: 10;
  }

  .selected-area {
    flex: 1;
    min-width: 300px;
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  }

  .area-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    padding-bottom: 6px;
    border-bottom: 1px solid #e4e7ed;
  }

  .area-title {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .count-badge {
    background: linear-gradient(135deg, #409eff, #5cb6ff);
    color: white;
    border-radius: 12px;
    padding: 3px 10px;
    font-size: 12px;
    font-weight: 500;
    box-shadow: 0 2px 4px rgba(64, 158, 255, 0.3);
    animation: pulse 2s infinite;
  }

  @keyframes pulse {
    0%,
    100% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.05);
    }
  }

  .selection-tabs {
    height: 100%;

    :deep(.el-tabs__content) {
      height: calc(100% - 40px);
      overflow: hidden;
    }

    :deep(.el-tab-pane) {
      height: 100%;
      overflow: hidden;
    }

    :deep(.el-tabs__header) {
      margin: 0;
      padding-right: 120px;
    }
  }

  .selected-content {
    height: calc(100% - 60px);
  }

  .selected-grid-container {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 8px;
    padding: 8px;
  }

  .selected-item-card {
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e9ecef;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    animation: slideInRight 0.3s ease-out;
    position: relative;
    overflow: hidden;

    &:hover {
      background: #e3f2fd;
      border-color: #2196f3;
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(33, 150, 243, 0.15);
    }
  }

  .card-content {
    display: flex;
    align-items: center;
    padding: 8px;
    gap: 6px;
  }

  @keyframes slideInRight {
    from {
      opacity: 0;
      transform: translateX(20px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  .item-avatar {
    flex-shrink: 0;
  }

  .item-info {
    flex: 1;
    min-width: 0;
  }

  .item-name {
    font-weight: 500;
    color: #303133;
    font-size: 12px;
    line-height: 1.2;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-bottom: 2px;
  }

  .item-id {
    font-size: 10px;
    color: #909399;
    line-height: 1;
  }

  .item-remove {
    position: absolute;
    top: 2px;
    right: 2px;
    width: 22px;
    height: 22px;
    padding: 0;

    :deep(.el-icon) {
      font-size: 18px;
    }
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 20px 0 0;
    border-top: 1px solid #e4e7ed;
    background: linear-gradient(
      90deg,
      transparent 0%,
      rgba(64, 158, 255, 0.03) 50%,
      transparent 100%
    );
  }

  // 响应式设计
  @media (max-width: 768px) {
    .personnel-container {
      flex-direction: column;
      height: auto;
    }

    .selection-area {
      border-right: none;
      border-bottom: 2px solid #f0f2f5;
      padding-right: 20px;
      margin-right: 0;
      margin-bottom: 20px;
    }

    .selected-area {
      min-width: auto;
      margin-top: 20px;
    }

    .selected-content {
      height: 300px;
    }
  }
</style>
