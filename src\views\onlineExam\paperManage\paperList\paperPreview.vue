<!--
 * @Description: 试卷预览页面
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-11-01 14:45:50
 * @LastEditTime: 2023-11-02 09:41:26
-->
<template>
  <div class="app-container">
    <div class="header-info">
      <el-icon class="backIcon" size="20" @click="handleBack">
        <ArrowLeftBold />
      </el-icon>
      <div class="base-info">
        <div class="info-item">
          试卷名称：
          <span class="bold-text">
            {{ completedPaperInfo?.paperName }}
          </span>
        </div>
        <div class="info-item">
          试卷总分：
          <span class="bold-text">
            {{ completedPaperInfo?.paperScore || 0 }} 分
          </span>
        </div>
        <div class="info-item">
          试卷题数：
          <span class="bold-text"> {{ examQuestionList.length }} 题 </span>
        </div>
        <div class="info-item">
          <el-button type="primary" @click="loadData">重新生成版本</el-button>
        </div>
      </div>
    </div>
    <div class="paper-detail">
      <div class="question-list">
        <template v-for="(item, index) in examQuestionList">
          <div class="box-item" :ref="`questionBox${index}`">
            <div class="questionType">
              {{ index + 1 }}.{{
                proxy.selectDictLabel(exam_question_type, item.questionType)
              }}
            </div>
            <div class="questionName">
              {{ item?.questionName }}
            </div>
            <div class="choice-box">
              <div class="choice-tap-item">
                <template v-for="choice in 11">
                  <div class="choice-item" v-if="item[`item${choice}`]">
                    <div class="index">
                      <img
                        v-if="isCorrect(item, String.fromCharCode(64 + choice))"
                        src="@/assets/images/correct.png"
                        alt=""
                      />
                      <img
                        v-else-if="
                          isWrong(item, String.fromCharCode(64 + choice))
                        "
                        src="@/assets/images/error.png"
                        alt=""
                      />
                      <span class="letterOnly" v-else>{{
                        String.fromCharCode(64 + choice)
                      }}</span>
                    </div>
                    <div class="content">
                      {{ item[`item${choice}`] }}
                    </div>
                  </div>
                </template>
              </div>
            </div>
          </div>
        </template>
      </div>
    </div>
    <div class="opertaion-bar">
      <el-checkbox
        style="margin-right: 20px"
        v-model="containAnswers"
        label="是否导出正确答案"
        size="large"
      />
      <el-button type="primary" @click="handleExport">导出</el-button>
    </div>
  </div>
</template>

<script setup name="paperPreview">
  import { previewPaper, exportPaperWord } from "@/api/onlineExam/paper.js"
  import dayjs from "dayjs"

  const { proxy } = getCurrentInstance()
  const { exam_question_type } = proxy.useDict("exam_question_type")

  const completedPaperInfo = ref()
  const paperTacticsList = ref()
  const examQuestionList = ref([])
  const emit = defineEmits(["updateCurrentView"])
  const props = defineProps({
    row: {
      type: Object,
      default: () => ({})
    }
  })

  const containAnswers = ref(false) // 是否导出正确答案
  const loadData = async () => {
    completedPaperInfo.value = {}
    paperTacticsList.value = []
    examQuestionList.value = []
    const { data } = await previewPaper(props.row.paperId)
    completedPaperInfo.value = data
    paperTacticsList.value = data.paperTactics
    paperTacticsList.value?.forEach(item => {
      item?.examQuestions.forEach(question => {
        examQuestionList.value.push(question)
      })
    })
  }

  // 正确的选项
  function isCorrect(item, choice) {
    if (item.questionType === "S" || item.questionType === "J") {
      return item.answer === choice
    } else if (item.questionType === "M") {
      if (item.answer.includes(",")) {
        return item.answer.split(",").indexOf(choice) > -1
      }
      return item.answer === choice
    }
    return false
  }

  // 错误的选项
  function isWrong(item, choice) {
    if (!item.userAnswer) return false
    if (item.questionType === "S" || item.questionType === "J") {
      return item.userAnswer === choice && item.userAnswer !== item.answer
    } else if (item.questionType === "M") {
      return item.userAnswer.includes(choice) && !item.answer.includes(choice)
    }
    return false
  }

  const handleBack = () => {
    emit("updateCurrentView", "paperList")
  }

  // 导出试卷详情word
  const handleExport = async () => {
    const res = await exportPaperWord({
      ...completedPaperInfo.value,
      containAnswers: containAnswers.value
    })
    let blob = new Blob([res], {
      type: `application/msword` // word文档为msword, pdf文档为pdf
    })
    let objectUrl = URL.createObjectURL(blob)
    let link = document.createElement("a")
    let fname = `${completedPaperInfo.value?.paperName}_${dayjs().format(
      "YYYY-MM-DD"
    )}.doc`
    link.href = objectUrl
    link.setAttribute("download", fname)
    document.body.appendChild(link)
    link.click()
  }

  loadData()
</script>

<style scoped lang="scss">
  .app-container {
    background-color: #f5f6fa;
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: center;
    padding-bottom: 60px;
  }
  .header-info,
  .exam-info,
  .paper-detail {
    display: flex;
    background-color: #fff;
    padding: 20px;
    width: 100%;
  }
  .header-info {
    flex-direction: column;
    .backIcon {
      cursor: pointer;
      vertical-align: text-bottom;
      margin-bottom: 20px;
    }

    .base-info {
      width: 60%;
      padding-left: 50px;

      .info-item {
        font-size: 15px;
        margin-bottom: 10px;

        .bold-text {
          font-size: 18px;
          font-weight: bold;
        }
      }
    }
  }

  .paper-detail {
    flex-direction: column;

    .question-list {
      border: 1px solid #ccc;
      margin: 25px;
      padding: 20px;
      .box-item {
        display: flex;
        flex-direction: column;
        padding: 20px;
        background-color: #fff;
        border-radius: 10px;

        .questionType {
          display: flex;
          font-size: 20px;
        }

        .questionName {
          margin-top: 20px;
          font-size: 16px;
        }

        .choice-box {
          padding: 15px 0;
          display: flex;
          .choice-tap-item {
            cursor: pointer;
            line-height: 40px;
            display: flex;
            flex-direction: column;
            .choice-item {
              display: flex;
              align-items: center;

              .choice-selected {
                background-color: #3ca7fa;
                border: 1px solid #3ca7fa;
              }

              .index {
                display: flex;
                justify-content: center;
                align-items: center;
                width: 24px;
                height: 24px;

                > img {
                  width: 95%;
                  height: 95%;
                }

                .letterOnly {
                  display: flex;
                  justify-content: center;
                  align-items: center;
                  border-radius: 50% 50%;
                  width: 22px;
                  height: 22px;
                  border: 1px solid #ccc;
                }
              }

              .content {
                padding-left: 10px;
                font-size: 16px;
              }
            }
          }
        }
      }
    }
  }

  .opertaion-bar {
    height: 60px;
    width: 100%;
    position: fixed;
    bottom: 0;
    background-color: #f6f6f6;
    display: flex;
    justify-content: center;
    align-items: center;
  }
</style>
