<!--
 * @Description: 新增/修改套餐实施
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-08-25 16:20:07
 * @LastEditTime: 2024-02-27 17:28:18
-->
<template>
  <el-dialog
    :title="title"
    v-model="visible"
    width="900px"
    append-to-body
    @close="reset"
  >
    <el-form ref="packageRef" :model="form" :rules="rules" label-width="80px">
      <div class="sub-title">选择套餐</div>
      <el-form-item label="发起部门" prop="deptId">
        <el-tree-select
          v-model="form.deptId"
          :data="deptOptions"
          :props="{ value: 'id', label: 'label', children: 'children' }"
          value-key="id"
          placeholder="请选择部门"
          check-strictly
          clearable
          @current-change="deptIdSelect"
        />
      </el-form-item>
      <el-form-item label="选择套餐" prop="packageId">
        <el-select
          v-model="form.packageId"
          clearable
          style="width: 240px"
          @change="selectPackage"
        >
          <el-option
            v-for="item in packageList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="套餐价格">
        <div class="total-price"> {{ packagePrice || 0 }}元 </div>
      </el-form-item>

      <div class="sub-title">设置账号</div>
      <el-form-item label="账号个数" prop="accountCount">
        <el-input
          @input="accountNumInput"
          v-model="form.accountCount"
          placeholder="请输入账号个数"
          clearable
          style="width: 240px"
        >
          <template #append>个</template>
        </el-input>
      </el-form-item>
      <el-form-item label="折扣比例">
        <div class="total-price">
          {{ accountDiscountRate * 100 }}%

          <el-popover placement="right" :width="280" trigger="hover">
            <template #reference>
              <el-icon style="margin-left: 5px"><QuestionFilled /></el-icon>
            </template>
            <img src="@/assets/images/accountDiscount.png" alt="" />
          </el-popover>
        </div>
      </el-form-item>
      <el-form-item label="折扣价位">
        <div class="total-price">
          {{ (accountDiscountRate * packagePrice).toFixed(2) || 0 }}元
        </div>
      </el-form-item>

      <div class="sub-title">视频容量</div>
      <div class="available-capacity-tips">
        当前剩余可分配容量：{{ remainCapacity || 0 }}MB
      </div>
      <el-form-item
        label="视频免费容量"
        prop="availableCapacity"
        label-width="100"
        style="position: relative"
      >
        <el-input
          @input="availableCapacityInput"
          v-model="form.availableCapacity"
          placeholder="请输入容量大小"
          clearable
          style="width: 240px"
        >
          <template #append>MB</template>
        </el-input>
      </el-form-item>

      <div class="sub-title">设置时长</div>
      <el-form-item label="开始时间" prop="startTime">
        <el-date-picker
          v-model="form.startTime"
          type="date"
          placeholder="开始时间"
          value-format="YYYY-MM-DD"
        />
      </el-form-item>
      <el-form-item label="结束时间" prop="endTime">
        <el-date-picker
          v-model="form.endTime"
          type="date"
          placeholder="结束时间"
          value-format="YYYY-MM-DD"
          :disabled-date="disabledDate"
        />
      </el-form-item>
      <el-form-item label="折扣比例">
        <div class="total-price">
          {{ timeDiscountRate * 100 }}%
          <el-popover placement="right" :width="280" trigger="hover">
            <template #reference>
              <el-icon style="margin-left: 5px"><QuestionFilled /></el-icon>
            </template>
            <img src="@/assets/images/durationDiscount.png" alt="" />
          </el-popover>
        </div>
      </el-form-item>
      <el-divider />
      <el-form-item label="总价位">
        <div class="total-price"> {{ theoreticalTotalPrice }}元 </div>
      </el-form-item>
      <el-form-item label="最终价格" prop="finalPrice">
        <el-input
          @input="v => (form.finalPrice = v.replace(/[^\d.]/g, ''))"
          v-model="form.finalPrice"
          clearable
          style="width: 240px"
        >
          <template #append>元</template>
        </el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="submitForm" :disabled="submitDisabled"
          >确 定</el-button
        >
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup name="addEditPackageImpDialog">
  import {
    getAccountDiscountRatio,
    getDurationDiscountRatio
  } from "@/utils/packageDiscount.js"
  import { deptTreeSelect } from "@/api/system/user"
  import { listPackage, getAvailableCapacity } from "@/api/system/package"
  import {
    getPackageImp,
    addEditPackageImp
  } from "@/api/onlineCourse/package.js"

  const emit = defineEmits(["fetch-data"])

  const { proxy } = getCurrentInstance()

  const deptOptions = ref()
  const visible = ref(false)
  const form = ref({})
  const title = ref("新增套餐实施")

  // 打开弹窗事件
  const openDialog = async id => {
    getDeptTree()
    await getPackageList()
    await fetchRemainCapacity()
    if (id) {
      title.value = "修改套餐实施"
      const { data } = await getPackageImp(id)
      Object.assign(form.value, data)
      selectPackage(form.value.packageId)
      form.value.status = form.value.status.toString()
    }
    visible.value = true
  }

  const rules = ref({
    deptId: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
    packageId: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
    accountCount: [
      { required: true, message: "必填项不能为空", trigger: "blur" }
    ],
    startTime: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
    endTime: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
    finalPrice: [{ required: true, message: "必填项不能为空", trigger: "blur" }]
  })

  /** 提交按钮 */
  function submitForm() {
    proxy.$refs["packageRef"].validate(async valid => {
      if (valid) {
        let submitData = {
          ...form.value,
          accountDiscountRate: accountDiscountRate.value,
          timeDiscountRate: timeDiscountRate.value,
          packagePrice: theoreticalTotalPrice.value
        }
        const { code } = await addEditPackageImp(submitData)
        if (code === 200) proxy.$modal.msgSuccess("操作成功")
        cancel()
        emit("fetch-data")
      }
    })
  }

  const cancel = () => {
    visible.value = false
    reset()
  }

  /** 表单重置 */
  function reset() {
    packagePrice.value = 0
    form.value = {}
    title.value = "新增套餐实施"
    proxy.resetForm("packageRef")
  }

  /** 查询部门下拉树结构 */
  const getDeptTree = async () => {
    const response = await deptTreeSelect({
      parentId: 100
    })
    deptOptions.value = response.data
  }

  const packageList = ref()
  const getPackageList = async () => {
    let queryData = {
      pageSize: 999,
      pageNum: 1,
      status: 0
    }
    const { rows } = await listPackage(queryData)
    packageList.value = rows || []
  }

  const packagePrice = ref(0) // 套餐原价格
  const endTimeDisabledCondition = ref(null) // 实施的结束时间必须大于这个时间（套餐内课程最小下架时间）
  const submitDisabled = ref(false)
  // 套餐select事件
  const selectPackage = val => {
    submitDisabled.value = false
    const selectedItem = packageList.value.find(item => item.id === val)
    packagePrice.value = selectedItem.packagePrice || 0
    if (packagePrice.value === -1) {
      packagePrice.value = 0
      proxy.$modal.msgWarning("该套餐下无课程，请先添加课程")
      submitDisabled.value = true
    }
    form.value.packageName = selectedItem.name || ""
    if (selectedItem.minimumCourseOffShelfTime) {
      endTimeDisabledCondition.value = selectedItem.minimumCourseOffShelfTime
    }
  }

  // 账号个数对应折扣比例
  const accountDiscountRate = computed(() => {
    if (!form.value?.accountCount) return 1
    return getAccountDiscountRatio(form.value.accountCount)
  })
  const accountNumInput = v => {
    // 使用正则表达式替换非数字字符为空字符串
    form.value.accountCount = v.replace(/\D/g, "")

    // 限制最大值为 1000
    if (Number(form.value.accountCount) > 1000) {
      form.value.accountCount = 1000
    }
  }

  const availableCapacityInput = v => {
    // 使用正则表达式替换非数字字符为空字符串
    form.value.availableCapacity = v.replace(/\D/g, "")
  }

  // 时长对应折扣比例
  const timeDiscountRate = computed(() => {
    if (!form.value.startTime || !form.value.endTime) return 1
    return getDurationDiscountRatio(
      new Date(form.value.startTime),
      new Date(form.value.endTime)
    )
  })

  // 理论总价格
  const theoreticalTotalPrice = computed(() => {
    if (!form.value.packageId) return 0
    return (
      packagePrice.value *
      timeDiscountRate.value *
      accountDiscountRate.value
    ).toFixed(2)
  })

  const deptIdSelect = data => {
    form.value.deptName = data.label
  }

  const disabledDate = time => {
    if (endTimeDisabledCondition.value)
      return (
        time.getTime() < new Date(form.value.startTime).getTime() &&
        time.getTime() < endTimeDisabledCondition.value
      )
    return time.getTime() < new Date(form.value.startTime).getTime()
  }

  const remainCapacity = ref(0)
  const fetchRemainCapacity = async () => {
    const res = await getAvailableCapacity()
    remainCapacity.value = res.data || 0
  }

  defineExpose({
    openDialog
  })
</script>

<style lang="scss" scoped>
  .total-price {
    display: flex;
    align-items: center;
    font-size: 18px;
    font-weight: bold;
  }

  .sub-title {
    display: flex;
    align-items: center;
    border-left: 5px solid #64b9ff;
    background-color: #f2f2f2;
    margin: 15px 10px;
    height: 30px;
    padding-left: 10px;
    font-size: 15px;
  }

  .svg-icon {
    margin-left: 10px;
  }

  .available-capacity-tips {
    margin: 0 0 15px 5px;
  }
</style>
