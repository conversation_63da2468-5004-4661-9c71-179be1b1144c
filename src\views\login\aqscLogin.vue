<!--
 * @Description: 安全生产登录页面登录
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-04-22 15:08:42
 * @LastEditTime: 2024-04-22 16:10:38
-->

<template>
  <div
    class="login"
    :style="
      tenantBgImg
        ? `background-image: url(${tenantBgImg}) !important`
        : `background-image: url(https://training-voc.obs.cn-north-4.myhuaweicloud.com:443/PC端背景.jpg) !important`
    "
  >
    <div class="login_main">
      <el-form
        ref="unepLoginRef"
        :model="loginForm"
        :rules="rules"
        class="login-form"
      >
        <div class="form-content">
          <el-form-item prop="mobile">
            <div class="tel">
              <el-input v-model="loginForm.mobile" round auto-complete="off">
                <template #prefix>
                  <el-icon :size="22"> <User /></el-icon>
                </template>
              </el-input>
            </div>
          </el-form-item>
          <el-form-item prop="code">
            <div class="code">
              <el-input
                v-model="loginForm.code"
                auto-complete="off"
                @keyup.enter="handleLogin"
              >
                <template #prefix>
                  <el-icon :size="22"> <Lock /></el-icon>
                </template>
                <template #suffix>
                  <div class="end-inner">
                    <el-divider direction="vertical"></el-divider>
                    <div
                      class="end-type"
                      @click="getTelCode"
                      v-if="!sendMsgDisabled"
                      >获取验证码</div
                    >
                    <div class="end-interval" v-if="sendMsgDisabled">{{
                      time + "秒后获取"
                    }}</div>
                  </div>
                </template>
              </el-input>
            </div>
          </el-form-item>
          <el-form-item style="width: 100%">
            <div class="login-btn" @click.prevent="handleLogin">
              <img class="login-img" src="@/assets/images/login_btn.png" alt=""
            /></div>
          </el-form-item>
          <div class="tip" v-if="activeTab === '2'"
            >未注册的手机号将默认开通平台账号</div
          >
        </div>
      </el-form>
    </div>
  </div>
</template>

<script setup>
  import { sendCode } from "@/api/login"
  import useUserStore from "@/store/modules/user"
  import { getToken } from "@/utils/auth"
  import useTenantStore from "@/store/modules/tenant"
  import { ElMessage } from "element-plus"

  const tenantStore = useTenantStore()
  const userStore = useUserStore()
  const router = useRouter()
  const { proxy } = getCurrentInstance()

  const { tenantBgImg } = storeToRefs(tenantStore)
  const loginForm = ref({
    username: "",
    password: "",
    mobile: "",
    code: ""
  })

  const rules = {
    username: [{ required: true, trigger: "blur", message: "请输入您的账号" }],
    password: [{ required: true, trigger: "blur", message: "请输入您的密码" }],
    code: [{ required: true, trigger: "change", message: "请输入验证码" }]
  }

  const loading = ref(false)
  // 注册开关
  const redirectUrl = ref(null)
  let time = ref(60)
  let sendMsgDisabled = ref(false)

  const handleLogin = () => {
    proxy.$refs.unepLoginRef.validate(valid => {
      if (valid) {
        loading.value = true
        loginForm.value.password = loginForm.value.code
        userStore
          .unepLogin(loginForm.value)
          .then(() => {
            if (redirectUrl.value.indexOf("http") !== -1) {
              window.location.href =
                redirectUrl.value + "?" + "token=" + getToken()
            } else {
              router.push({ path: "/" })
            }
          })
          .catch(() => {
            loading.value = false
          })
      }
    })
  }

  // redirect是获取哪个系统域名 比如:http://127.0.0.1:8080 方便登录成功以后跳转相应的系统
  if (window.location.href.indexOf("redirect") >= 0) {
    //如果url中包含redirect   split？分割成数组，取第二个
    let redirect = window.location.href.split("?")[1]
    redirect = redirect.substring(9) // 截取字符串第9位开始截取到最后
    redirectUrl.value = redirect
  }

  const getTelCode = async () => {
    let queryData = {
      mobile: loginForm.value.mobile,
      scene: 1
    }
    await sendCode(queryData)
    ElMessage({
      message: "验证码发送成功！请注意查看",
      type: "success"
    })
    sendMsgDisabled.value = true
    const interval = setInterval(() => {
      if (time.value-- <= 0) {
        time.value = 60
        sendMsgDisabled.value = false
        clearInterval(interval)
      }
    }, 1000)
  }
</script>

<style lang="scss" scoped>
  :deep(.el-input__wrapper) {
    border: 1px solid #b5b6b6;
    border-radius: 5px;
    box-shadow: none;
  }
  :deep(.el-divider) {
    height: 2.2em;
    border-left: 1px solid #898989;
  }
  .login {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    background-size: 100% auto;
    background-position: bottom;
    background-repeat: no-repeat;

    .login_main {
      width: 500px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      position: absolute;
      top: 30%;
      right: 150px;
      background-color: #f7f7f7;
      padding: 5px;
      border-radius: 5px;
    }
  }

  .login-form {
    width: 100%;
    height: 100%;
    background-color: #eef3f6;
    border-radius: 5px;

    .form-content {
      margin-top: 30px;
      padding: 15px;
    }
    .el-input {
      height: 50px;
      input {
        height: 50px;
      }
    }
    .input-icon {
      height: 39px;
      width: 14px;
      margin-left: 15px;
    }
    .tel {
      display: flex;
      width: 100%;
      align-items: center;

      .front-inner {
        display: flex;
        align-items: center;
        justify-content: center;
        .front-type {
          color: #7f7f7f;
        }
      }
    }

    .code {
      display: flex;
      width: 100%;
      align-items: center;

      .end-inner {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
        .end-type {
          padding-left: 5px;
          color: #898989;
          cursor: pointer;
        }

        .end-interval {
          color: #ccc;
        }
      }
    }

    .login-btn {
      padding-top: 20px;
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      .login-img {
        cursor: pointer;
        width: 230px;
      }
      .login-img:hover {
        content: url("@/assets/images/login_btn_hover.png");
      }
    }

    .tip {
      font-size: 12px;
      text-align: center;
      width: 100%;
      color: #aaaaaa;
      margin-top: -10px;
      margin-bottom: 20px;
    }
  }
</style>
