<!--
 * @Description: 选择试卷弹窗
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-03-06 16:01:30
 * @LastEditTime: 2025-06-27 13:53:55
-->
<template>
  <div class="app-container">
    <el-dialog
      v-model="dialogVisible"
      title="选择试卷"
      width="50%"
      top="110px"
      @close="close"
      :key="new Date().getTime()"
      center
    >
      <el-form
        :model="queryParams"
        ref="queryRef"
        :inline="true"
        v-show="showSearch"
      >
        <el-form-item label="试卷名称" prop="paperName">
          <el-input
            v-model="queryParams.paperName"
            placeholder="请输入试卷名称"
            clearable
            style="width: 240px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="试卷目录" prop="catalogueId">
          <el-tree-select
            v-model="queryParams.catalogueId"
            :data="catalogueOptions"
            :props="{
              value: 'catalogueId',
              label: 'catalogueName',
              children: 'children',
              disabled: 'disabled'
            }"
            clearable
            value-key="catalogueId"
            placeholder="选择试卷目录"
            check-strictly
            default-expand-all
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-table
        ref="choosePaperTableRef"
        v-loading="loading"
        :data="tableData"
        highlight-current-row
        @current-change="handleCurrentChange"
      >
        <el-table-column label="试卷名称" prop="paperName" />
        <el-table-column label="试卷总分" prop="paperScore">
          <template #default="scope"> {{ scope.row.paperScore }}分 </template>
        </el-table-column>
        <el-table-column label="所属目录" prop="catalogueName" />
        <el-table-column label="备注" prop="remark" />
      </el-table>

      <pagination
        class="dialog-pagination"
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
      <template #footer>
        <el-button @click="close">取消</el-button>
        <el-button type="primary" @click="save">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="choosePaperDialog">
  import { listPaper } from "@/api/onlineExam/paper"
  import { catalogueList } from "@/api/system/catalogue.js"
  import catalogue from "@/utils/catalogue.js"

  const emit = defineEmits(["fetch-data"])
  const { proxy } = getCurrentInstance()

  const tableData = ref([])
  const loading = ref(true)
  const showSearch = ref(true)
  const dialogVisible = ref(false)
  const total = ref(0)
  const catalogueOptions = ref([])
  const currentRow = ref()

  const data = reactive({
    queryParams: {
      pageNum: 1,
      pageSize: 10
    }
  })

  const { queryParams } = toRefs(data)

  //** 弹框打开事件 */
  const openDialog = () => {
    dialogVisible.value = true
    getList()
    getTreeselect()
  }

  const handleCurrentChange = val => {
    currentRow.value = val
  }

  /** 查询目录列表 */
  function getList() {
    loading.value = true
    listPaper(queryParams.value).then(response => {
      tableData.value = response.rows || []
      total.value = response.total
      loading.value = false
    })
  }

  /** 查询目录下拉树结构 */
  function getTreeselect() {
    catalogueOptions.value = []
    catalogueList({ catalogueType: catalogue.PAPER_CATALOGUE }).then(
      response => {
        const paperCatalogue = {
          catalogueId: 0,
          catalogueName: "主类目",
          children: [],
          disabled: true
        }
        paperCatalogue.children = proxy.handleTree(response.rows, "catalogueId")
        catalogueOptions.value.push(paperCatalogue)
      }
    )
  }
  /** 搜索按钮操作 */
  function handleQuery() {
    getList()
  }
  /** 重置按钮操作 */
  function resetQuery() {
    proxy.resetForm("queryRef")
    handleQuery()
  }
  // 关闭弹框并重置操作
  const close = () => {
    proxy.resetForm("queryRef")
    dialogVisible.value = false
  }
  const save = () => {
    emit("fetch-data", currentRow.value)
    proxy.$modal.msgSuccess("操作成功")
    close()
  }

  defineExpose({
    openDialog
  })
</script>

<style lang="scss" scoped>
  :deep(.pagination-container .el-pagination) {
    position: static;
  }
</style>
