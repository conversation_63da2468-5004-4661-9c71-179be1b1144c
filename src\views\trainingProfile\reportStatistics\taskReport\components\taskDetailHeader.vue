<!--
 * @Description: 任务实施报表头部组件
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-10-30 17:17:32
 * @LastEditTime: 2025-01-15 10:14:21
-->
<template>
  <div class="border border-gray-200 mb-10 rounded-md shadow-sm">
    <div class="bg-gray-50 px-6 border-b border-gray-200 flex items-center">
      <h2 class="text-xl font-bold text-gray-800">{{ detailInfo.taskName }}</h2>

      <div class="w-300px ml-5">
        <el-progress
          :percentage="
            Math.floor(
              (detailInfo.completeNum / detailInfo.authorizeNum) * 100
            ) || 0
          "
          :stroke-width="12"
        />
      </div>
    </div>
    <div class="p-6">
      <el-row class="mb-6">
        <el-col :span="8">
          <div class="text-gray-600">任务时间：{{ detailInfo.startTime }}</div>
        </el-col>
        <el-col :span="8">
          <div class="text-gray-600">发布于：{{ detailInfo.createTime }}</div>
        </el-col>
        <el-col :span="8">
          <div class="text-gray-600">发布人：{{ detailInfo.createBy }}</div>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <div class="text-gray-600"
            >授权人数：{{ detailInfo.authorizeNum || 0 }}</div
          >
        </el-col>
        <el-col :span="8">
          <div class="text-gray-600"
            >完成人数：{{ detailInfo.completeNum || 0 }}</div
          >
        </el-col>
        <el-col :span="8">
          <div class="text-gray-600"
            >获证人数：{{ detailInfo.hasCertificateNum || 0 }}</div
          >
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup name="taskDetailHeader">
  const props = defineProps({
    detailInfo: {
      type: Object,
      default: {}
    }
  })

  const { detailInfo } = toRefs(props)
</script>

<style lang="scss" scoped>
  :deep(.el-progress-bar__outer) {
    background-color: #f0f0f0 !important;
    border-radius: 8px;
  }

  :deep(.el-progress-bar__inner) {
    border-radius: 8px;
    background: linear-gradient(90deg, #409eff 0%, #95c8ff 100%);
  }

  :deep(.el-progress .el-progress__text) {
    margin-left: 15px !important;
  }
</style>
