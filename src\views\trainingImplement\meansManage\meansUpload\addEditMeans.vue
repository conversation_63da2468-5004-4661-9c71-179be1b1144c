<!--
 * @Description: 资料上传
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-03-08 11:28:06
 * @LastEditTime: 2024-05-07 16:52:04
-->
<template>
  <div class="app-container">
    <el-form
      :model="form"
      ref="formRef"
      label-width="100px"
      :rules="rules"
      :inline="true"
    >
      <el-row :gutter="10">
        <el-col :span="24">
          <el-form-item label="资料目录" prop="catalogueId">
            <el-tree-select
              v-model="form.catalogueId"
              :data="catalogueOptions"
              :props="{
                value: 'catalogueId',
                label: 'catalogueName',
                children: 'children',
                disabled: 'disabled'
              }"
              value-key="catalogueId"
              placeholder="选择资料目录"
              check-strictly
              @current-change="catalogueSelect"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="资料名称" prop="manageName">
            <el-input v-model="form.manageName" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="资料类型" prop="manageType">
            <el-select
              v-model="form.manageType"
              placeholder="资料类型"
              clearable
              style="width: 200px"
            >
              <el-option
                v-for="dict in sys_flie_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item
            label="资料文件"
            prop="manageAddress"
            v-if="form.manageType"
          >
            <FileUpload
              :limit="1"
              v-model="form.manageAddress"
              :fileType="FILE_TYPE[form.manageType]"
              :fileSize="2048"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="关键字">
            <el-input v-model="form.manageTitle" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="资料简介">
            <el-input
              v-model="form.profile"
              :autosize="{ minRows: 2, maxRows: 4 }"
              type="textarea"
            />
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="资料封面">
            <ImageUpload v-model="form.cover" :limit="1" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="高级选项">
            <el-checkbox
              v-model="form.isDownload"
              true-label="1"
              false-label="2"
              label="允许下载"
            />
            <el-checkbox
              v-model="form.isIntroduction"
              true-label="1"
              false-label="2"
              label="推荐"
            />
            <el-checkbox
              v-model="form.isOpen"
              true-label="1"
              false-label="2"
              label="公开"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24" v-if="form.isOpen === '2'">
          <el-form-item label="授权学员" prop="dataUserNames">
            <el-input v-model="form.dataUserNames" disabled />
            <el-button
              type="primary"
              class="chooseMeansUser"
              @click="chooseMeansUserClick"
              >选择授权学员</el-button
            >
          </el-form-item>
        </el-col>
        <el-col :span="24" class="buttonCol">
          <el-form-item>
            <el-button type="primary" @click="submitForm">提交</el-button>
            <el-button @click="handleBack">返回</el-button>
            <el-button @click="reset">重置</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <chooseMeansUser
      ref="chooseMeansUserDialogRef"
      @fetch-data="chooseMeansUserDone"
    />
  </div>
</template>

<script setup name="meansUpload">
  import {
    addMeans,
    getMeans,
    updateMeans
  } from "@/api/trainingImplement/means"
  import { catalogueList } from "@/api/system/catalogue.js"
  import catalogue from "@/utils/catalogue.js"
  import chooseMeansUser from "./components/chooseMeansUser"
  import { FILE_TYPE } from "@/utils/constant.js"

  const route = useRoute()
  const { proxy } = getCurrentInstance()
  const { sys_flie_type } = proxy.useDict("sys_flie_type")

  const catalogueOptions = ref([])
  const flag = ref("add")

  const data = reactive({
    form: {
      isDownload: "2",
      isIntroduction: "2",
      isOpen: "2"
    },
    rules: {
      catalogueId: [
        { required: true, message: "资料目录不能为空", trigger: "change" }
      ],
      manageName: [
        { required: true, message: "资料名称不能为空", trigger: "change" }
      ],
      manageType: [
        { required: true, message: "资料类型不能为空", trigger: "change" }
      ],
      manageAddress: [
        { required: true, message: "资料文件不能为空", trigger: "change" }
      ],
      dataUserNames: [
        { required: true, message: "授权学员不能为空", trigger: "change" }
      ]
    }
  })
  const { form, rules } = toRefs(data)

  if (route.query.flag !== "add" && Object.keys(route.query).length) {
    getMeans(route.query.manageId).then(res => {
      form.value = res.data
      flag.value = "edit"
    })
  }

  /** 查询菜单下拉树结构 */
  function getTreeselect() {
    catalogueOptions.value = []
    catalogueList({ catalogueType: catalogue.MEANS_CATALOGUE }).then(
      response => {
        const catalogueTree = {
          catalogueId: 0,
          catalogueName: "主类目",
          children: [],
          disabled: true
        }
        catalogueTree.children = proxy.handleTree(response.rows, "catalogueId")
        catalogueOptions.value.push(catalogueTree)
      }
    )
  }

  // 选择授权学员
  const chooseMeansUserClick = () => {
    proxy.$refs["chooseMeansUserDialogRef"].openDialog(form.value.dataUserIds)
  }
  // 授权学员选择完成
  const chooseMeansUserDone = item => {
    if (!item || item.length === 0) return
    form.value.dataUserNames = item.map(user => user.userName).join()
    form.value.dataUserIds = item.map(user => user.userId).join()
  }

  const submitForm = () => {
    proxy.$refs["formRef"].validate(valid => {
      if (valid) {
        if (flag.value === "add") {
          addMeans(form.value).then(res => {
            if (res.code === 200) {
              reset()
              proxy.$modal.msgSuccess("新增成功")
              proxy.$tab.closeOpenPage({
                path: "/trainingImplement/meansManage"
              })
            }
          })
        } else {
          updateMeans(form.value).then(res => {
            if (res.code === 200) {
              reset()
              proxy.$modal.msgSuccess("修改成功")
              proxy.$tab.closeOpenPage({
                path: "/trainingImplement/meansManage"
              })
            }
          })
        }
      }
    })
  }

  const catalogueSelect = (data, node) => {
    form.value.catalogueName = data.catalogueName
  }

  /** 表单重置 */
  function reset() {
    form.value = {
      isDownload: "2",
      isIntroduction: "2",
      isOpen: "2"
    }
  }
  const handleBack = () => {
    proxy.$tab.closeOpenPage({
      path: "/trainingImplement/meansManage"
    })
  }

  getTreeselect()
</script>
<style lang="scss" scoped>
  :deep(.el-textarea__inner) {
    width: 300px;
  }

  .buttonCol {
    margin-left: 100px;
  }

  :deep(.el-input) {
    display: inline-block;
    width: auto;
  }

  .chooseMeansUser {
    margin-left: 20px;
  }
</style>
