<template>
  <div class="upload-file">
    <el-upload multiple :action="uploadFileUrl" :before-upload="handleBeforeUpload" :http-request="handleUpload"
      :file-list="fileList" :limit="limit" :on-error="handleUploadError" :on-exceed="handleExceed"
      :on-success="handleUploadSuccess" :on-progress="onProgress" :show-file-list="false" :headers="headers"
      class="upload-file-uploader" ref="fileUpload" v-if="!disabled">
      <!-- 上传按钮 -->
      <el-button type="primary">选取文件</el-button>
    </el-upload>
    <!-- 上传提示 -->
    <div class="el-upload__tip" v-if="showTip && !disabled">
      请上传
      <template v-if="fileSize">
        大小不超过 <b style="color: #f56c6c">{{ fileSize }}MB</b>
      </template>
      <template v-if="fileType">
        格式为 <b style="color: #f56c6c">{{ fileType.join("/") }}</b>
      </template>
      的文件
    </div>
    <!-- 文件列表 -->
    <transition-group class="upload-file-list el-upload-list el-upload-list--text" name="el-fade-in-linear" tag="ul">
      <li :key="file.uid" class="el-upload-list__item ele-upload-list__item-content" v-for="(file, index) in fileList">
        <el-link :href="canDownload ? file.url : ''" :underline="false" target="_blank">
          <span class="el-icon-document"> {{ file.name }} </span>
        </el-link>
        <div class="ele-upload-list__item-content-action">
          <el-link v-if="!disabled" :underline="false" @click="handleDelete(index)" type="danger">删除</el-link>
        </div>
      </li>
    </transition-group>
    <el-progress v-show="videoFlag" :percentage="videoUploadPercent">
    </el-progress>
  </div>
</template>

<script setup>
import { getToken } from "@/utils/auth"
import { nextTick } from "vue"
import ObsClient from "esdk-obs-browserjs/src/obs"

const props = defineProps({
  modelValue: [String, Object, Array],
  // 数量限制
  limit: {
    type: Number,
    default: 5
  },
  // 大小限制(MB)
  fileSize: {
    type: Number,
    default: 5
  },
  // 文件类型, 例如['png', 'jpg', 'jpeg']
  fileType: {
    type: Array,
    default: () => ["doc", "docx", "xls", "xlsx", "ppt", "txt", "pdf"]
  },
  // 是否显示提示
  isShowTip: {
    type: Boolean,
    default: true
  },
  // 是否禁用
  disabled: {
    type: Boolean,
    default: false
  },
  // 是否可以下载
  canDownload: {
    type: Boolean,
    default: true
  },
  // 是否获取文件数组
  isGetFileArray: {
    type: Boolean,
    default: false
  }
})
const obsClient = new ObsClient({
  access_key_id: "I5CROPRZ7WFRHNCILVMP",
  secret_access_key: "lcr5k2ereEVEvjp0TQAgmIeugQGHHLz680n2NeoM",
  server: "obs.cn-north-4.myhuaweicloud.com",
  // timeout: 900 // 15分钟
  timeout: 1200 // 20分钟，这个是设置超时时间，我在本地测试上传1G的视频大概需要15分钟
})

const { proxy } = getCurrentInstance()
const emit = defineEmits()
const number = ref(0)
const uploadList = ref([])
const uploadFileUrl = ref(import.meta.env.VITE_APP_BASE_API + "/file/upload") // 上传文件服务器地址
const headers = ref({ Authorization: "Bearer " + getToken() })
const fileList = ref([])
const videoFlag = ref(false)
const videoUploadPercent = ref([0])

const showTip = computed(
  () => props.isShowTip && (props.fileType || props.fileSize)
)

const percent = ref(0)
const color = ref("#409EFF")

watch(
  () => props.modelValue,
  val => {
    videoFlag.value = false
    videoUploadPercent.value = 0
    if (val) {
      let temp = 1
      // 首先将值转为数组
      const list = Array.isArray(val) ? val : props.modelValue.split(",")
      // 然后将数组转为对象数组
      fileList.value = list.map(item => {
        if (typeof item === "string") {
          item = { name: getFileName(item), url: item }
        }
        item.uid = item.uid || new Date().getTime() + temp++
        return item
      })
    } else {
      fileList.value = []
      return []
    }
  },
  { deep: true, immediate: true }
)
function handleUpload(fileObj) {
  videoUploadPercent.value = 0
  obsClient.putObject(
    {
      Bucket: "training-voc", //桶名，公司运维或者负责人给你提供
      Key: `${fileObj.file.name}`,
      SourceFile: fileObj.file,
      ProgressCallback: function (
        transferredAmount,
        totalAmount,
        totalSeconds
      ) {
        nextTick(() => {
          // 进度条 备用
          videoFlag.value = true
          videoUploadPercent.value = Math.floor(
            (transferredAmount * 100.0) / totalAmount
          )
        })
      }
    },
    (err, result) => {
      if (err) {
        proxy.$modal.msgError(`上传失败!`)
      } else {
        let fileName = `https://training-voc.obs.cn-north-4.myhuaweicloud.com/${fileObj.file.name}`
        fileList.value.push({
          name: fileObj.file.name,
          uid: fileObj.uid,
          url: fileName
        })
        emit("update:modelValue", listToString(fileList.value))
        if (props.isGetFileArray) {
          emit("getFileArray", fileList.value)
        }
      }
    }
  )
}
// 上传前校检格式和大小
function handleBeforeUpload(file) {
  // 校检文件类型
  if (props.fileType.length) {
    const fileName = file.name.split(".")
    const fileExt = fileName[fileName.length - 1].toLowerCase()

    // 检查是否为不允许的 .doc 或 .ppt 文件
    if (fileExt === "doc") {
      proxy.$modal.msgError("不支持上传 .doc 文件，请上传 .docx 格式文件!")
      return false
    }
    if (fileExt === "ppt") {
      proxy.$modal.msgError("不支持上传 .ppt 文件，请上传 .pptx 格式文件!")
      return false
    }

    const isTypeOk = props.fileType.indexOf(fileExt) >= 0
    if (!isTypeOk) {
      proxy.$modal.msgError(
        `文件格式不正确, 请上传${props.fileType.join("/")}格式文件!`
      )
      return false
    }
  }
  // 校检文件大小
  if (props.fileSize) {
    const isLt = file.size / 1024 / 1024 < props.fileSize
    if (!isLt) {
      proxy.$modal.msgError(`上传文件大小不能超过 ${props.fileSize} MB!`)
      return false
    }
  }

  const fileType = file.type
  const isVideo = fileType.startsWith("video/")
  if (isVideo) {
    // 如果是视频文件，可以获取视频高度和宽度
    const video = document.createElement("video")
    video.setAttribute("src", URL.createObjectURL(file))
    video.addEventListener("loadedmetadata", () => {
      const width = video.videoWidth
      const height = video.videoHeight
      const duration = video.duration
      emit("onVideoUploadSuccess", { width, height, duration })
    })
  }

  /* proxy.$modal.loading("正在上传文件，请稍候..."); */
  number.value++
  return true
}

// 上传过程
const onProgress = (event, file, fileList) => {
  // 文件上传过程中的进度回调函数
  percent.value = Math.round(event.percent)
}

// 文件个数超出
function handleExceed() {
  proxy.$modal.msgError(`上传文件数量不能超过 ${props.limit} 个!`)
}

// 上传失败
function handleUploadError(err) {
  proxy.$modal.msgError("上传文件失败")
}

// 上传成功回调
function handleUploadSuccess(res, file) {
  if (res.code === 200) {
    uploadList.value.push({ name: res.data.url, url: res.data.url })
    uploadedSuccessfully()
  } else {
    number.value--
    proxy.$modal.closeLoading()
    proxy.$modal.msgError(res.msg)
    proxy.$refs.fileUpload.handleRemove(file)
    uploadedSuccessfully()
  }
}

// 删除文件
function handleDelete(index) {
  fileList.value.splice(index, 1)
  emit("update:modelValue", listToString(fileList.value))
  if (props.isGetFileArray) {
    emit("getFileArray", fileList.value)
  }
}

// 上传结束处理
function uploadedSuccessfully() {
  if (number.value > 0 && uploadList.value.length === number.value) {
    /* fileList.value = fileList.value
      .filter(f => f.url !== undefined)
      .concat(uploadList.value) */
    uploadList.value = []
    number.value = 0
    /* emit("update:modelValue", listToString(fileList.value)) */
    proxy.$modal.closeLoading()
  }
}

// 获取文件名称
function getFileName(name) {
  name = decodeURIComponent(name)
  if (name.lastIndexOf("/") > -1) {
    return name.slice(name.lastIndexOf("/") + 1)
  } else {
    return ""
  }
}

// 对象转成指定字符串分隔
function listToString(list, separator) {
    let strs = ""
    separator = separator || ","
    for (let i in list) {
      if (list[i].url) {
        strs += list[i].url + separator
      }
    }
    return strs != "" ? strs.substr(0, strs.length - 1) : ""
  }
</script>

<style scoped lang="scss">
.upload-file-uploader {
  margin-bottom: 5px;
}

.upload-file-list .el-upload-list__item {
  border: 1px solid #e4e7ed;
  line-height: 2;
  margin-bottom: 10px;
  position: relative;
}

.upload-file-list .ele-upload-list__item-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: inherit;
}

.ele-upload-list__item-content-action .el-link {
  margin-right: 10px;
}

.el-icon-document {
    margin-left: 20px;
  }
</style>
