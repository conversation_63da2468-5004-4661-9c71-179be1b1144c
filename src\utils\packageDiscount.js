/*
 * @Description: 套餐实施-折扣map
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-08-28 11:09:28
 * @LastEditTime: 2023-08-28 15:34:58
 */
// =================================== 账号数量-折扣比例Map ===================================
// 创建一个 Map 结构
const discountMap = new Map()

// 添加账号数量和相应的折扣比例
discountMap.set({ min: 0, max: 100 }, 1.0) // 0-100: 100%
discountMap.set({ min: 101, max: 200 }, 0.95) // 101-200: 95%
discountMap.set({ min: 201, max: 500 }, 0.85) // 201-500: 85%
discountMap.set({ min: 501, max: 1000 }, 0.8) // 501-1000: 80%

// 示例函数，接受账号数量并返回折扣比例
export function getAccountDiscountRatio(accountCount) {
  let discountPercentage = 1.0 // 默认为 100%

  // 遍历 Map 中的键值对，找到符合条件的折扣比例
  for (const [range, discount] of discountMap) {
    if (accountCount >= range.min && accountCount <= range.max) {
      discountPercentage = discount
      break
    }
  }

  return discountPercentage
}

// =================================== 时长-折扣比例Map ===================================
// 创建一个 Map 结构，用于存储使用时长和对应的折扣比例
const durationDiscountMap = new Map([
  [180, 1.0], // 半年期内折扣比例为 100%
  [365, 0.95], // 1年期内折扣比例为 95%
  [730, 0.85], // 2年期内折扣比例为 85%
  [1095, 0.8], // 3年期内折扣比例为 80%
  [1460, 0.75] // 4年期内折扣比例为 75%
])

// 定义函数，根据使用时长返回对应的折扣比例
export function getDurationDiscountRatio(startDate, endDate) {
  const oneDay = 24 * 60 * 60 * 1000 // 1天的毫秒数
  const diffDays = Math.round((endDate - startDate) / oneDay)

  // 查找 Map 结构中对应的折扣比例
  for (const [days, ratio] of durationDiscountMap) {
    if (diffDays <= days) {
      return ratio
    }
    if (diffDays > 1460) {
      return 0.7
    }
  }

  return 1.0 // 默认返回最大折扣比例 100%
}
