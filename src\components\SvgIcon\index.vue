<template>
  <!-- 使用 sprite 图标 -->
  <svg v-if="!isLocalFile" :class="svgClass" aria-hidden="true">
    <use :xlink:href="iconName" :fill="color" />
  </svg>
  <!-- 使用本地 SVG 文件 -->
  <div v-else class="local-svg" :style="{ width: size, height: size }">
    <div 
      class="local-svg-icon"
      :class="className"
      :style="{ color }"
      v-html="svgContent"
    ></div>
  </div>
</template>

<script>
export default defineComponent({
  name: "SvgIcon",
  props: {
    iconClass: {
      type: String,
      required: true
    },
    className: {
      type: String,
      default: ""
    },
    color: {
      type: String,
      default: ""
    },
    size: {
      type: String,
      default: "32px"
    }
  },
  setup(props) {
    const isLocalFile = computed(() => props.iconClass.endsWith(".svg"))
    const svgContent = ref("")

    // 加载本地SVG内容
    const loadSvgContent = async () => {
      if (isLocalFile.value) {
        try {
          const response = await fetch(
            new URL(`../../assets/svg/${props.iconClass}`, import.meta.url).href
          )
          const text = await response.text()
          svgContent.value = text
        } catch (error) {
          console.error("Failed to load SVG:", error)
        }
      }
    }

    // 监听 iconClass 变化重新加载SVG
    watch(() => props.iconClass, loadSvgContent, { immediate: true })

    const iconName = computed(() => `#icon-${props.iconClass}`)
    const svgClass = computed(() => {
      if (props.className) {
        return `svg-icon ${props.className}`
      }
      return "svg-icon"
    })

    return {
      isLocalFile,
      svgContent,
      iconName,
      svgClass
    }
  }
})
</script>

<style scoped lang="scss">
.svg-icon {
  width: 1em;
  height: 1em;
  position: relative;
  fill: currentColor;
  vertical-align: -2px;
}

.local-svg {
  display: inline-block;
  position: relative;

  .local-svg-icon {
    width: 100%;
    height: 100%;
    
    :deep(svg) {
      width: 100%;
      height: 100%;
      fill: currentColor;
    }
  }
}

.sub-el-icon,
.nav-icon {
  display: inline-block;
  font-size: 15px;
  margin-right: 12px;
  position: relative;
}
</style>
