<!--
 * @Description: 新增/修改试卷
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-03-02 13:43:59
 * @LastEditTime: 2025-06-20 09:27:06
-->
<template>
  <div class="app-container">
    <el-form :model="form" ref="formRef" label-width="120px" :rules="rules">
      <el-form-item label="试卷目录" prop="catalogueId">
        <el-tree-select
          v-model="form.catalogueId"
          :data="catalogueOptions"
          :props="{
            value: 'catalogueId',
            label: 'catalogueName',
            children: 'children',
            disabled: 'disabled'
          }"
          value-key="catalogueId"
          placeholder="选择试卷目录"
          check-strictly
          @current-change="catalogueSelect"
          default-expand-all
        />
      </el-form-item>
      <el-form-item label="试卷名称" prop="paperName">
        <el-input v-model="form.paperName" />
      </el-form-item>
      <el-form-item label="组卷类型" prop="paperType">
        <el-select
          v-model="form.paperType"
          placeholder="组卷类型"
          clearable
          style="width: 200px"
          @change="paperTypeSelect"
        >
          <el-option
            v-for="dict in paper_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        label="是否关联课程"
        prop="associateCourse"
        v-if="form.paperType !== '3'"
      >
        <el-radio-group v-model="form.associateCourse">
          <el-radio :label="1">是</el-radio>
          <el-radio :label="2">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item
        label="关联课程"
        v-if="form.associateCourse === 1 && form.paperType !== '3'"
        class="mb-5"
      >
        <div class="mt-2 flex flex-wrap gap-2">
          <div
            v-for="course in selectedCourses"
            :key="course.courseId"
            class="bg-gray-100 h-9 rounded-3xl flex items-center pr-2 relative"
          >
            <el-avatar class="!m-1" :size="28">
              {{ course.courseName.substring(0, 1) }}
            </el-avatar>
            <span class="mx-2">{{ course.courseName }}</span>
            <el-icon
              class="ml-2 cursor-pointer hover:text-red-500"
              @click="handleRemoveCourse(course)"
            >
              <Close />
            </el-icon>
          </div>
          <el-button type="primary" link @click="openCourseSelect">
            <el-icon><Plus /></el-icon> 选择课程
          </el-button>
        </div>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="form.remark"
          type="textarea"
          :rows="3"
          placeholder="请输入备注"
          style="width: 400px"
        />
      </el-form-item>

      <!-- 固定组卷/随机组卷 -->
      <template
        v-if="form.paperType !== '3'"
        v-for="(item, index) in form.paperTactics"
      >
        <el-form-item
          :label="`题型${index + 1}`"
          prop="questionType"
          class="questionTypeChoose"
        >
          <el-select
            v-model="item.questionType"
            clearable
            placeholder="试题类型"
            @change="questionTypeChange($event, item, index)"
          >
            <el-option
              v-for="dict in exam_question_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
              :disabled="existType.includes(dict.value)"
            ></el-option>
          </el-select>
          <div class="extendButton">
            <el-button
              type="primary"
              @click="questionItemAdd(item, index)"
              :disabled="
                !item.questionType ||
                (form.associateCourse === 1 && selectedCourses.length === 0)
              "
              >添加题目</el-button
            >
            <el-button
              v-if="index !== 0"
              type="danger"
              @click="questionItemRemove(index)"
              >删除</el-button
            >
          </div>
          <el-card class="box-card" v-if="item.isShow">
            <template #header>
              <div class="card-header">
                <dict-tag
                  :options="exam_question_type"
                  :value="item.questionType"
                />
              </div>
            </template>
            <el-scrollbar height="300px">
              <div
                v-for="(question, indey) in item.examQuestions"
                :key="indey"
                class="item"
              >
                <div class="question_item">
                  <span class="text">
                    {{ indey + 1 }}. {{ question.questionName }}
                  </span>
                  <el-button
                    type="danger"
                    @click="deleteAssignedQuestion(index, indey)"
                    >删除</el-button
                  >
                </div>

                <el-divider />
              </div>
            </el-scrollbar>

            <div class="card-footer">
              <el-form-item
                label="抽取题数"
                prop="extractQuestionCount"
                style="margin-bottom: 10px"
                v-if="form.paperType === '2'"
              >
                <el-input-number
                  @change="extractQuestionCountChange(item)"
                  v-model="item.extractQuestionCount"
                  :min="1"
                  :max="999"
                />
              </el-form-item>
              <el-form-item label="每题分值" prop="questionScore">
                <el-input-number
                  v-model="item.questionScore"
                  :min="1"
                  :max="999"
                  :precision="1"
                  :step="0.5"
                />
              </el-form-item>
            </div>
          </el-card>
        </el-form-item>
      </template>

      <!-- 混合试卷 -->
      <template v-else v-for="(item, index) in form.paperTactics">
        <el-divider />
        <el-form-item
          :label="`题型${index + 1}`"
          :prop="`paperTactics.${index}.paperQuestionType`"
          :rules="rules.paperQuestionType"
          class="questionTypeChoose"
        >
          <el-select
            v-model="item.paperQuestionType"
            clearable
            placeholder="试题类型"
            @change="mixedPaperQuestionTypeChange($event, item)"
          >
            <el-option
              v-for="dict in exam_question_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
              :disabled="existType?.includes(dict.value)"
            ></el-option>
          </el-select>
          <div
            class="qs-total-num"
            v-if="item.questionTotalNum || item.questionTotalNum === 0"
          >
            该题型下共有{{ item.questionTotalNum }}道题
          </div>
          <div class="extendButton">
            <el-button
              v-if="index !== 0"
              type="danger"
              @click="questionItemRemove(index)"
              >删除</el-button
            >
          </div>
        </el-form-item>

        <template v-if="item.paperQuestionType">
          <el-form-item
            label="该题型试题数量"
            :prop="`paperTactics.${index}.totalQuestionCount`"
            :rules="rules.totalQuestionCount"
          >
            <el-input
              @blur="qsTypeCountBlur(item)"
              @input="v => (item.totalQuestionCount = v.replace(/\D/g, ''))"
              v-model="item.totalQuestionCount"
              maxlength="3"
            >
              <template #append>道</template>
            </el-input>
          </el-form-item>

          <el-form-item
            label="该题型每题分值"
            :prop="`paperTactics.${index}.questionScore`"
            :rules="rules.mixedQuestionScore"
          >
            <el-input
              @input="v => (item.questionScore = v.replace(/\D/g, ''))"
              v-model="item.questionScore"
              maxlength="2"
            >
              <template #append>分</template>
            </el-input>
          </el-form-item>
        </template>

        <el-form-item label="该题型总分数">
          {{
            Number(item.totalQuestionCount) * Number(item.questionScore) || 0
          }}
          分
        </el-form-item>
        <el-form-item
          label="试题目录"
          v-if="item.totalQuestionCount"
          :prop="`paperTactics.${index}.questionCatalogueId`"
          :rules="rules.questionCatalogueId"
        >
          <el-tree-select
            :ref="`treeRef${index}`"
            v-model="item.questionCatalogueId"
            :data="questionCatalogueOptions"
            :props="{
              value: 'questionCatalogueId',
              label: 'questionCatalogueName',
              children: 'children'
            }"
            :show-checkbox="true"
            multiple
            value-key="questionCatalogueId"
            placeholder="选择试题目录"
            check-strictly
            @check="checkChange(index)"
            default-expand-all
          />
        </el-form-item>

        <template
          v-if="item.qsCheckedNodesList && item.qsCheckedNodesList.length > 0"
          v-for="(item2, index2) in item.qsCheckedNodesList"
        >
          <div class="catalogue-name">
            <el-tag size="large">{{ item2.questionCatalogueName }}</el-tag>
            <div
              class="qs-total-num"
              v-if="
                item2.questionCatalogueNum || item2.questionCatalogueNum === 0
              "
            >
              该目录下共有{{ item2.questionCatalogueNum }}道题
            </div>
          </div>
          <el-form-item
            label="题目数量"
            :prop="`paperTactics.${index}.qsCheckedNodesList.${index2}.extractQuestionCount`"
            :rules="rules.mixedExtractQuestionCount"
          >
            <el-input
              @focus="questionCountFocus(item, item2)"
              @input="v => questionCountInput(v, item2)"
              @blur="questionCountBlur(item, item2)"
              v-model="item2.extractQuestionCount"
              maxlength="3"
            >
              <template #append>道</template>
            </el-input>
            <div class="extendText">
              占比
              {{
                (item2.extractQuestionCount / item.totalQuestionCount).toFixed(
                  2
                ) * 100 || 0
              }}% &nbsp;&nbsp;&nbsp;&nbsp;{{
                item2.extractQuestionCount * item.questionScore || 0
              }}分
            </div>
          </el-form-item>
        </template>
      </template>

      <el-form-item label="总分数"> {{ mixedTotalScore || 0 }}分 </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="submitForm">提交</el-button>
        <el-button @click="handleBack">返回</el-button>
        <el-button @click="reset">重置</el-button>
        <el-button type="primary" @click="addAnotherType">添加题型</el-button>
      </el-form-item>
    </el-form>
    <addQuestionDialog
      ref="addQuestionDialogRef"
      @fetch-data="addQuestionDone"
    />
    <ChooseCourseDialog
      ref="chooseCourseDialogRef"
      :selectionType="'multiple'"
      :disabledList="[]"
      @fetch-data="handleCourseSelected"
    />
  </div>
</template>

<script setup name="paperAdd">
  import {
    addPaper,
    getPaper,
    updatePaper,
    queryQuestionSum
  } from "@/api/onlineExam/paper"
  import { catalogueList } from "@/api/system/catalogue.js"
  import catalogue from "@/utils/catalogue.js"
  import addQuestionDialog from "./components/addQuestionDialog.vue"
  import ChooseCourseDialog from "@/components/ChooseCourseDialog/index.vue"
  import { Close, Plus } from "@element-plus/icons-vue"
  import { uniqBy } from "lodash-es"

  const route = useRoute()
  const { proxy } = getCurrentInstance()
  const { exam_question_type, paper_type } = proxy.useDict(
    "exam_question_type",
    "paper_type"
  )

  const catalogueOptions = ref([])
  const questionCatalogueOptions = ref([])
  const flag = ref("add")

  const existType = computed(() => {
    return form.value.paperTactics.map(item => {
      return item.questionType || item.paperQuestionType
    })
  })

  const data = reactive({
    form: {
      associateCourse: 2, // 默认选择否
      associateCourseIds: "",
      associateCourseNames: "",
      paperTactics: [
        {
          questionType: "",
          examQuestions: [],
          isShow: false
        }
      ]
    },
    rules: {
      catalogueId: [
        { required: true, message: "必填项不能为空", trigger: "change" }
      ],
      paperName: [
        { required: true, message: "必填项不能为空", trigger: "change" }
      ],
      paperType: [
        { required: true, message: "必填项不能为空", trigger: "change" }
      ],
      associateCourse: [
        { required: true, message: "必填项不能为空", trigger: "change" }
      ],
      paperQuestionType: [
        { required: true, message: "必填项不能为空", trigger: "change" }
      ],
      totalQuestionCount: [
        { required: true, message: "必填项不能为空", trigger: "change" }
      ],
      mixedQuestionScore: [
        { required: true, message: "必填项不能为空", trigger: "change" }
      ],
      questionCatalogueId: [
        { required: true, message: "必填项不能为空", trigger: "change" }
      ],
      mixedExtractQuestionCount: [
        { required: true, message: "必填项不能为空", trigger: "change" }
      ]
    }
  })
  const { form, rules } = toRefs(data)

  // 选中的课程列表
  const selectedCourses = ref([])

  if (Object.keys(route.query).length !== 0) {
    getPaper(route.query.paperId).then(async res => {
      form.value = JSON.parse(JSON.stringify(res.data))

      // 处理关联课程数据
      if (form.value.associateCourseIds) {
        const courseIds = form.value.associateCourseIds.split(",")
        const courseNames = form.value.associateCourseNames.split(",")
        selectedCourses.value = courseIds.map((id, index) => ({
          courseId: id,
          courseName: courseNames[index] || ""
        }))
      }

      if (form.value.paperType !== "3") {
        form.value.paperTactics.forEach(item => {
          item.questionType = proxy.selectDictValue(
            exam_question_type.value,
            item.paperTacticsName
          )
          item.isShow = true
        })
      } else {
        await getQuestionTreeselect()
        form.value.paperTactics = mergeArrayItems(res.data.paperTactics)
      }
      flag.value = "edit"
    })
  }

  // 题型切换
  const questionTypeChange = (value, item, index) => {
    form.value.paperTactics[index].isShow = false
    form.value.paperTactics[index].examQuestions = []
  }
  // 为指定题型添加题目
  const questionItemAdd = (item, index) => {
    // 检查是否关联课程且是否已选择课程
    if (
      form.value.associateCourse === 1 &&
      selectedCourses.value.length === 0
    ) {
      proxy.$modal.msgWarning("请先选择关联的课程")
      return
    }

    // 传入关联课程IDs作为参数
    const associateCourseIds =
      form.value.associateCourse === 1
        ? selectedCourses.value.map(course => course.courseId).join(",")
        : ""

    proxy.$refs["addQuestionDialogRef"].openDialog(
      item,
      index,
      associateCourseIds
    )
  }
  // 添加完成
  const addQuestionDone = (index, questionList) => {
    form.value.paperTactics[index].isShow = true
    form.value.paperTactics[index].examQuestions = questionList
  }
  // 删除某种题型
  const questionItemRemove = index => {
    form.value.paperTactics.splice(index, 1)
  }
  // 删除指定题型的某道题
  const deleteAssignedQuestion = (index, indey) => {
    form.value.paperTactics[index].examQuestions.splice(indey, 1)
    if (form.value.paperTactics[index].examQuestions.length == 0) {
      form.value.paperTactics[index].isShow = false
    }
  }

  // 添加另一种题型
  const addAnotherType = () => {
    form.value.paperTactics.push({
      questionType: "",
      examQuestions: []
    })
  }

  /** 查询菜单下拉树结构 */
  function getTreeselect() {
    catalogueOptions.value = []
    catalogueList({ catalogueType: catalogue.PAPER_CATALOGUE }).then(
      response => {
        const catalogueTree = {
          catalogueId: 0,
          catalogueName: "主类目",
          children: [],
          disabled: true
        }
        catalogueTree.children = proxy.handleTree(response.rows, "catalogueId")
        catalogueOptions.value.push(catalogueTree)
      }
    )
  }
  /** 查询菜单下拉树结构 */
  const getQuestionTreeselect = async () => {
    questionCatalogueOptions.value = []
    const response = await catalogueList({
      catalogueType: catalogue.SUBJECT_CATALOGUE
    })
    response.rows = response.rows.map(item => ({
      ...item,
      questionCatalogueId: item.catalogueId,
      questionCatalogueName: item.catalogueName
    }))
    const catalogueTree = {
      questionCatalogueId: 0,
      questionCatalogueName: "主类目",
      children: [],
      disabled: true
    }
    catalogueTree.children = proxy.handleTree(
      response.rows,
      "questionCatalogueId"
    )
    questionCatalogueOptions.value.push(catalogueTree)
  }

  // 提交
  const submitForm = () => {
    // 处理关联课程数据
    if (form.value.associateCourse === 1 && selectedCourses.value.length > 0) {
      form.value.associateCourseIds = selectedCourses.value
        .map(course => course.courseId)
        .join(",")
      form.value.associateCourseNames = selectedCourses.value
        .map(course => course.courseName)
        .join(",")
    } else {
      form.value.associateCourseIds = ""
      form.value.associateCourseNames = ""
    }

    if (form.value.paperType != "3") {
      try {
        form.value.paperTactics.forEach((item, index) => {
          item.questionId = []
          item.examQuestions.forEach(question => {
            item.questionId.push(question.questionId)
          })
          if (form.value.paperType === "2") {
            if (!item.extractQuestionCount || !item.questionScore) {
              throw new Error()
            }
          } else {
            if (!item.questionScore) {
              throw new Error()
            }
          }
          item.paperTacticsName = proxy.selectDictLabel(
            exam_question_type.value,
            item.questionType
          )
          item.paperQuestionType = item.questionType
          item.questionId = item.questionId.join()
        })
      } catch (error) {
        if (form.value.paperType === "2") {
          return proxy.$modal.msgWarning(
            "请确保每种题型的每题分值或抽取题数不为空"
          )
        } else {
          return proxy.$modal.msgWarning("请确保每种题型的每题分值不为空")
        }
      }
    }
    proxy.$refs["formRef"].validate(valid => {
      if (valid) {
        let requestData
        // 混合试卷特殊处理
        if (form.value.paperType == "3") {
          requestData = {
            ...form.value,
            paperTactics: transformedArray(form.value.paperTactics)
          }
        } else {
          requestData = form.value
        }
        if (flag.value === "add") {
          addPaper(requestData).then(res => {
            if (res.code === 200) {
              reset()
              proxy.$modal.msgSuccess("新增成功")
              proxy.$tab.closeOpenPage({
                path: "/onlineExam/paperManage"
              })
            }
          })
        } else {
          updatePaper(requestData).then(res => {
            if (res.code === 200) {
              reset()
              proxy.$modal.msgSuccess("修改成功")
              proxy.$tab.closeOpenPage({
                path: "/onlineExam/paperManage"
              })
            }
          })
        }
      }
    })
  }

  const catalogueSelect = (data, node) => {
    form.value.catalogueName = data.catalogueName
  }

  /** 表单重置 */
  function reset() {
    form.value = {
      associateCourse: 2,
      associateCourseIds: "",
      associateCourseNames: "",
      paperTactics: [
        {
          questionType: "",
          examQuestions: [],
          isShow: false
        }
      ]
    }
    selectedCourses.value = []
  }

  // 组卷类型发生改变
  const paperTypeSelect = val => {
    form.value.paperTactics = [
      {
        questionType: "",
        examQuestions: [],
        isShow: false
      }
    ]

    // 如果选择混合试卷，重置关联课程选项
    if (val === "3") {
      form.value.associateCourse = 2
      selectedCourses.value = []
      form.value.associateCourseIds = ""
      form.value.associateCourseNames = ""
    }

    if (val == "3") {
      if (questionCatalogueOptions.value.length > 0) return
      getQuestionTreeselect()
    }
  }

  // 混合试卷总分数计算
  const mixedTotalScore = computed(() => {
    if (!form.value.paperTactics || form.value.paperTactics.length === 0)
      return 0
    let total
    // 固定试卷总分计算逻辑
    if (form.value.paperType == "1") {
      total = form.value.paperTactics.reduce((pre, cur) => {
        return (
          pre +
          Number(cur.examQuestions.length || 0) * Number(cur.questionScore || 0)
        )
      }, 0)
    }
    // 随机组卷
    else if (form.value.paperType == "2") {
      total = form.value.paperTactics.reduce((pre, cur) => {
        return (
          pre +
          Number(cur.extractQuestionCount || 0) * Number(cur.questionScore || 0)
        )
      }, 0)
    }
    // 混合试卷总分计算逻辑
    else if (form.value.paperType == "3") {
      total = form.value.paperTactics.reduce((pre, cur) => {
        return (
          pre +
          Number(cur.totalQuestionCount || 0) * Number(cur.questionScore || 0)
        )
      }, 0)
    }
    return total
  })

  // 树选中值发生改变
  const checkChange = index => {
    const checkedNodes = proxy.$refs[`treeRef${index}`][0].getCheckedNodes()
    form.value.paperTactics[index].qsCheckedNodesList = JSON.parse(
      JSON.stringify(checkedNodes)
    )
  }

  // 变更混合试卷类型提交所需数据结构
  const transformedArray = originalArr => {
    let newArr = []
    originalArr.forEach(item => {
      item.qsCheckedNodesList.forEach(node => {
        newArr.push({
          paperTacticsId: node.paperTacticsId,
          questionCatalogueId: node.questionCatalogueId,
          questionCatalogueName: node.questionCatalogueName,
          paperQuestionType: item.paperQuestionType,
          totalQuestionCount: item.totalQuestionCount,
          questionScore: item.questionScore,
          extractQuestionCount: node.extractQuestionCount
        })
      })
    })
    return newArr
  }

  // 混合试卷类型详情接口获取数据合并整理便于展示
  const mergeArrayItems = originalArr => {
    const mergedArray = []

    originalArr.forEach(item => {
      const paperQuestionType = item.paperQuestionType
      const existingItem = mergedArray.find(
        mergedItem => mergedItem.paperQuestionType === paperQuestionType
      )

      if (existingItem) {
        existingItem.qsCheckedNodesList.push({
          paperTacticsId: item.paperTacticsId,
          extractQuestionCount: item.extractQuestionCount,
          questionCatalogueId: item.questionCatalogueId,
          questionCatalogueName: item.questionCatalogueName
        })
        if (Array.isArray(existingItem.questionCatalogueId)) {
          existingItem.questionCatalogueId.push(item.questionCatalogueId)
        } else {
          existingItem.questionCatalogueId = [item.questionCatalogueId]
        }
      } else {
        mergedArray.push({
          questionCatalogueId: [item.questionCatalogueId],
          paperQuestionType,
          totalQuestionCount: item.totalQuestionCount,
          questionScore: item.questionScore,
          qsCheckedNodesList: [
            {
              paperTacticsId: item.paperTacticsId,
              extractQuestionCount: item.extractQuestionCount,
              questionCatalogueId: item.questionCatalogueId,
              questionCatalogueName: item.questionCatalogueName
            }
          ]
        })
      }
    })

    return mergedArray
  }

  // 目录题目数量input事件
  const questionCountInput = (v, item) => {
    item.extractQuestionCount = v.replace(/\D/g, "")
  }

  // 混合试卷-目录题目数量blur事件
  const questionCountBlur = async (paperTacticsItem, qsCatalogueItem) => {
    // 获取该题型试题数量
    const total = paperTacticsItem.qsCheckedNodesList.reduce((pre, cur) => {
      return pre + Number(cur.extractQuestionCount || 0)
    }, 0)

    // 判断各目录试题总数是否超过该题型试题数量
    if (total > paperTacticsItem.totalQuestionCount) {
      proxy.$modal.msgWarning("各目录试题总数不可超过该题型试题数量")
      qsCatalogueItem.extractQuestionCount = null
      return
    }

    // 判断输入数量是否为0
    if (qsCatalogueItem.extractQuestionCount == 0) {
      proxy.$modal.msgWarning("试题数量不可为0")
      qsCatalogueItem.extractQuestionCount = null
      return
    }

    // 判断输入数量是否超过该目录下试题总数
    if (
      qsCatalogueItem.extractQuestionCount >
      qsCatalogueItem.questionCatalogueNum
    ) {
      proxy.$modal.msgWarning("输入数量超过该目录下试题总数")
      qsCatalogueItem.extractQuestionCount = null
      return
    }

    // 判断该题型下最后一个填的目录题目数量输入完毕后是否等于题型总数
    let notFilledCount = 0
    for (let i = 0; i < paperTacticsItem.qsCheckedNodesList.length; i++) {
      if (!paperTacticsItem.qsCheckedNodesList[i].extractQuestionCount) {
        notFilledCount++
      }
    }
    if (notFilledCount === 0 && total != paperTacticsItem.totalQuestionCount) {
      proxy.$modal.msgWarning("各目录试题总和应等于该题型试题数量")
      qsCatalogueItem.extractQuestionCount = null
      return
    }
  }

  // 混合试卷-该题型试题数量blur事件
  const qsTypeCountBlur = async paperTacticsItem => {
    // 判断输入数量是否为0
    if (paperTacticsItem.totalQuestionCount == 0) {
      proxy.$modal.msgWarning("试题数量不可为0")
      paperTacticsItem.totalQuestionCount = null
      return
    }

    // 判断输入数量是否超过该题型下试题总数
    if (
      paperTacticsItem.totalQuestionCount > paperTacticsItem.questionTotalNum
    ) {
      proxy.$modal.msgWarning("输入数量超过该题型下试题总数")
      paperTacticsItem.totalQuestionCount = null
      return
    }
  }

  // 混合试卷-试题类型change事件
  const mixedPaperQuestionTypeChange = async (val, item) => {
    let queryData = {
      questionType: val
    }
    const { data } = await queryQuestionSum(queryData)
    item.questionTotalNum = Number(data) || 0
  }

  // 混合试卷-目录下题目数量输入框focus事件
  const questionCountFocus = async (paperTacticsItem, catalogueItem) => {
    let queryData = {
      questionType: paperTacticsItem.paperQuestionType,
      catalogueId: catalogueItem.catalogueId
    }
    const { data } = await queryQuestionSum(queryData)
    catalogueItem.questionCatalogueNum = Number(data) || 0
  }

  // 随机试卷-抽取题数change事件
  const extractQuestionCountChange = item => {
    if (Number(item.extractQuestionCount) > item.examQuestions?.length) {
      proxy.$modal.msgWarning("抽取题数数量超过所选试题总数")
      item.extractQuestionCount = null
      return
    }
  }

  // 打开课程选择弹窗
  const openCourseSelect = () => {
    // 不传入已选择的课程ID，让弹窗只做新增操作
    proxy.$refs["chooseCourseDialogRef"].openDialog("")
  }

  // 处理课程选择结果
  const handleCourseSelected = courses => {
    if (Array.isArray(courses) && courses.length > 0) {
      // 将新选择的课程与已选择的课程合并，并去重
      const newCourses = courses.map(course => ({
        courseId: course.courseId,
        courseName: course.courseName
      }))

      // 使用lodash的uniqBy方法根据courseId去重
      selectedCourses.value = uniqBy(
        [...selectedCourses.value, ...newCourses],
        "courseId"
      )
    }
  }

  // 移除选中的课程
  const handleRemoveCourse = course => {
    const index = selectedCourses.value.findIndex(
      item => item.courseId === course.courseId
    )
    if (index > -1) {
      selectedCourses.value.splice(index, 1)
    }
  }

  const handleBack = () => {
    proxy.$tab.closeOpenPage({ path: "/onlineExam/paperManage" })
  }

  getTreeselect()
</script>
<style lang="scss" scoped>
  :deep(.el-tag .el-icon) {
    display: none;
  }
  :deep(.el-form-item__content) {
    display: block;
  }
  :deep(.el-input) {
    display: inline-block;
    width: auto;
  }

  .questionTypeChoose {
    position: relative;
  }
  .extendButton {
    position: absolute;
    left: 250px;
    top: 1px;
  }

  .extendText {
    position: absolute;
    left: 260px;
    top: 1px;
  }
  .box-card {
    width: 550px;
    margin: 10px 0;
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .card-footer {
      :deep(.el-input-number .el-input__wrapper) {
        padding-left: 50px;
        padding-right: 50px;
      }
    }

    .item {
      :deep(.el-divider--horizontal) {
        margin: 10px 0;
      }
      font-size: 14px;
      margin-bottom: 18px;

      .question_item {
        display: flex;
        justify-content: space-between;
      }
    }
  }

  .catalogue-name {
    margin-left: 30px;
    margin-bottom: 15px;
    display: flex;
    align-items: center;

    .qs-total-num {
      margin-left: 15px;
      font-size: 14px;
    }
  }
</style>
