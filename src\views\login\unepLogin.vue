<!--
 * @Description: 防灾减灾登录页面
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-11-07 16:43:08
 * @LastEditTime: 2024-11-06 14:57:36
-->
<template>
  <div
    class="login"
    :style="
      tenantBgImg
        ? `background-image: url(${tenantBgImg}) !important`
        : `background-image: url(https://training-voc.obs.cn-north-4.myhuaweicloud.com/background.jpg) !important`
    "
  >
    <div class="login_main">
      <el-form
        ref="unepLoginRef"
        :model="loginForm"
        :rules="unepLoginRules"
        class="login-form"
        style="padding: 50px 50px 10px 50px"
      >
        <div class="logo" style="margin-bottom: 30px">
          <div class="title">
            {{
              domainName === "yingji"
                ? "应急管理部宣传教育中心在线宣教平台"
                : tenantName
            }}
          </div>
        </div>
        <el-form-item prop="mobile">
          <div class="tel">
            <el-input
              v-model="loginForm.mobile"
              round
              auto-complete="off"
              placeholder="请输入您的手机号"
              onfocus="this.placeholder=''"
              onblur="this.placeholder='请输入您的手机号'"
              ><template #prefix>
                <div class="front-inner">
                  <div class="front-type">+86</div>
                  <el-divider direction="vertical"></el-divider>
                </div>
              </template>
            </el-input>
          </div>
        </el-form-item>
        <el-form-item prop="code">
          <div class="code">
            <el-input
              v-model="loginForm.code"
              auto-complete="off"
              placeholder="请输入验证码"
              @keyup.enter="handleUnepLogin"
            >
              <template #suffix>
                <div class="end-inner">
                  <el-divider direction="vertical"></el-divider>
                  <div
                    class="end-type"
                    @click="getTelCode"
                    v-if="!sendMsgDisabled"
                    >获取验证码</div
                  >
                  <div class="end-interval" v-if="sendMsgDisabled">{{
                    time + "秒后获取"
                  }}</div>
                </div>
              </template>
            </el-input>
          </div>
        </el-form-item>
        <el-form-item style="width: 100%">
          <el-button
            :loading="loading"
            size="large"
            type="primary"
            style="width: 100%; border-radius: 20px"
            @click.prevent="handleUnepLogin"
            class="unepLoginButton"
          >
            <span v-if="!loading">登 录</span>
            <span v-else>登 录 中...</span>
          </el-button>
        </el-form-item>
        <div class="tip">未注册的手机号将默认开通平台账号</div>
      </el-form>
    </div>
    <div class="tip-container">
      本次活动由上海柏科咨询管理有限公司提供技术支持
    </div>
  </div>
</template>

<script setup>
  import { sendCode } from "@/api/login"
  import useUserStore from "@/store/modules/user"
  import { getToken } from "@/utils/auth"
  import useTenantStore from "@/store/modules/tenant"
  import { ElMessage } from "element-plus"

  const tenantStore = useTenantStore()
  const userStore = useUserStore()
  const router = useRouter()
  const { proxy } = getCurrentInstance()

  const { domainName, tenantBgImg, tenantName } = storeToRefs(tenantStore)
  const loginForm = ref({
    username: "",
    password: "",
    mobile: "",
    code: ""
  })

  const unepLoginRules = {
    code: [{ required: true, trigger: "change", message: "请输入验证码" }]
  }

  const loading = ref(false)
  // 注册开关
  const redirectUrl = ref(null)

  let time = ref(60)
  let sendMsgDisabled = ref(false)

  const handleUnepLogin = () => {
    proxy.$refs.unepLoginRef.validate(valid => {
      if (valid) {
        loading.value = true
        // 调用action的登录方法
        loginForm.value.password = loginForm.value.code
        userStore
          .unepLogin(loginForm.value)
          .then(() => {
            if (redirectUrl.value.indexOf("http") !== -1) {
              window.location.href =
                redirectUrl.value + "?" + "token=" + getToken()
            } else {
              router.push({ path: "/" })
            }
          })
          .catch(() => {
            loading.value = false
          })
      }
    })
  }

  // redirect是获取哪个系统域名 比如:http://127.0.0.1:8080 方便登录成功以后跳转相应的系统
  if (window.location.href.indexOf("redirect") >= 0) {
    //如果url中包含redirect   split？分割成数组，取第二个
    let redirect = window.location.href.split("?")[1]
    redirect = redirect.substring(9) // 截取字符串第9位开始截取到最后
    redirectUrl.value = redirect
  }

  const getTelCode = async () => {
    let queryData = {
      mobile: loginForm.value.mobile,
      scene: 1
    }
    await sendCode(queryData)
    ElMessage({
      message: "验证码发送成功！请注意查看",
      type: "success"
    })
    sendMsgDisabled.value = true
    const interval = setInterval(() => {
      if (time.value-- <= 0) {
        time.value = 60
        sendMsgDisabled.value = false
        clearInterval(interval)
      }
    }, 1000)
  }
</script>

<style lang="scss" scoped>
  .login {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    background-size: cover;

    .login_main {
      position: absolute;
      top: 250px;
      right: 25%;
      padding: 30px 0;
    }
    .tip-container {
      position: absolute;
      bottom: 15px;
      right: 27%;
      font-size: 14px;
    }
  }
  .logo {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    .title {
      text-align: center;
      max-height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      color: #589cfd;
      font-size: 26px;
      font-weight: 700;
      height: 20px;
      margin-bottom: 18px;
    }
    .logo-img {
      max-height: 70px;
      max-width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      margin: 0px auto 30px auto;
      text-align: center;
      color: #707070;
    }
  }

  .login-form {
    border-radius: 6px;
    background: #ffffff;
    width: 400px;
    padding: 25px 25px 5px 25px;
    border: 1px solid #dcdfe6;
    box-shadow: 0px 0px 6px rgba(0, 0, 0, 0.34901960784313724);
    .el-input {
      height: 40px;
      input {
        height: 40px;
      }
    }
    .input-icon {
      height: 39px;
      width: 14px;
      margin-left: 0px;
    }
    .tel {
      display: flex;
      width: 100%;
      align-items: center;
      :deep(.el-input__wrapper) {
        // background-color: #f2f2f2;
        border-radius: 20px !important;

        .front-inner {
          display: flex;
          align-items: center;
          justify-content: center;
          .front-type {
            color: #7f7f7f;
          }
        }
      }
    }

    .code {
      display: flex;
      width: 100%;
      align-items: center;
      :deep(.el-input__wrapper) {
        padding-left: 30px;
        // background-color: #f2f2f2;
        border-radius: 20px !important;

        .end-inner {
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 15px;
          .end-type {
            color: #4baef5;
            cursor: pointer;
          }

          .end-interval {
            color: #ccc;
          }
        }
      }
    }

    .unepLoginButton:hover {
      background-color: #409eff;
      font-size: 16px;
    }

    .tip {
      font-size: 12px;
      text-align: center;
      width: 100%;
      color: #aaaaaa;
      margin-top: -10px;
      margin-bottom: 20px;
    }
  }
  .login-tip {
    font-size: 13px;
    text-align: center;
    color: #bfbfbf;
  }
  .login-code {
    width: 33%;
    height: 40px;
    float: right;
    img {
      cursor: pointer;
      vertical-align: middle;
    }
  }
  .el-login-footer {
    height: 40px;
    line-height: 40px;
    position: fixed;
    bottom: 0;
    width: 100%;
    text-align: center;
    color: black;
    font-family: Arial;
    font-size: 12px;
    letter-spacing: 1px;
  }
  .login-code-img {
    height: 40px;
    padding-left: 12px;
  }

  :deep(.el-input--large .el-input__wrapper) {
    margin-left: 10px;
    padding: 1px 1px;
  }

  :deep(.el-input__prefix) {
    padding-left: 10px;
  }

  :deep(.el-input--large .el-input__inner) {
    padding-left: 10px;
  }
</style>
