<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="套餐名" prop="packageName">
        <el-input
          v-model="queryParams.packageName"
          placeholder="请输入套餐名"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="使用部门" prop="deptId">
        <el-tree-select
          v-model="queryParams.deptId"
          :data="deptOptions"
          :props="{ value: 'id', label: 'label', children: 'children' }"
          value-key="id"
          placeholder="请选择部门"
          check-strictly
          clearable
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="状态"
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="dict in sys_job_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="使用时长" style="width: 308px">
        <el-date-picker
          v-model="dateRange"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="selectDate"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb-8px">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd"
          >新增</el-button
        >
      </el-col>
      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="packageList">
      <el-table-column label="套餐编号" prop="packageId" width="120" />
      <el-table-column label="使用部门" prop="deptName" />
      <el-table-column label="套餐名" prop="packageName" />
      <el-table-column label="账号数量" prop="accountCount" width="120" />
      <el-table-column label="视频容量" prop="availableCapacity" width="120">
        <template #default="scope">
          {{ scope.row.availableCapacity }}MB
        </template>
      </el-table-column>
      <el-table-column label="使用时长">
        <template #default="scope">
          {{ scope.row.startTime }} ~ {{ scope.row.endTime }}
        </template>
      </el-table-column>
      <el-table-column label="套餐价格" prop="finalPrice">
        <template #default="scope">
          {{ scope.row.finalPrice ? scope.row.finalPrice + "元" : "" }}
        </template>
      </el-table-column>
      <el-table-column label="状态" prop="status">
        <template #default="scope">
          <dict-tag :options="sys_job_status" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        width="220"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-button
            link
            type="primary"
            icon="Edit"
            @click="handleUpdate(scope.row)"
            >修改</el-button
          >
          <el-button
            link
            type="primary"
            icon="Delete"
            @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <addEditPackageImpDialog
      ref="addEditPackageImpDialogRef"
      @fetch-data="getList"
    />
  </div>
</template>

<script setup name="PackageImplement">
  import { deptTreeSelect } from "@/api/system/user"
  import { listPackageImp, delPackageImp } from "@/api/onlineCourse/package.js"
  import addEditPackageImpDialog from "./components/addEditPackageImpDialog.vue"

  const { proxy } = getCurrentInstance()
  const { sys_job_status } = proxy.useDict("sys_job_status")

  const loading = ref(false)
  const showSearch = ref(true)

  const queryParams = ref({
    pageNum: 1,
    pageSize: 10
  })

  let total = ref(0)
  let packageList = ref()
  let dateRange = ref([])
  const deptOptions = ref()

  /** 查询字典类型列表 */
  const getList = async () => {
    let queryData = {
      ...queryParams.value,
      parentId: 100,
      status: 0
    }
    const { rows } = await listPackageImp(queryData)
    packageList.value = rows
  }
  /** 搜索按钮操作 */
  function handleQuery() {
    queryParams.value.pageNum = 1
    getList()
  }
  /** 重置按钮操作 */
  function resetQuery() {
    queryParams.value = {
      pageNum: 1,
      pageSize: 10
    }
    dateRange.value = []
    proxy.resetForm("queryRef")
    handleQuery()
  }
  /** 新增按钮操作 */
  function handleAdd() {
    proxy.$refs["addEditPackageImpDialogRef"].openDialog()
  }
  /** 修改按钮操作 */
  const handleUpdate = async row => {
    proxy.$refs["addEditPackageImpDialogRef"].openDialog(row.implementId)
  }
  /** 删除按钮操作 */
  function handleDelete(row) {
    const packageId = row.implementId
    proxy.$modal
      .confirm('是否确认删除套餐实施编号为"' + packageId + '"的数据项？')
      .then(function () {
        return delPackageImp(packageId)
      })
      .then(() => {
        getList()
        proxy.$modal.msgSuccess("删除成功")
      })
      .catch(() => {})
  }

  const selectDate = () => {
    if (dateRange.value != null) {
      queryParams.value.queryTimeFrom = dateRange.value[0]
      queryParams.value.queryTimeTo = dateRange.value[1]
    } else {
      queryParams.value.queryTimeFrom = ""
      queryParams.value.queryTimeTo = ""
    }
  }

  /** 查询部门下拉树结构 */
  function getDeptTree() {
    deptTreeSelect().then(response => {
      deptOptions.value = response.data
    })
  }

  getList()
  getDeptTree()
</script>
