<!--
 * @Description: 新增试题页面
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-02-27 16:44:46
 * @LastEditTime: 2025-03-03 14:45:15
-->
<template>
  <div class="app-container">
    <el-form :model="form" ref="formRef" label-width="100px" :rules="rules">
      <el-form-item label="试题类型" prop="questionType">
        <el-select
          v-model="form.questionType"
          clearable
          placeholder="试题类型"
          @change="questionTypeChange"
          :disabled="flag === 'edit'"
        >
          <el-option
            v-for="dict in exam_question_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="试题目录" prop="catalogueId">
        <el-tree-select
          v-model="form.catalogueId"
          :data="catalogueOptions"
          :props="{
            value: 'catalogueId',
            label: 'catalogueName',
            children: 'children',
            disabled: 'disabled'
          }"
          value-key="catalogueId"
          placeholder="选择试题目录"
          check-strictly
          @current-change="catalogueSelect"
          default-expand-all
        />
      </el-form-item>
      <el-form-item label="题干" prop="questionName">
        <el-input
          type="textarea"
          autosize
          style="width: 400px"
          v-model="form.questionName"
          @blur="form.questionName = $event.target.value.trim()"
        />
        <el-button
          class="insert_fill"
          v-if="form.questionType === 'F'"
          type="primary"
          @click="insertFill"
          >插入填空</el-button
        >
      </el-form-item>

      <!-- 单选题或多选题 -->
      <template v-if="form.questionType === 'S' || form.questionType === 'M'">
        <el-form-item label="选项" porp="item1">
          <el-form-item
            v-for="(item, index) in items"
            :label="item.prefix"
            :key="item.prefix"
            label-width="50px"
            class="question-item-label"
          >
            <el-input
              v-model="item.prefix"
              style="width: 50px; display: inline-block"
            />
            <el-input
              v-model="form[`item${index + 1}`]"
              @blur="form[`item${index + 1}`] = $event.target.value.trim()"
              class="question-item-content-input"
              style="width: 300px"
            />
            <el-button
              v-if="index !== 0"
              type="danger"
              class="question-item-remove"
              @click="questionItemRemove(index)"
              >删除</el-button
            >
          </el-form-item>
          <el-button
            type="primary"
            class="question-item-remove"
            @click="questionItemAdd()"
            >添加选项</el-button
          >
        </el-form-item>

        <el-form-item label="正确答案" prop="answer">
          <el-radio-group
            v-model="form.answer"
            v-if="form.questionType === 'S'"
          >
            <el-radio
              v-for="item in items"
              :key="item.prefix"
              :label="item.prefix"
              >{{ item.prefix }}</el-radio
            >
          </el-radio-group>

          <el-checkbox-group
            v-model="form.answer"
            v-else-if="form.questionType === 'M'"
          >
            <el-checkbox
              v-for="item in items"
              :key="item.prefix"
              :label="item.prefix"
            />
          </el-checkbox-group>
        </el-form-item>
      </template>
      <!-- 判断题 -->
      <template v-else-if="form.questionType === 'J'">
        <el-form-item label="选项" prop="item1">
          <el-form-item
            :label="item.prefix"
            :key="item.prefix"
            v-for="(item, index) in items"
            label-width="50px"
            class="question-item-label"
          >
            <el-input v-model="item.prefix" style="width: 50px" disabled />
            <el-input
              v-model="form[`item${index + 1}`]"
              class="question-item-content-input"
              disabled
            />
          </el-form-item>
        </el-form-item>

        <el-form-item label="正确答案" prop="answer">
          <el-radio-group v-model="form.answer">
            <el-radio
              v-for="item in items"
              :key="item.prefix"
              :label="item.prefix"
              >{{ item.prefix }}</el-radio
            >
          </el-radio-group>
        </el-form-item>
      </template>
      <!-- 填空题 -->
      <template v-else-if="form.questionType === 'F'">
        <template v-for="(item, index) in fillCount">
          <el-form-item :label="`填空${item}答案`" prop="answer">
            <el-input
              v-model="fillAnswer[index]"
              @blur="fillAnswer[index] = $event.target.value.trim()"
              class="question-item-content-input"
              style="width: 80%"
            />
            <el-button
              type="danger"
              class="question-item-remove"
              @click="fillAnswerRemove(index)"
              >删除</el-button
            >
          </el-form-item>
        </template>
      </template>
      <!-- 简答题 -->
      <template v-else-if="form.questionType === 'Q'">
        <el-form-item label="答案" prop="answer">
          <el-input
            type="textarea"
            autosize
            v-model="form.answer"
            @blur="form.answer = $event.target.value.trim()"
            style="width: 300px"
          />
        </el-form-item>
      </template>
      <!-- ================== 案例题todo ==================== -->
      <el-form-item label="解析" prop="analysis">
        <el-input
          v-model="form.analysis"
          @blur="form.analysis = $event.target.value.trim()"
          type="textarea"
          autosize
          style="width: 400px"
        />
      </el-form-item>
      <el-form-item label="难度" prop="degreeType">
        <el-select v-model="form.degreeType" placeholder="难度">
          <el-option
            v-for="dict in exam_degree_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="submitForm">提交</el-button>
        <el-button @click="handleBack">返回</el-button>
        <el-button @click="reset">重置</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup name="questionAdd">
  import {
    addQuestion,
    getQuestion,
    updateQuestion
  } from "@/api/onlineExam/question"
  import { catalogueList } from "@/api/system/catalogue"
  import catalogue from "@/utils/catalogue.js"
  import { countSubstr, delLastStr } from "@/utils/common.js"

  const { proxy } = getCurrentInstance()
  const { exam_question_type, exam_degree_type } = proxy.useDict(
    "exam_question_type",
    "exam_degree_type"
  )

  const route = useRoute()
  const catalogueOptions = ref([])
  const items = ref([])
  const flag = ref("add")
  // 插入填空题数量
  const fillCount = computed(() => {
    if (form.value.questionType !== "F") return
    if (!form.value.questionName) return 0
    const finalCount = countSubstr(form.value.questionName, INSERT_FILL_CONTENT)
    fillAnswer.value.length = finalCount
    return finalCount
  })

  const fillAnswer = ref([])

  const data = reactive({
    form: {},
    rules: {
      questionType: [
        { required: true, message: "试题类型不能为空", trigger: "change" }
      ],
      catalogueId: [
        { required: true, message: "试题目录不能为空", trigger: "change" }
      ],
      questionName: [
        { required: true, message: "题干不能为空", trigger: "change" }
      ],
      item1: [{ required: true, message: "选项不能为空", trigger: "change" }],
      // answer: [{ required: true, message: "答案不能为空", trigger: "change" }],
      degreeType: [
        { required: true, message: "试题难度不能为空", trigger: "change" }
      ]
    }
  })
  const { form, rules } = toRefs(data)

  if (Object.keys(route.query).length !== 0) {
    getQuestion(route.query.questionId).then(response => {
      form.value = response.data
      // 单选题、多选题处理
      if (
        form.value?.questionType === "S" ||
        form.value?.questionType === "M"
      ) {
        for (let i = 1; i < 11; i++) {
          if (form.value[`item${i}`]) {
            // 获取从A开始的英文字母
            const letter = String.fromCharCode(64 + i)
            items.value.push({ prefix: letter, content: "" })
          }
        }
        if (form.value.questionType === "M") {
          form.value.answer = form.value.answer.split(",")
        }
      }
      // 判断题处理
      else if (form.value?.questionType === "J") {
        items.value = [
          { id: null, prefix: "A", content: "对" },
          { id: null, prefix: "B", content: "错" }
        ]
        form.value.item1 = "对"
        form.value.item2 = "错"
      }
      // 填空题处理
      else if (form.value?.questionType === "F") {
        fillAnswer.value = form.value.answer?.split("::")
      }
      flag.value = "edit"
    })
  }

  /** 查询菜单下拉树结构 */
  function getTreeselect() {
    catalogueOptions.value = []
    catalogueList({ catalogueType: catalogue.SUBJECT_CATALOGUE }).then(
      response => {
        const catalogueTree = {
          catalogueId: 0,
          catalogueName: "主类目",
          children: [],
          disabled: true
        }
        catalogueTree.children = proxy.handleTree(response.rows, "catalogueId")
        catalogueOptions.value.push(catalogueTree)
      }
    )
  }
  // 试题类型切换
  const questionTypeChange = type => {
    items.value = []
    if (type === "S" || type === "M") {
      items.value = [
        { prefix: "A", content: "" },
        { prefix: "B", content: "" },
        { prefix: "C", content: "" },
        { prefix: "D", content: "" }
      ]
      if (type === "S") {
        form.value.answer = ""
      } else {
        form.value.answer = []
      }
    } else if (type === "J") {
      items.value = [
        { id: null, prefix: "A", content: "对" },
        { id: null, prefix: "B", content: "错" }
      ]
      form.value.item1 = "对"
      form.value.item2 = "错"
    } else if (type === "F") {
      items.value = []
    }
  }

  const questionItemRemove = index => {
    items.value.splice(index, 1)
  }
  const questionItemAdd = () => {
    if (items.value.length >= 10) {
      proxy.$modal.msgError("最多添加10个选项")
      return
    }
    items.value.push({ prefix: getNextLetter(), content: "" })
  }

  const getNextLetter = () => {
    if (items.value.length === 0) return
    const prevLetter = items.value[items.value.length - 1].prefix
    if (!prevLetter) return
    return String.fromCharCode(prevLetter.charCodeAt(0) + 1)
  }

  const submitForm = () => {
    proxy.$refs["formRef"].validate(valid => {
      if (valid) {
        // 多选题答案处理
        if (form.value.questionType === "M") {
          form.value.answer = form.value.answer.sort().join()
        }
        // 填空题答案
        else if (form.value.questionType === "F") {
          if (fillAnswer.value.length === 0) {
            proxy.$modal.msgWarning("请至少添加一个填空")
            return
          }
          try {
            fillAnswer.value.forEach(item => {
              if (!item) throw new Error()
            })
          } catch (error) {
            proxy.$modal.msgWarning("填空答案不能为空")
            return
          }
          form.value.answer = fillAnswer.value.join("::")
        }
        if (flag.value === "add") {
          addQuestion(form.value).then(res => {
            if (res.code === 200) {
              reset()
              proxy.$modal.msgSuccess("新增成功")
              proxy.$tab.closeOpenPage({
                path: "/onlineExam/questionManage"
              })
            }
          })
        } else {
          updateQuestion(form.value).then(res => {
            if (res.code === 200) {
              proxy.$modal.msgSuccess("修改成功")
              proxy.$tab.closeOpenPage({
                path: "/onlineExam/questionManage"
              })
            }
          })
        }
      }
    })
  }

  const catalogueSelect = (data, node) => {
    form.value.catalogueName = data.catalogueName
  }

  /** 表单重置 */
  function reset() {
    form.value = {}
  }

  const INSERT_FILL_CONTENT = "（____）"
  // 插入填空
  const insertFill = () => {
    if (fillCount.value >= 10) {
      return proxy.$modal.msgWarning("最多添加10个填空")
    }
    if (!form.value.questionName) {
      form.value.questionName = INSERT_FILL_CONTENT
      return
    }
    form.value.questionName =
      form.value.questionName.concat(INSERT_FILL_CONTENT)
    fillAnswer.value.push("")
  }

  const fillAnswerRemove = index => {
    fillAnswer.value.splice(index, 1)
    form.value.questionName = delLastStr(
      form.value.questionName,
      INSERT_FILL_CONTENT,
      index + 1
    )
  }
  const handleBack = () => {
    proxy.$tab.closeOpenPage({
      path: "/onlineExam/questionManage"
    })
  }

  getTreeselect()
</script>
<style lang="scss" scoped>
  :deep(.el-form-item__content) {
    display: block;
  }
  :deep(.el-input) {
    display: inline-block;
    width: auto;
  }
  :deep(.el-input__wrapper) {
    width: 100%;
  }

  .insert_fill {
    margin-left: 20px;
  }
</style>
