/**
 * 通用js方法封装处理
 */
const env = import.meta.env.VITE_APP_ENV

// 时间格式化
export function formatSecond(sec) {
  let hour = Math.floor(sec / 3600)
  let min = Math.floor((sec - hour * 3600) / 60)
  let second = sec - hour * 3600 - min * 60
  return [hour, min, second]
}
export function formatSecondStr(sec) {
  let hour = Math.floor(sec / 3600)
  let min = Math.floor((sec - hour * 3600) / 60)
  let second = sec - hour * 3600 - min * 60
  let hstr = hour ? hour + "时" : ""
  let mstr = min ? min + "分" : ""
  return hstr + mstr + second + "秒"
}
// 日期格式化
export function parseTime(time, pattern) {
  if (arguments.length === 0 || !time) {
    return null
  }
  const format = pattern || "{y}-{m}-{d} {h}:{i}:{s}"
  let date
  if (typeof time === "object") {
    date = time
  } else {
    if (typeof time === "string" && /^[0-9]+$/.test(time)) {
      time = parseInt(time)
    } else if (typeof time === "string") {
      time = time
        .replace(new RegExp(/-/gm), "/")
        .replace("T", " ")
        .replace(new RegExp(/\.[\d]{3}/gm), "")
    }
    if (typeof time === "number" && time.toString().length === 10) {
      time = time * 1000
    }
    date = new Date(time)
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay()
  }
  const time_str = format.replace(/{(y|m|d|h|i|s|a)+}/g, (result, key) => {
    let value = formatObj[key]
    // Note: getDay() returns 0 on Sunday
    if (key === "a") {
      return ["日", "一", "二", "三", "四", "五", "六"][value]
    }
    if (result.length > 0 && value < 10) {
      value = "0" + value
    }
    return value || 0
  })
  return time_str
}

// 表单重置
export function resetForm(refName) {
  if (this.$refs[refName]) {
    this.$refs[refName].resetFields()
  }
}

// 添加日期范围
export function addDateRange(params, dateRange, propName) {
  let search = params
  search.params =
    typeof search.params === "object" &&
    search.params !== null &&
    !Array.isArray(search.params)
      ? search.params
      : {}
  dateRange = Array.isArray(dateRange) ? dateRange : []
  if (typeof propName === "undefined") {
    search.params["beginTime"] = dateRange[0]
    search.params["endTime"] = dateRange[1]
  } else {
    search.params["begin" + propName] = dateRange[0]
    search.params["end" + propName] = dateRange[1]
  }
  return search
}

// 回显数据字典
export function selectDictLabel(datas, value) {
  if (value === undefined) {
    return ""
  }
  var actions = []
  Object.keys(datas).some(key => {
    if (datas[key].value == "" + value) {
      actions.push(datas[key].label)
      return true
    }
  })
  if (actions.length === 0) {
    actions.push(value)
  }
  return actions.join("")
}

// 回显数据字典（字符串数组）
export function selectDictLabels(datas, value, separator) {
  if (value === undefined || value.length === 0) {
    return ""
  }
  if (Array.isArray(value)) {
    value = value.join(",")
  }
  var actions = []
  var currentSeparator = undefined === separator ? "," : separator
  var temp = value.split(currentSeparator)
  Object.keys(value.split(currentSeparator)).some(val => {
    var match = false
    Object.keys(datas).some(key => {
      if (datas[key].value == "" + temp[val]) {
        actions.push(datas[key].label + currentSeparator)
        match = true
      }
    })
    if (!match) {
      actions.push(temp[val] + currentSeparator)
    }
  })
  return actions.join("").substring(0, actions.join("").length - 1)
}

// 回显数据字典(通过label取value)
export function selectDictValue(datas, label) {
  if (label === undefined) {
    return ""
  }
  var actions = []
  Object.keys(datas).some(key => {
    if (datas[key].label == "" + label) {
      actions.push(datas[key].value)
      return true
    }
  })
  if (actions.length === 0) {
    actions.push(label)
  }
  return actions.join("")
}

// 字符串格式化(%s )
export function sprintf(str) {
  var args = arguments,
    flag = true,
    i = 1
  str = str.replace(/%s/g, function () {
    var arg = args[i++]
    if (typeof arg === "undefined") {
      flag = false
      return ""
    }
    return arg
  })
  return flag ? str : ""
}

// 转换字符串，undefined,null等转化为""
export function parseStrEmpty(str) {
  if (!str || str == "undefined" || str == "null") {
    return ""
  }
  return str
}

// 数据合并
export function mergeRecursive(source, target) {
  for (var p in target) {
    try {
      if (target[p].constructor == Object) {
        source[p] = mergeRecursive(source[p], target[p])
      } else {
        source[p] = target[p]
      }
    } catch (e) {
      source[p] = target[p]
    }
  }
  return source
}

/**
 * 构造树型结构数据
 * @param {*} data 数据源
 * @param {*} id id字段 默认 'id'
 * @param {*} parentId 父节点字段 默认 'parentId'
 * @param {*} children 孩子节点字段 默认 'children'
 */
export function handleTree(data, id, parentId, children) {
  let config = {
    id: id || "id",
    parentId: parentId || "parentId",
    childrenList: children || "children"
  }

  var childrenListMap = {}
  var nodeIds = {}
  var tree = []

  for (let d of data) {
    let parentId = d[config.parentId]
    if (childrenListMap[parentId] == null) {
      childrenListMap[parentId] = []
    }
    nodeIds[d[config.id]] = d
    childrenListMap[parentId].push(d)
  }

  for (let d of data) {
    let parentId = d[config.parentId]
    if (nodeIds[parentId] == null) {
      tree.push(d)
    }
  }

  for (let t of tree) {
    adaptToChildrenList(t)
  }

  function adaptToChildrenList(o) {
    if (childrenListMap[o[config.id]] !== null) {
      o[config.childrenList] = childrenListMap[o[config.id]]
    }
    if (o[config.childrenList]) {
      for (let c of o[config.childrenList]) {
        adaptToChildrenList(c)
      }
    }
  }
  return tree
}

/**
 * 参数处理
 * @param {*} params  参数
 */
export function tansParams(params) {
  let result = ""
  for (const propName of Object.keys(params)) {
    const value = params[propName]
    var part = encodeURIComponent(propName) + "="
    if (value !== null && value !== "" && typeof value !== "undefined") {
      if (typeof value === "object") {
        for (const key of Object.keys(value)) {
          if (
            value[key] !== null &&
            value[key] !== "" &&
            typeof value[key] !== "undefined"
          ) {
            let params = propName + "[" + key + "]"
            var subPart = encodeURIComponent(params) + "="
            result += subPart + encodeURIComponent(value[key]) + "&"
          }
        }
      } else {
        result += part + encodeURIComponent(value) + "&"
      }
    }
  }
  return result
}

// 返回项目路径
export function getNormalPath(p) {
  if (p.length === 0 || !p || p == "undefined") {
    return p
  }
  let res = p.replace("//", "/")
  if (res[res.length - 1] === "/") {
    return res.slice(0, res.length - 1)
  }
  return res
}

// 验证是否为blob格式
export async function blobValidate(data) {
  try {
    const text = await data.text()
    JSON.parse(text)
    return false
  } catch (error) {
    return true
  }
}

// 计算子字符串在原始字符串中出现的次数
export function countSubstr(str, subStr) {
  return str?.split(subStr).length - 1
}

/**  isRequiredIP 为true时如果当前是 IP 地址，则直接返回IP Address，为false则返回空; 如果是 uzi.bkehs.cn 则返回'uzi' */
export function getSubdomain(isRequiredIP = true) {
  try {
    let subdomain = ""
    const { domain } = document
    const domainList = domain.split(".")
    const ipAddressReg =
      /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/ // 若为 IP 地址、localhost，则直接返回
    if (ipAddressReg.test(domain) || domain === "localhost") {
      return isRequiredIP ? domain : ""
    }
    subdomain = domainList[0]

    if (env === "production") {
      if (subdomain === "training" || subdomain === "learning") return ""
      subdomain = subdomain + "."
      return subdomain || document.domain
    } else if (env === "hwproduction" || env === "xdproduction") {
      subdomain = subdomain.replace("eduadmin", "")
      return subdomain
    }
  } catch (e) {
    return document.domain
  }
}

// 传入秒钟，格式化为对应的时分秒
export function formatSeconds(seconds) {
  var hours = Math.floor(seconds / 3600)
  var minutes = Math.floor((seconds % 3600) / 60)
  var remainingSeconds = seconds % 60
  var result = ""

  if (hours > 0) {
    result += hours + "时"
  }
  if (minutes > 0) {
    result += minutes + "分"
  }
  if (hours == 0 && minutes == 0) {
    result = remainingSeconds + "秒"
  } else if (remainingSeconds > 0) {
    result += remainingSeconds + "秒"
  }

  return result
}

// v-bind绑定src后无值 默认绑定本地静态资源
export function getAssetURL(url) {
  if (url) return url
  return new URL(`@/assets/images/avatar.png`, import.meta.url).href
}

/* 参数
 * treeData 树结构 查找所有叶子节点
 */
export function parseTreeJson(treeData) {
  const leafNodes = []

  function traverse(node) {
    for (let index = 0; index < node.length; index++) {
      const element = node[index]

      if (element.children && element.children.length !== 0) {
        traverse(element.children)
      } else {
        leafNodes.push(element)
      }
    }
  }

  traverse(treeData)
  return leafNodes
}

// 计算两个时间格式之间相差的秒数
export function calculateTimeDifference(startTime, endTime) {
  const start = new Date(startTime)
  const end = new Date(endTime)
  const difference = Math.abs(end - start)
  const seconds = Math.floor(difference / 1000)

  return seconds
}

// 获取文件名称
export function getFileName(name) {
  name = decodeURIComponent(name)
  if (name.lastIndexOf("/") > -1) {
    return name.slice(name.lastIndexOf("/") + 1)
  } else {
    return ""
  }
}

// 去除文件名后缀
export function removeFileExtension(filename) {
  const lastDotIndex = filename.lastIndexOf(".")
  if (lastDotIndex !== -1) {
    return filename.substring(0, lastDotIndex)
  } else {
    return filename
  }
}

// 判断当前时间是否在指定时间区间内
export function isInTimeRange(start, end) {
  const now = new Date() // 获取当前时间
  const startTime = new Date(start) // 开始时间
  const endTime = new Date(end) // 结束时间
  return now >= startTime && now <= endTime
}

// 删除长字符串中第i次出现的短字符串
export function delLastStr(str, target, i) {
  let index1 = str.indexOf(target) // 首先找出第一次寻找到target的位置
  let num = 0 // 出现的次数
  while (index1 !== -1) {
    num++
    if (num == i) {
      const up = str.substring(0, index1)
      const next = str.substring(index1 + target.length, str.length)
      return up + next
    }
    index1 = str.indexOf(target, index1 + 1) // 寻找下一个target
  }
}

// 判断两个数组中是否有相同元素
export function hasCommonElement(arr1, arr2) {
  let set = new Set(arr1)
  for (let element of arr2) {
    if (set.has(element)) {
      return true
    }
  }
  return false
}
