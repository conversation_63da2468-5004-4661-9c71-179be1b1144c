/*
 * @Description: 在线课程-课程
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-04-07 15:53:54
 * @LastEditTime: 2024-02-20 09:56:34
 */

import request from "@/utils/request"

// 查询课程列表
export function listCourse(query) {
  return request({
    url: "/course/base/list",
    method: "get",
    params: query
  })
}

// 查询课程详细
export function getCourse(courseId) {
  return request({
    url: "/course/base/" + courseId,
    method: "get"
  })
}

// 新增课程
export function addCourse(data) {
  return request({
    url: "/course/base",
    method: "post",
    data: data
  })
}

// 修改课程
export function updateCourse(data) {
  return request({
    url: "/course/base",
    method: "put",
    data: data
  })
}

// 删除课程
export function delCourse(courseId) {
  return request({
    url: "/course/base/" + courseId,
    method: "delete"
  })
}

// 查询评论列表
export function listComment(query) {
  return request({
    url: "/course/course-discuss/list",
    method: "get",
    params: query
  })
}

// 删除评论
export function delComment(commentId) {
  return request({
    url: "/course/course-discuss/" + commentId,
    method: "delete"
  })
}

// 课程提交/审批/驳回
export function approvalCourse(data) {
  return request({
    url: "/course/base/approval",
    method: "post",
    data: data
  })
}

// 课程批量审批/驳回
export function batchApprovalCourse(data) {
  return request({
    url: "/course/base/batchApproval",
    method: "post",
    data: data
  })
}

// 在线课程统计报表明细
export function getReportDetail(params) {
  return request({
    url: "/course/progress/user-detail",
    method: "get",
    params
  })
}

// 查询课程审批记录
export function getCourseReviewRecords(courseId) {
  return request({
    url: "/course/base/records/" + courseId,
    method: "get"
  })
}

// 查询课程审核列表
export function getCourseReviewList(query) {
  return request({
    url: "/course/base/wait-approve-list",
    method: "get",
    params: query
  })
}
