{"name": "beckwell", "version": "3.6.2", "description": "培训学习平台后台", "author": "<PERSON>", "license": "MIT", "scripts": {"dev": "vite", "dev:stage": "vite --mode staging", "build:hwprod": "vite build --mode hwprod && node huawei-obs-upload.js eduadmin eduadmin1", "build:prod": "vite build --mode prod", "build:xdprod": "vite build --mode xdprod && node huawei-obs-upload.js eduxdadmin", "build:stage": "vite build --mode staging", "preview": "vite preview"}, "repository": {"type": "git", "url": "https://codeup.aliyun.com/bw/beckwell-web-training.git"}, "dependencies": {"@element-plus/icons-vue": "2.0.10", "@vue-office/docx": "^1.6.2", "@vue-office/pdf": "^2.0.8", "@vueuse/core": "9.5.0", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "axios": "0.27.2", "echarts": "^5.4.3", "el-table-infinite-scroll": "^3.0.3", "element-plus": "2.2.27", "esdk-obs-browserjs": "^3.23.5", "file-saver": "2.0.5", "fuse.js": "6.6.2", "html2canvas": "^1.4.1", "js-cookie": "3.0.1", "jsencrypt": "3.3.1", "jspdf": "^2.5.1", "lodash-es": "^4.17.21", "mitt": "^3.0.1", "nprogress": "0.2.0", "pinia": "2.0.22", "qrcodejs2-fix": "^0.0.1", "smooth-signature": "^1.0.14", "sortablejs": "^1.15.0", "vue": "3.2.45", "vue-cropper": "1.0.3", "vue-router": "4.1.4", "unocss": "0.45.30", "@unocss/preset-attributify": "0.45.30", "@unocss/preset-icons": "0.45.30", "@unocss/preset-uno": "0.45.30"}, "devDependencies": {"@vitejs/plugin-vue": "3.1.0", "@vue/compiler-sfc": "3.2.45", "huawei-obs-plugin": "^1.0.2", "sass": "1.56.1", "unplugin-auto-import": "0.11.4", "vite": "3.2.3", "vite-ali-oss-plugin": "^1.1.3", "vite-plugin-compression": "0.5.1", "vite-plugin-svg-icons": "2.0.1", "vite-plugin-vue-setup-extend": "0.4.0"}, "volta": {"node": "16.20.0"}}