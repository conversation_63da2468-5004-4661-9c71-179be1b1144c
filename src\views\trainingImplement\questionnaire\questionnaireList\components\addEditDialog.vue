<!--
 * @Description: 新增/修改问卷下发
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-05-12 08:52:26
 * @LastEditTime: 2023-11-15 17:03:01
-->
<template>
  <el-dialog
    v-model="visible"
    :title="!dataForm.questionnaireId ? '新增' : '修改'"
    width="680px"
    center
  >
    <el-form
      ref="dataFormRef"
      :model="dataForm"
      :rules="rules"
      label-width="110px"
      @keyup.enter="submitHandle()"
    >
      <el-row :gutter="10">
        <el-col :span="24">
          <el-form-item label="开始时间" prop="startTime">
            <el-date-picker
              v-model="dataForm.startTime"
              type="date"
              placeholder="开始时间"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="结束时间" prop="endTime">
            <el-date-picker
              v-model="dataForm.endTime"
              type="date"
              placeholder="结束时间"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="下发人员" prop="issuedUserNames">
            <el-input
              v-model="dataForm.issuedUserNames"
              :rows="6"
              type="textarea"
              disabled
            />
            <div class="extendButton">
              <el-button type="primary" @click="choosePersonnel"
                >选择下发人员</el-button
              >
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="submitHandle()">确定</el-button>
    </template>

    <!-- 下发人员选择弹窗 -->
    <ChoosePersonnelOrDeptDialog
      ref="choosePersonnelOrDeptDialogRef"
      :needDeptId="true"
      @fetch-data="choosePersonnelDone"
    />
  </el-dialog>
</template>

<script setup>
  import {
    getIssued,
    addIssued,
    updateIssued
  } from "@/api/trainingImplement/questionnaire.js"

  const emit = defineEmits(["refreshDataList"])

  const { proxy } = getCurrentInstance()

  const visible = ref(false)
  const dataFormRef = ref()
  let dataForm = ref({})
  let question = ref()

  // 打开弹窗事件
  const openDialog = async (questionId, issuedId) => {
    // 重置表单数据
    dataForm.value = {}
    question.value = questionId
    if (issuedId) {
      const { data } = await getIssued(issuedId)
      Object.assign(dataForm.value, data)
    }
    visible.value = true
  }

  const rules = ref({
    startTime: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
    endTime: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
    issuedUserNames: [
      { required: true, message: "必填项不能为空", trigger: "blur" }
    ]
  })

  // 表单提交
  const submitHandle = () => {
    dataFormRef.value.validate(valid => {
      if (!valid) {
        return false
      }
      if (!dataForm.value.questionnaireId) {
        dataForm.value.questionnaireId = question.value
        addIssued(dataForm.value).then(res => {
          if (res.code === 200) {
            proxy.$modal.msgSuccess("新增成功")
            emit("refreshDataList")
            visible.value = false
          }
        })
      } else {
        updateIssued(dataForm.value).then(res => {
          if (res.code === 200) {
            proxy.$modal.msgSuccess("修改成功")
            emit("refreshDataList")
            visible.value = false
          }
        })
      }
    })
  }

  // 选择下发人员
  const choosePersonnel = () => {
    proxy.$refs["choosePersonnelOrDeptDialogRef"].openDialog(
      dataForm.value.issuedUserIds,
      dataForm.value.issuedUserNames,
      dataForm.value.deptIds
    )
  }
  // 下发人员选择完成
  const choosePersonnelDone = userList => {
    if (!userList || userList.length === 0) return
    delete dataForm.value.deptName
    dataForm.value.deptIds = userList.map(user => user.deptId).join()
    dataForm.value.issuedUserIds = userList.map(user => user.userId).join()
    dataForm.value.issuedUserNames = userList.map(user => user.userName).join()
  }

  defineExpose({
    openDialog
  })
</script>

<style lang="scss" scoped>
  :deep(.el-textarea__inner) {
    width: 300px;
  }
  .extendButton {
    position: absolute;
    left: 320px;
    top: 50px;
  }
</style>
