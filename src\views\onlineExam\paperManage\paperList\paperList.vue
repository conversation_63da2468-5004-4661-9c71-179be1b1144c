<!--
 * @Description: 试卷列表
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-03-02 13:43:59
 * @LastEditTime: 2025-06-27 13:54:00
-->
<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
    >
      <el-form-item label="试卷名称" prop="paperName">
        <el-input
          v-model="queryParams.paperName"
          placeholder="请输入试卷名称"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="试卷目录" prop="catalogueId">
        <el-tree-select
          v-model="queryParams.catalogueId"
          :data="catalogueOptions"
          :props="{
            value: 'catalogueId',
            label: 'catalogueName',
            children: 'children',
            disabled: 'disabled'
          }"
          clearable
          value-key="catalogueId"
          placeholder="选择试卷目录"
          check-strictly
          default-expand-all
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb-8px">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd">
          新增
        </el-button>
      </el-col>
      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table :data="tableData">
      <el-table-column label="试卷名称" prop="paperName" />
      <el-table-column label="所属目录" prop="catalogueName" />
      <el-table-column label="组卷类型" prop="paperType">
        <template #default="scope">
          <dict-tag :options="paper_type" :value="scope.row.paperType" />
        </template>
      </el-table-column>
      <el-table-column label="试卷总分" prop="paperScore">
        <template #default="scope"> {{ scope.row.paperScore }}分 </template>
      </el-table-column>
      <el-table-column label="备注" prop="remark" />
      <el-table-column
        label="操作"
        width="250"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-button
            link
            type="primary"
            icon="View"
            @click="handlePreview(scope.row)"
            >预览</el-button
          >
          <el-button
            link
            type="primary"
            icon="Edit"
            @click="handleUpdate(scope.row)"
            >修改</el-button
          >
          <el-button
            link
            type="primary"
            icon="Delete"
            @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script setup name="paperList">
  import { delPaper, listPaper } from "@/api/onlineExam/paper"
  import { catalogueList } from "@/api/system/catalogue.js"
  import catalogue from "@/utils/catalogue.js"

  const { proxy } = getCurrentInstance()
  const { paper_type } = proxy.useDict("paper_type")

  const emit = defineEmits(["updateCurrentView"])
  const tableData = ref([])
  const showSearch = ref(true)
  const total = ref(0)
  const catalogueOptions = ref([])

  const data = reactive({
    queryParams: {
      pageNum: 1,
      pageSize: 10
    }
  })

  const { queryParams } = toRefs(data)

  /** 查询目录列表 */
  function getList() {
    listPaper(queryParams.value).then(response => {
      tableData.value = response.rows || []
      total.value = response.total
    })
  }
  /** 查询目录下拉树结构 */
  function getTreeselect() {
    catalogueOptions.value = []
    catalogueList({ catalogueType: catalogue.PAPER_CATALOGUE }).then(
      response => {
        const paperCatalogue = {
          catalogueId: 0,
          catalogueName: "主类目",
          children: [],
          disabled: true
        }
        paperCatalogue.children = proxy.handleTree(response.rows, "catalogueId")
        catalogueOptions.value.push(paperCatalogue)
      }
    )
  }
  /** 搜索按钮操作 */
  function handleQuery() {
    getList()
  }
  /** 重置按钮操作 */
  function resetQuery() {
    proxy.resetForm("queryRef")
    handleQuery()
  }
  const handleAdd = () => {
    proxy.$tab.closeOpenPage({
      path: "/onlineExam/paperAdd"
    })
  }
  /** 修改按钮操作 */
  function handleUpdate(row) {
    proxy.$tab.closeOpenPage({
      path: "/onlineExam/paperAdd",
      query: row
    })
  }
  /** 删除按钮操作 */
  function handleDelete(row) {
    proxy.$modal
      .confirm('是否确认删除名称为"' + row.paperName + '"的数据项?')
      .then(function () {
        return delPaper(row.paperId)
      })
      .then(() => {
        getList()
        proxy.$modal.msgSuccess("删除成功")
      })
      .catch(() => {})
  }

  const handlePreview = row => {
    emit("updateCurrentView", "paperPreview", {
      ...row
    })
  }

  getList()
  getTreeselect()
</script>
