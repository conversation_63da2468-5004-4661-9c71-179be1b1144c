<!--
 * @Description: 任务实施报表任务详情
 * @Author: <PERSON><PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-08-11 08:57:51
 * @LastEditTime: 2025-01-13 14:08:36
-->
<template>
  <div class="app-container">
    <el-button
      class="mb-8px"
      type="primary"
      icon="ArrowLeftBold"
      size="20"
      @click="handleBack"
    >
      返 回
    </el-button>
    <taskDetailHeader :detailInfo="detailInfo" />

    <el-tabs v-model="activeTab" class="demo-tabs" @tab-change="tabChange">
      <el-tab-pane :label="`人员(${userTotalCount})`" name="1"></el-tab-pane>
      <el-tab-pane
        v-if="detailInfo?.courseList?.length > 0"
        :label="`在线课程(${detailInfo?.courseList?.length || 0})`"
        name="2"
      ></el-tab-pane>
      <el-tab-pane
        v-if="detailInfo?.examList?.length > 0"
        :label="`考试(${detailInfo?.examList?.length || 0})`"
        name="3"
      ></el-tab-pane>
      <el-tab-pane
        v-if="detailInfo?.questionnaireList?.length > 0"
        :label="`调查问卷(${detailInfo?.questionnaireList?.length || 0})`"
        name="4"
      ></el-tab-pane>
    </el-tabs>
    <template v-if="activeTab !== '1'">
      <div class="tagList">
        <el-check-tag
          v-for="item in tagList"
          :checked="currentId === item.id"
          @change="onChange(item)"
        >
          {{ item.name }}
        </el-check-tag>
      </div>
    </template>

    <el-form :model="queryParams" ref="queryRef" :inline="true">
      <el-form-item label="学员名称" prop="studentName">
        <el-input
          v-model="queryParams.studentName"
          placeholder="学员名称"
          clearable
          style="width: 240px"
          @keyup.enter="getList"
        />
      </el-form-item>

      <el-form-item
        label="状态"
        prop="completionStatus"
        v-if="activeTab !== '4'"
      >
        <el-select
          v-model="queryParams.completionStatus"
          placeholder="状态"
          clearable
          style="width: 200px"
        >
          <el-option
            v-for="dict in completionStatusOpts"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="getList">
          搜索
        </el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb-8px">
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport">
          导出
        </el-button>
      </el-col>
    </el-row>
    <el-table :data="tableData" :border="false">
      <el-table-column label="学员姓名" prop="studentName" />
      <el-table-column label="所属部门" prop="localDeptName" />
      <el-table-column label="学习进度" prop="rateLearning">
        <template #default="scope">
          {{ Math.floor(scope.row.rateLearning * 100) }}%
        </template>
      </el-table-column>
      <el-table-column label="最近学习时间" prop="recentLearningTime" />
      <el-table-column label="实际完成时间" prop="actualCompletionTime" />
      <el-table-column label="完成状态" prop="completionStatus">
        <template #default="scope">
          <dict-tag
            :options="completionStatusOpts"
            :value="scope.row.completionStatus"
          />
        </template>
      </el-table-column>
      <template v-if="activeTab === '1' || activeTab === '3'">
        <el-table-column label="考试分值" prop="userPaperScore" />
        <el-table-column label="是否通过" prop="isPass" />
        <el-table-column label="人脸抓拍" prop="matchResult">
          <template #default="scope">
            {{
              scope.row.matchResult === "1"
                ? "通过"
                : scope.row.matchResult === "2"
                ? "未通过"
                : "未抓拍"
            }}
          </template>
        </el-table-column>
      </template>
      <!-- <el-table-column label="取得证书名称" prop="templateName">
        <template #default="scope">
          {{ scope.row.templateName || "-" }}
        </template>
      </el-table-column> -->
      <el-table-column v-if="activeTab === '1'" label="证书" prop="certUrl">
        <template #default="scope">
          <el-image
            v-if="scope.row.certUrl"
            class="w-100px h-70px cursor-pointer"
            :src="scope.row.certUrl"
            :preview-src-list="[scope.row.certUrl]"
            preview-teleported
            fit="fill"
          />
          <span v-else>-</span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="取得证书时间" prop="issueDate">
        <template #default="scope">
          {{ scope.row.issueDate || "-" }}
        </template>
      </el-table-column> -->
      <el-table-column
        v-if="activeTab === '1'"
        label="操作"
        width="200"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-button
            link
            type="primary"
            icon="View"
            @click="handleDetail(scope.row)"
          >
            详情
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
      :autoScroll="false"
    />
  </div>
</template>

<script setup name="taskDetail">
  import dayjs from "dayjs"
  import {
    getTrainingTask,
    trainingTrackUserList,
    trainingTrackCourseList,
    trainingTrackExamList,
    trainingTrackQuestionnaireList
  } from "@/api/trainingImplement/trainingTask.js"
  import taskDetailHeader from "./components/taskDetailHeader.vue"

  const { proxy } = getCurrentInstance()
  const { completion_status, training_task_unite_status } = proxy.useDict(
    "completion_status",
    "training_task_unite_status"
  )
  const emit = defineEmits(["updateCurrentView"])
  const props = defineProps({
    row: {
      type: Object,
      default: () => ({})
    }
  })

  const activeTab = ref("1")
  const completionStatusOpts = computed(() => {
    if (activeTab.value === "1") {
      return completion_status.value
    } else {
      return training_task_unite_status.value
    }
  })

  const detailInfo = ref({})
  const tableData = ref([])
  const tagList = ref([])
  const total = ref(0)
  let currentId = ref()

  const data = reactive({
    queryParams: {
      pageNum: 1,
      pageSize: 10
    }
  })

  const { queryParams } = toRefs(data)

  /** 查询详情信息 */
  const getDetail = async () => {
    const response = await getTrainingTask(props.row.taskId)
    detailInfo.value = response.data
  }
  /** 重置按钮操作 */
  function resetQuery() {
    proxy.resetForm("queryRef")
    getList()
  }

  const userTotalCount = ref(0)
  /** 查询列表数据 */
  const getList = async flag => {
    let typeField
    if (activeTab.value === "2") {
      tagList.value = detailInfo.value?.courseList
      typeField = "courseId"
    } else if (activeTab.value === "3") {
      tagList.value = detailInfo.value?.examList
      typeField = "examId"
    } else if (activeTab.value === "4") {
      tagList.value = detailInfo.value?.questionnaireList
      typeField = "questionnaireId"
    }
    if (flag === "changeTab") currentId.value = tagList.value[0]?.id
    let queryData = {
      ...queryParams.value,
      taskId: props.row.taskId,
      [typeField]: activeTab.value === "1" ? undefined : currentId.value
    }
    let res =
      activeTab.value === "1"
        ? await trainingTrackUserList(queryData)
        : activeTab.value === "2"
        ? await trainingTrackCourseList(queryData)
        : activeTab.value === "3"
        ? await trainingTrackExamList(queryData)
        : activeTab.value === "4"
        ? await trainingTrackQuestionnaireList(queryData)
        : ""
    tableData.value = res.rows || []
    total.value = res.total
    if (activeTab.value === "1") {
      userTotalCount.value = res.total
    }
  }

  const onChange = item => {
    if (item.id === currentId.value) return
    queryParams.value.pageNum = 1
    currentId.value = item.id
    getList()
  }

  const handleBack = () => {
    emit("updateCurrentView", "taskList")
  }

  const handleDetail = row => {
    emit("updateCurrentView", "taskPersonDetail", {
      ...detailInfo.value,
      row
    })
  }

  const tabChange = () => {
    queryParams.value = {
      pageNum: 1,
      pageSize: 10
    }
    getList("changeTab")
  }

  /** 导出按钮操作 */
  function handleExport() {
    // 人员导出
    if (activeTab.value === "1") {
      proxy.download(
        "course/record/export/userList",
        {
          ...queryParams.value,
          taskId: props.row.taskId
        },
        `${detailInfo.value.taskName}_人员_${dayjs().format("YYYY-MM-DD")}.xlsx`
      )
    }
    // 课程导出
    else if (activeTab.value === "2") {
      proxy.download(
        "course/record/export/courseList",
        {
          ...queryParams.value,
          taskId: props.row.taskId,
          courseId: currentId.value
        },
        `${detailInfo.value.taskName}_课程_${dayjs().format("YYYY-MM-DD")}.xlsx`
      )
    }
    // 考试导出
    else if (activeTab.value === "3") {
      proxy.download(
        "course/record/export/examList",
        {
          ...queryParams.value,
          taskId: props.row.taskId,
          examId: currentId.value
        },
        `${detailInfo.value.taskName}_考试_${dayjs().format("YYYY-MM-DD")}.xlsx`
      )
    }
    // 调查问卷导出
    else if (activeTab.value === "4") {
      proxy.download(
        "course/record/export/questionnaireList",
        {
          ...queryParams.value,
          taskId: props.row.taskId,
          questionnaireId: currentId.value
        },
        `${detailInfo.value.taskName}_调查问卷_${dayjs().format(
          "YYYY-MM-DD"
        )}.xlsx`
      )
    }
  }

  // 处理base64图片
  const getBase64Image = base64Str => {
    // 如果已经是完整的base64图片格式则直接返回
    if (base64Str?.startsWith("data:image")) {
      return base64Str
    }
    // 否则添加前缀
    return base64Str ? `data:image/png;base64,${base64Str}` : ""
  }

  onMounted(() => {
    getDetail()
    getList()
  })
</script>

<style lang="scss" scoped>
  .backIcon {
    svg {
      height: 0.7em;
    }
  }

  .tagList {
    margin: 10px;
    padding-bottom: 10px;
    :deep(.el-check-tag) {
      margin: 0 10px 10px 0;
      max-width: 200px;
      /*强制文字在一行文本框内*/
      white-space: nowrap;
      /*溢出部分文字隐藏*/
      overflow: hidden;
      /*溢出部分省略号处理*/
      text-overflow: ellipsis;
    }
  }

  :deep(.el-image) {
    border-radius: 4px;
    overflow: hidden;

    &:hover {
      box-shadow: 0 0 8px rgba(0, 0, 0, 0.1);
    }
  }
</style>
