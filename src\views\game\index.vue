<!--
 * @Description: 游戏管理
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2025-06-06 13:59:33
 * @LastEditTime: 2025-07-16 16:02:19
-->

<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
      @submit.prevent
    >
      <el-form-item label="游戏名称" prop="gameName">
        <el-input
          v-model="queryParams.gameName"
          placeholder="游戏名称"
          clearable
          style="width: 240px"
          @keyup.enter="getList"
        />
      </el-form-item>
      <el-form-item label="游戏编号" prop="gameCode">
        <el-input
          v-model="queryParams.gameCode"
          placeholder="游戏编号"
          clearable
          style="width: 240px"
          @keyup.enter="getList"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="getList"
          >搜索</el-button
        >
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb-8px">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleUpdate">
          新增
        </el-button>
        <el-button
          type="info"
          plain
          icon="User"
          @click="handleBatchAssignPersonnel"
          :disabled="selectedGames.length === 0"
        >
          批量设置人员
        </el-button>
      </el-col>
      <right-toolbar
        :search="false"
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="tableData"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column prop="gameCover" label="游戏封面" width="100">
        <template #default="scope">
          <el-popover placement="right" :width="400" trigger="hover">
            <img :src="scope.row.gameCover" width="375" height="375" />
            <template #reference>
              <img
                :src="scope.row.gameCover"
                style="max-height: 60px; max-width: 60px"
              />
            </template>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column prop="gameName" label="游戏名称"></el-table-column>
      <el-table-column prop="gameCode" label="游戏编号"></el-table-column>
      <el-table-column prop="gameType" label="游戏类型"></el-table-column>
      <el-table-column prop="gameLink" label="游戏链接URL"></el-table-column>
      <el-table-column prop="status" label="状态" width="100">
        <template #default="scope">
          <el-tag v-if="scope.row.status === 0" type="danger">下架</el-tag>
          <el-tag v-else-if="scope.row.status === 1" type="success"
            >上架</el-tag
          >
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        width="250"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-button
            link
            type="primary"
            icon="Edit"
            @click="handleUpdate(scope.row)"
            >修改</el-button
          >
          <el-button
            link
            type="primary"
            icon="Delete"
            @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 弹窗, 新增 / 修改 -->
    <addEditDialog
      ref="addEditDialogRef"
      @refreshDataList="getList"
    ></addEditDialog>

    <!-- 批量设置人员弹窗 -->
    <BatchAssignPersonnelDialog
      ref="batchAssignPersonnelDialogRef"
      @refreshDataList="getList"
    />
  </div>
</template>

<script setup name="videoList">
  import addEditDialog from "./addEditDialog.vue"
  import BatchAssignPersonnelDialog from "./batchAssignPersonnelDialog.vue"
  import { listGame, delGame } from "@/api/game"

  const { proxy } = getCurrentInstance()
  const tableData = ref([])
  const loading = ref(false)
  const showSearch = ref(true)
  const total = ref(0)
  const addEditDialogRef = ref()
  const batchAssignPersonnelDialogRef = ref()
  const selectedGames = ref([])

  const queryParams = ref({
    pageNum: 1,
    pageSize: 10
  })

  /** 查询目录列表 */
  function getList() {
    loading.value = true
    listGame(queryParams.value).then(response => {
      tableData.value = response.rows || []
      total.value = response.total
      loading.value = false
    })
  }

  /** 修改按钮操作 */
  function handleUpdate(row) {
    addEditDialogRef.value.openDialog(row?.id)
  }

  /** 删除按钮操作 */
  function handleDelete(row) {
    proxy.$modal
      .confirm("是否确认删除?")
      .then(function () {
        return delGame(row.id)
      })
      .then(() => {
        getList()
        proxy.$modal.msgSuccess("删除成功")
      })
      .catch(() => {})
  }

  /** 多选变化 */
  function handleSelectionChange(selection) {
    selectedGames.value = selection
  }

  /** 批量设置人员 */
  function handleBatchAssignPersonnel() {
    if (selectedGames.value.length === 0) {
      proxy.$modal.msgWarning("请先选择游戏")
      return
    }
    batchAssignPersonnelDialogRef.value.openDialog(selectedGames.value)
  }

  function resetQuery() {
    proxy.resetForm("queryRef")
    getList()
  }

  onMounted(() => {
    getList()
  })
</script>
