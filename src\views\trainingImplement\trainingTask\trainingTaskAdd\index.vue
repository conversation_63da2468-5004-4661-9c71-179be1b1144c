<!--
 * @Description: 培训任务录入
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-05-25 09:37:33
 * @LastEditTime: 2025-07-25 11:33:58
-->
<template>
  <div class="app-container">
    <el-form
      :model="form"
      ref="formRef"
      label-width="160px"
      :inline="true"
      :rules="rules"
    >
      <el-row :gutter="10">
        <el-col :span="24">
          <el-form-item label="封面图片" prop="coverPhoto">
            <ImageUpload v-model="form.coverPhoto" :limit="1" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="任务名称" prop="taskName">
            <el-input v-model="form.taskName" class="!w-240px" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="指定的学员列表" prop="studentUserNames">
            <el-input
              class="!w-240px"
              v-model="form.studentUserNames"
              disabled
            />
            <div class="flex ml-4">
              <el-button
                v-if="showViewSelectedPersonnelBtn"
                type="primary"
                @click="viewSelectedPersonnel"
              >
                查看全部所选人员
              </el-button>
              <el-button type="primary" @click="choosePersonnel">
                指定学员
              </el-button>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="开始时间" prop="startTime">
            <el-date-picker
              class="!w-240px"
              v-model="form.startTime"
              type="datetime"
              placeholder="开始时间"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="结束时间" prop="endTime">
            <el-date-picker
              class="!w-240px"
              v-model="form.endTime"
              type="datetime"
              placeholder="结束时间"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="通知人员" prop="notifyUserNames" class="mb-5">
            <div class="mt-2 flex flex-wrap gap-2">
              <div
                v-for="(person, index) in displayNotifyPersons"
                :key="person.userId"
                class="bg-green-50 h-9 rounded-3xl flex items-center pr-2 relative border border-green-200"
              >
                <el-avatar
                  class="!m-1"
                  :size="28"
                  :style="{ backgroundColor: '#67c23a' }"
                >
                  {{ person.userName.substring(0, 1) }}
                </el-avatar>
                <span class="mx-2 text-sm">{{ person.userName }}</span>
                <el-icon
                  class="ml-2 cursor-pointer hover:text-red-500"
                  @click="handleRemoveNotifyPerson(person)"
                >
                  <Close />
                </el-icon>
              </div>

              <!-- 展开/收起按钮 -->
              <div
                v-if="selectedNotifyPersons.length > 20"
                class="bg-gray-100 h-9 rounded-3xl flex items-center px-3 border border-gray-200 cursor-pointer hover:bg-gray-200"
                @click="toggleNotifyExpanded"
              >
                <span class="text-sm text-gray-600">
                  {{
                    isNotifyExpanded
                      ? "收起"
                      : `还有${selectedNotifyPersons.length - 20}人...`
                  }}
                </span>
                <el-icon class="ml-1 text-gray-600">
                  <component :is="isNotifyExpanded ? 'ArrowUp' : 'ArrowDown'" />
                </el-icon>
              </div>

              <el-button type="primary" link @click="chooseNotifyPersonnel">
                <el-icon><Plus /></el-icon> 选择通知人员
              </el-button>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="通知时间" prop="notifyTime">
            <el-date-picker
              class="!w-240px"
              v-model="form.notifyTime"
              type="date"
              placeholder="通知时间"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="证书设置" prop="certificateName">
            <el-input
              class="!w-240px"
              v-model="form.certificateName"
              disabled
            />
            <div class="ml-4">
              <el-button type="primary" @click="chooseCertificate">
                设置证书
              </el-button>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="任务介绍" prop="taskDesc">
            <el-input
              class="!w-340px"
              v-model="form.taskDesc"
              :autosize="{ minRows: 3, maxRows: 5 }"
              type="textarea"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-tabs v-model="activeName" class="demo-tabs">
            <el-tab-pane label="指定学习内容" name="1" />
          </el-tabs>
          <el-row :gutter="10" class="mb-8px">
            <el-col :span="1.5">
              <el-button type="primary" plain icon="Plus" @click="chooseCourse">
                在线课程
              </el-button>
              <el-button type="primary" plain icon="Plus" @click="createExam">
                考试
              </el-button>
              <el-button
                type="primary"
                plain
                icon="Plus"
                @click="chooseQuestionnaire"
              >
                调查问卷
              </el-button>
              <el-button type="primary" plain icon="Plus" @click="chooseMeans">
                资料
              </el-button>
            </el-col>
          </el-row>
          <el-table
            :data="studyContentData"
            border
            stripe
            class="tableContent"
            row-key="fieldId"
          >
            <el-table-column type="index" width="50" />
            <el-table-column label="内容类型">
              <template #default="scope">
                {{ FIELD_TYPE_LABEL[scope.row.fieldType] }}
              </template>
            </el-table-column>
            <el-table-column label="名称" prop="fieldName" />
            <el-table-column label="选修人员" prop="excludeUserNames" />
            <el-table-column label="前置条件">
              <template #default="scope">
                <el-select
                  ref="selectRef"
                  v-model="scope.row.preconditionsItem"
                  placeholder="无"
                  clearable
                  value-key="fieldPrimaryKey"
                  @change="preconditionSelect($event, scope.row)"
                >
                  <el-option
                    v-for="dict in preconditionsOpts"
                    :key="dict.fieldPrimaryKey"
                    :label="dict.fieldName"
                    :value="dict"
                    :disabled="
                      scope.row.fieldPrimaryKey === dict.fieldPrimaryKey
                    "
                  />
                </el-select>
              </template>
            </el-table-column>
            <el-table-column
              label="操作"
              width="250"
              class-name="small-padding fixed-width"
            >
              <template #default="scope">
                <el-button
                  v-if="scope.row.fieldType === FIELD_TYPE.COURSE"
                  link
                  type="primary"
                  icon="Select"
                  @click="handleSelectCourse(scope.row, scope.$index)"
                  >选择选修人员</el-button
                >
                <el-button
                  link
                  type="danger"
                  icon="Delete"
                  @click="handleDelete(scope.$index)"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </el-col>
        <!-- 课程 -->
        <el-col :span="24" v-if="hasCourse">
          <el-tabs v-model="activeName" class="demo-tabs">
            <el-tab-pane label="任务设置" name="1" />
          </el-tabs>
          <span class="sub_title">课程</span>
          <el-col :span="24" style="margin-top: 20px">
            <el-form-item label="学分" prop="courseCredit">
              <el-input-number v-model="form.courseCredit" :min="0" />
            </el-form-item>
            <el-form-item label="时长" prop="courseDuration">
              <el-input-number v-model="form.courseDuration" :min="0" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="人脸抓拍" prop="faceCapture">
              <el-radio-group v-model="form.faceCapture">
                <el-radio :label="1">是</el-radio>
                <el-radio :label="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="24" v-if="form.faceCapture === 1">
            <el-form-item label="是否记录学分" prop="recordCredit">
              <el-radio-group v-model="form.recordCredit">
                <el-radio :label="1">是</el-radio>
                <el-radio :label="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-col>
        <!-- 资料 -->
        <el-col :span="24" style="margin-top: 20px" v-if="hasMeans">
          <span class="sub_title">资料</span>
          <el-col :span="24" style="margin-top: 20px">
            <el-form-item label="学分" prop="materialCredit">
              <el-input-number v-model="form.materialCredit" :min="0" />
            </el-form-item>
            <el-form-item label="时长" prop="materialDuration">
              <el-input-number v-model="form.materialDuration" :min="0" />
            </el-form-item>
          </el-col>
        </el-col>
        <el-col :span="24" class="flex justify-center items-center mt-4">
          <el-form-item>
            <el-button type="primary" @click="submitForm">提交</el-button>
            <el-button @click="handleBack">返回</el-button>
            <el-button @click="reset">重置</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <!-- 人员选择弹窗 -->
    <ChoosePersonnelOrDeptDialog
      ref="choosePersonnelOrDeptDialogRef"
      @fetch-data="choosePersonnelDone"
    />
    <!-- 课程选择弹窗 -->
    <ChooseCourseDialog
      selectionType="multiple"
      ref="chooseCourseDialogRef"
      @fetch-data="chooseCourseDone"
    />
    <!-- 创建考试弹窗 -->
    <CreateExamDialog ref="createExamDialogRef" @fetch-data="createExamDone" />
    <!-- 课程选择弹窗 -->
    <ChooseQuestionnaireDialog
      ref="chooseQuestionnaireDialogRef"
      @fetch-data="chooseQuestionnaireDone"
    />
    <!-- 设置证书弹窗 -->
    <ChooseCertificateDialog
      ref="chooseCertificateDialogRef"
      @fetch-data="chooseCertificateDone"
    />
    <!-- 查看所选人员弹窗 -->
    <ViewSelectedPersonnelDialog ref="viewSelectedPersonnelDialogRef" />
    <!-- 资料选择弹窗 -->
    <ChooseMeansDialog
      selectionType="multiple"
      ref="chooseMeansDialogRef"
      @fetch-data="chooseMeansDone"
    />
  </div>
</template>

<script setup name="trainingTaskUpload">
  import { isEmpty } from "@/utils/is"
  import ViewSelectedPersonnelDialog from "./components/viewSelectedPersonnelDialog.vue"
  import Sortable from "sortablejs"
  import dayjs from "dayjs"
  import { deptTreeSelect } from "@/api/system/user"
  import {
    addTrainingTask,
    getTrainingTask,
    updateTrainingTask
  } from "@/api/trainingImplement/trainingTask"
  import { cloneDeep } from "lodash-es"
  import { Close, Plus, ArrowUp, ArrowDown } from "@element-plus/icons-vue"

  const route = useRoute()
  const { proxy } = getCurrentInstance()

  // 培训内容类型字典
  const FIELD_TYPE = {
    COURSE: "0", // 课程
    EXAM: "1", // 考试
    QUESTIONNAIRE: "2", // 调查问卷
    MEANS: "3" // 资料
  }

  // 培训内容类型名称映射
  const FIELD_TYPE_LABEL = {
    [FIELD_TYPE.COURSE]: "课程",
    [FIELD_TYPE.EXAM]: "考试",
    [FIELD_TYPE.QUESTIONNAIRE]: "调查问卷",
    [FIELD_TYPE.MEANS]: "资料"
  }

  const flag = ref("add")
  const deptOptions = ref()
  const activeName = ref("1")
  const studyContentData = ref([])

  // 选中的学员列表
  const selectedStudents = ref([])
  // 选中的通知人员列表
  const selectedNotifyPersons = ref([])
  // 通知人员展开/收起状态
  const isNotifyExpanded = ref(false)

  // 显示的通知人员列表（支持展开/收起）
  const displayNotifyPersons = computed(() => {
    if (selectedNotifyPersons.value.length <= 20 || isNotifyExpanded.value) {
      return selectedNotifyPersons.value
    }
    return selectedNotifyPersons.value.slice(0, 20)
  })

  // 切换通知人员展开/收起状态
  const toggleNotifyExpanded = () => {
    isNotifyExpanded.value = !isNotifyExpanded.value
  }

  // 创建拖拽实例
  const initSort = className => {
    const table = document.querySelector(
      `.${className} .el-table__body-wrapper tbody`
    )
    Sortable.create(table, {
      group: "shared",
      animation: 150,
      easing: "cubic-bezier(1, 0, 0, 1)",
      onStart: () => {},
      // 结束拖动事件
      onEnd: ({ newIndex, oldIndex }) => {
        setNodeSort(studyContentData.value, oldIndex, newIndex)
      }
    })
  }
  // 拖拽完成修改数据排序
  const setNodeSort = (data, oldIndex, newIndex) => {
    const currRow = data.splice(oldIndex, 1)[0]
    data.splice(newIndex, 0, currRow)
  }

  // 前置条件下拉框Opts
  const preconditionsOpts = computed(() => {
    return studyContentData.value.map(item => {
      return {
        fieldPrimaryKey: item.fieldType + item.fieldId,
        fieldId: item.fieldId,
        fieldName: item.fieldName,
        fieldType: item.fieldType
      }
    })
  })

  const form = ref({})
  const rules = ref({
    coverPhoto: [
      { required: true, message: "必填项不能为空", trigger: "change" }
    ],
    taskName: [
      { required: true, message: "必填项不能为空", trigger: "change" }
    ],
    startTime: [
      { required: true, message: "必填项不能为空", trigger: "change" }
    ],
    endTime: [{ required: true, message: "必填项不能为空", trigger: "change" }],
    integral: [
      { required: true, message: "必填项不能为空", trigger: "change" }
    ],
    studentUserNames: [
      { required: true, message: "必填项不能为空", trigger: "change" }
    ],
    courseCredit: [{ required: true, message: "请输入学分", trigger: "blur" }],
    courseDuration: [
      { required: true, message: "请输入时长", trigger: "blur" }
    ],
    faceCapture: [
      { required: true, message: "请选择是否人脸抓拍", trigger: "change" }
    ],
    recordCredit: [
      { required: true, message: "请选择是否记录学分", trigger: "change" }
    ],
    materialCredit: [
      { required: true, message: "请输入资料学分", trigger: "blur" }
    ],
    materialDuration: [
      { required: true, message: "请输入资料时长", trigger: "blur" }
    ]
    // notifyUserNames: [
    //   { required: false, message: "请选择通知人员", trigger: "change" }
    // ],
    // notifyTime: [
    //   { required: false, message: "请选择通知时间", trigger: "change" }
    // ]
  })

  /** 查询部门下拉树结构 */
  function getDeptTree() {
    deptTreeSelect().then(response => {
      deptOptions.value = response.data
    })
  }

  const submitForm = () => {
    proxy.$refs["formRef"].validate(async valid => {
      if (!valid) return
      // 学习内容非空校验
      if (!studyContentData.value || studyContentData.value?.length === 0) {
        return proxy.$modal.msgWarning("请先添加指定的学习内容")
      }

      // 根据内容类型动态验证必填项
      if (hasCourse.value) {
        if (
          isEmpty(form.value.courseCredit) ||
          isEmpty(form.value.courseDuration) ||
          form.value.faceCapture === undefined
        ) {
          return proxy.$modal.msgWarning("请完善课程相关设置")
        }
        if (
          form.value.faceCapture === 1 &&
          form.value.recordCredit === undefined
        ) {
          return proxy.$modal.msgWarning("请选择是否记录学分")
        }
      }

      if (hasMeans.value) {
        if (!form.value.materialCredit || !form.value.materialDuration) {
          return proxy.$modal.msgWarning("请完善资料相关设置")
        }
      }

      const submitData = cloneDeep(form.value)
      // 处理传参数据结构
      submitData.coursesIds = []
      submitData.questionIds = []
      submitData.questionnaireIds = []
      submitData.meansIds = []

      studyContentData.value.forEach(item => {
        if (item.fieldType === FIELD_TYPE.COURSE) {
          submitData.coursesIds.push(String(item.fieldId))
        } else if (item.fieldType === FIELD_TYPE.EXAM) {
          submitData.questionIds.push(String(item.fieldId))
        } else if (item.fieldType === FIELD_TYPE.QUESTIONNAIRE) {
          submitData.questionnaireIds.push(String(item.fieldId))
        } else if (item.fieldType === FIELD_TYPE.MEANS) {
          submitData.meansIds.push(String(item.fieldId))
        }
      })
      submitData.coursesIds = [...new Set(submitData.coursesIds)].join()
      submitData.questionIds = [...new Set(submitData.questionIds)].join()
      submitData.questionnaireIds = [
        ...new Set(submitData.questionnaireIds)
      ].join()
      submitData.meansIds = [...new Set(submitData.meansIds)].join()
      submitData.preconditionLinkList = studyContentData.value
      // 找出最大课程下架时间
      const expireTimeArr = studyContentData.value
        .map(item => {
          if (item.offShelfTime) return new Date(item.offShelfTime).getTime()
        })
        .filter(ite => ite !== undefined)
      if (expireTimeArr.length !== 0) {
        const minOffShelfTime = Math.min(...expireTimeArr)
        submitData.minOffShelfTime = dayjs(minOffShelfTime).format(
          "YYYY-MM-DD HH:mm:ss"
        )
      }
      delete submitData.userList

      submitData.preconditionLinkList.forEach(item => {
        delete item.fieldName
      })

      // 实际调用新增 / 修改接口
      const realFn = ["copy", "add"].includes(flag.value)
        ? addTrainingTask
        : updateTrainingTask
      await realFn(submitData)
      reset()
      proxy.$modal.msgSuccess("操作成功")
      proxy.$tab.closeOpenPage({
        path: "/trainingImplement/trainingTask"
      })
    })
  }

  // 选择指定学员
  const choosePersonnel = () => {
    const currentIds = selectedStudents.value
      .map(student => student.userId)
      .join(",")
    const currentNames = selectedStudents.value
      .map(student => student.userName)
      .join(",")
    proxy.$refs["choosePersonnelOrDeptDialogRef"].openDialog(
      currentIds,
      currentNames,
      null,
      "students"
    )
  }

  // 选择通知人员
  const chooseNotifyPersonnel = () => {
    const currentIds = selectedNotifyPersons.value
      .map(person => person.userId)
      .join(",")
    const currentNames = selectedNotifyPersons.value
      .map(person => person.userName)
      .join(",")
    proxy.$refs["choosePersonnelOrDeptDialogRef"].openDialog(
      currentIds,
      currentNames,
      null,
      "notify"
    )
  }

  // 指定学员选择完成
  const choosePersonnelDone = (userList, fieldType) => {
    if (!userList || userList.length === 0) return

    // 根据字段类型判断是指定学员还是通知人员或者是选修人员
    if (fieldType === "students") {
      selectedStudents.value = userList.map(user => ({
        userId: user.userId,
        userName: user.userName
      }))
      form.value.studentUserIds = userList.map(user => user.userId).join()
      form.value.studentUserNames = userList.map(user => user.userName).join()
    } else if (fieldType === "notify") {
      selectedNotifyPersons.value = userList.map(user => ({
        userId: user.userId,
        userName: user.userName
      }))
      form.value.notifyUserIds = userList.map(user => user.userId).join()
      form.value.notifyUserNames = userList.map(user => user.userName).join()
    } else if (typeof fieldType === "number") {
      // 这是课程选修人员选择
      const index = fieldType
      studyContentData.value[index].excludeUserIds = userList
        .map(user => user.userId)
        .join()
      studyContentData.value[index].excludeUserNames = userList
        .map(user => user.userName)
        .join()
    }
  }

  const showViewSelectedPersonnelBtn = ref(false)
  const viewSelectedPersonnel = () => {
    proxy.$refs["viewSelectedPersonnelDialogRef"].openDialog(route.query.taskId)
  }

  // 删除选中的学员
  const handleRemoveStudent = student => {
    const index = selectedStudents.value.findIndex(
      item => item.userId === student.userId
    )
    if (index > -1) {
      selectedStudents.value.splice(index, 1)
      // 更新form中的数据
      form.value.studentUserIds = selectedStudents.value
        .map(user => user.userId)
        .join()
      form.value.studentUserNames = selectedStudents.value
        .map(user => user.userName)
        .join()
    }
  }

  // 删除选中的通知人员
  const handleRemoveNotifyPerson = person => {
    const index = selectedNotifyPersons.value.findIndex(
      item => item.userId === person.userId
    )
    if (index > -1) {
      selectedNotifyPersons.value.splice(index, 1)
      // 更新form中的数据
      form.value.notifyUserIds = selectedNotifyPersons.value
        .map(user => user.userId)
        .join()
      form.value.notifyUserNames = selectedNotifyPersons.value
        .map(user => user.userName)
        .join()
    }
  }

  // 选择课程选修人员
  const handleSelectCourse = (row, index) => {
    proxy.$refs["choosePersonnelOrDeptDialogRef"].openDialog(
      row.excludeUserIds,
      row.excludeUserNames,
      null,
      index
    )
  }
  /** 表单重置 */
  function reset() {
    form.value = {}
    studyContentData.value = []
    selectedStudents.value = []
    selectedNotifyPersons.value = []
  }
  // 选择课程
  const chooseCourseDialogRef = ref()
  const chooseCourse = () => {
    chooseCourseDialogRef.value.openDialog()
  }
  // 选择课程完成
  const chooseCourseDone = row => {
    const rowsList = row.map(item => ({
      fieldPrimaryKey: FIELD_TYPE.COURSE + item.courseId,
      fieldName: item.courseName,
      fieldId: item.courseId,
      offShelfTime: item.offShelfTime,
      fieldType: FIELD_TYPE.COURSE
    }))
    studyContentData.value = [...studyContentData.value, ...rowsList]
    const resultMap = new Map()
    for (const item of studyContentData.value) {
      const { fieldId } = item
      if (!resultMap.has(fieldId)) {
        resultMap.set(fieldId, item)
      }
    }
    // 将Map对象转换回数组形式
    studyContentData.value = Array.from(resultMap.values())
  }

  const createExamDialogRef = ref()
  // 创建考试
  const createExam = () => {
    createExamDialogRef.value.openDialog()
  }
  // 创建考试完成
  const createExamDone = row => {
    studyContentData.value.push({
      fieldPrimaryKey: FIELD_TYPE.EXAM + row.baseId,
      fieldName: row.baseName,
      fieldId: row.baseId,
      fieldType: FIELD_TYPE.EXAM
    })
  }
  const chooseQuestionnaireDialogRef = ref()
  // 添加调查问卷
  const chooseQuestionnaire = () => {
    chooseQuestionnaireDialogRef.value.openDialog()
  }
  // 添加调查问卷完成
  const chooseQuestionnaireDone = row => {
    studyContentData.value.push({
      fieldPrimaryKey: FIELD_TYPE.QUESTIONNAIRE + row.questionnaireId,
      fieldName: row.surveyTitle,
      fieldId: row.questionnaireId,
      fieldType: FIELD_TYPE.QUESTIONNAIRE
    })
  }

  const handleDelete = index => {
    proxy.$modal.confirm("是否确定删除此项？").then(function () {
      studyContentData.value.splice(index, 1)
      proxy.$modal.msgSuccess("删除成功")
    })
  }

  const chooseCertificateDialogRef = ref()
  // 设置证书
  const chooseCertificate = () => {
    chooseCertificateDialogRef.value.openDialog()
  }
  // 证书选择完成
  const chooseCertificateDone = item => {
    form.value.certificateId = item.certTemplateId
    form.value.certificateName = item.templateName
  }

  const preconditionSelect = (val, row) => {
    row.preconditions = val.fieldId
    row.preconditionType = val.fieldType
  }
  const handleBack = () => {
    proxy.$tab.closeOpenPage({
      path: "/trainingImplement/trainingTask"
    })
  }

  const chooseMeansDialogRef = ref()
  const chooseMeans = () => {
    chooseMeansDialogRef.value.openDialog(form.value.meansIds)
  }

  const chooseMeansDone = rows => {
    const rowsList = rows.map(item => ({
      fieldPrimaryKey: FIELD_TYPE.MEANS + item.manageId,
      fieldName: item.manageName,
      fieldId: item.manageId,
      fieldType: FIELD_TYPE.MEANS
    }))
    studyContentData.value = [...studyContentData.value, ...rowsList]
    const resultMap = new Map()
    for (const item of studyContentData.value) {
      const { fieldId } = item
      if (!resultMap.has(fieldId)) {
        resultMap.set(fieldId, item)
      }
    }
    studyContentData.value = Array.from(resultMap.values())
  }

  // 判断是否包含课程和资料的计算属性
  const hasCourse = computed(() => {
    return studyContentData.value.some(
      item => item.fieldType === FIELD_TYPE.COURSE
    )
  })

  const hasMeans = computed(() => {
    return studyContentData.value.some(
      item => item.fieldType === FIELD_TYPE.MEANS
    )
  })

  onMounted(async () => {
    initSort("tableContent")
    getDeptTree()

    // 根据query中的type判断操作类型
    if (route.query.type) {
      flag.value = route.query.type

      // 如果是编辑或复制,需要调用详情接口获取数据
      if (["edit", "copy"].includes(flag.value)) {
        const res = await getTrainingTask(route.query.taskId)

        // 如果是复制,需要删除id并过滤考试内容
        if (flag.value === "copy") {
          delete res.data.taskId
          if (res.data.preconditionLinkList) {
            res.data.preconditionLinkList =
              res.data.preconditionLinkList.filter(
                item => item.fieldType !== FIELD_TYPE.EXAM
              )
          }
        }

        // 设置表单数据
        form.value = res.data
        form.value.faceCapture &&
          (form.value.faceCapture = Number(form.value.faceCapture))
        form.value.recordCredit &&
          (form.value.recordCredit = Number(form.value.recordCredit))

        // 设置学习内容数据
        studyContentData.value = form.value.preconditionLinkList || []
        studyContentData.value.forEach(item => {
          item.fieldPrimaryKey = item.fieldType + item.fieldId
          if (item.preconditions) {
            item.preconditionsItem = {
              fieldPrimaryKey: item.preconditionType + item.preconditions,
              fieldId: Number(item.preconditions),
              fieldName: item.preconditionName,
              fieldType: item.preconditionType
            }
          }
        })

        // 处理指定人员列表的回显
        selectedStudents.value = []
        if (res.data.userList && res.data.userList.length > 0) {
          selectedStudents.value = res.data.userList.map(item => ({
            userId: item.userId || item.id,
            userName: item.name || item.userName
          }))
          form.value.studentUserNames = selectedStudents.value
            .map(user => user.userName)
            .join()
        }

        // 处理通知人员列表的回显
        selectedNotifyPersons.value = []
        if (form.value.notifyUserIds && form.value.notifyUserNames) {
          const notifyUserIds = form.value.notifyUserIds.split(",")
          const notifyUserNames = form.value.notifyUserNames.split(",")
          if (notifyUserIds.length === notifyUserNames.length) {
            selectedNotifyPersons.value = notifyUserIds.map((id, index) => ({
              userId: id,
              userName: notifyUserNames[index]
            }))
          }
        }
        // 处理是否显示查看所选人员按钮
        if (
          form.value.studentUserIds &&
          form.value.studentUserIds.includes(",")
        ) {
          const studentUserIdsArr = form.value.studentUserIds.split(",")
          if (studentUserIdsArr.length > 5) {
            showViewSelectedPersonnelBtn.value = true
          }
        }
      }
    }
  })
</script>
<style lang="scss" scoped>
  :deep(.el-input-number .el-input__wrapper) {
    padding-left: 50px;
    padding-right: 50px;
  }

  .sub_title {
    box-sizing: border-box;
    margin-left: 20px;
    border-bottom: 1px solid black;
    padding: 2px 10px;
  }
</style>
