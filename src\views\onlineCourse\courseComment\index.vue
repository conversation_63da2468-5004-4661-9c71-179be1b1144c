<!--
 * @Description: 课程评论
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-04-07 15:56:22
 * @LastEditTime: 2023-06-01 16:29:34
-->

<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
    >
      <el-form-item label="评论内容" prop="discussContent">
        <el-input
          v-model="queryParams.discussContent"
          placeholder="请输入评论内容"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>

      <el-form-item label="课程名称" prop="courseName">
        <el-input
          v-model="queryParams.courseName"
          placeholder="请输入课程名称"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="评论人" prop="discussBy">
        <el-input
          v-model="queryParams.discussBy"
          placeholder="请输入评论人"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb-8px">
      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="tableData">
      <el-table-column label="评论内容" prop="discussContent" />
      <el-table-column label="所属课程" prop="courseName" width="250" />
      <el-table-column label="评论人" prop="discussBy" width="120" />
      <el-table-column label="评论时间" prop="createTime" width="200" />
      <el-table-column
        label="操作"
        width="200"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <!-- <el-button
            link
            type="primary"
            icon="Edit"
            @click="handleUpdate(scope.row)"
            >查看回复</el-button
          > -->
          <el-button
            link
            type="primary"
            icon="Delete"
            @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script setup name="courseComment">
import { delComment, listComment } from "@/api/onlineCourse/course"

const { proxy } = getCurrentInstance()

const tableData = ref([])
const loading = ref(true)
const showSearch = ref(true)
const total = ref(0)

const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10
  }
})

const { queryParams } = toRefs(data)

/** 查询目录列表 */
function getList() {
  loading.value = true
  listComment(queryParams.value).then((response) => {
    tableData.value = response.rows || []
    total.value = response.total
    loading.value = false
  })
}
/** 搜索按钮操作 */
function handleQuery() {
  getList()
}
/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef")
  handleQuery()
}
/** 修改按钮操作 */
function handleUpdate(row) {
  proxy.$tab.closeOpenPage({
    path: "/onlineCourse/courseAdd",
    query: row
  })
}
/** 删除按钮操作 */
function handleDelete(row) {
  proxy.$modal
    .confirm('是否确认删除评论人为"' + row.discussBy + '"的数据项?')
    .then(function () {
      return delComment(row.traningCourseDiscussId)
    })
    .then(() => {
      getList()
      proxy.$modal.msgSuccess("删除成功")
    })
    .catch(() => {})
}

getList()
</script>
