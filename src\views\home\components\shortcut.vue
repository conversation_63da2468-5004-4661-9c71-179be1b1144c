<!--
 * @Description: 首页-快捷方式
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-04-11 09:59:45
 * @LastEditTime: 2024-03-15 15:14:56
-->
<template>
  <el-card :bordered="false">
    <template #header> <div class="title">快捷方式</div> </template>
    <el-row :gutter="10" v-if="leafNodes.length > 0">
      <el-col :span="6" v-for="(shortcut, index) in leafNodes" :key="index">
        <template v-if="index <= 15">
          <shortcutCard
            :icon="shortcut.meta.icon ? shortcut.meta.icon : 'menu-outlined'"
            :label="shortcut.meta.title"
            @click="leaveFor(shortcut.component)"
          />
        </template>
      </el-col>
    </el-row>
    <el-empty v-else />
  </el-card>
</template>

<script setup name="shortcut">
  import router from "@/router"
  import shortcutCard from "./ShortcutCard"
  import usePermissionStore from "@/store/modules/permission"

  const permissionStore = usePermissionStore()

  const leafNodes = computed(() => permissionStore.leafNodes)

  const leaveFor = (url = "/") => {
    router.replace({
      path: url
    })
  }
</script>

<style scoped>
  .title {
    padding: 8px;
    font-size: 19px;
    font-weight: bolder;
  }
</style>
