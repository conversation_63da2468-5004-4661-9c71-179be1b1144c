<!--
 * @Description: 北蔡防灾减灾-学习情况分布
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-12-18 17:07:43
 * @LastEditTime: 2023-12-20 09:02:42
-->
<template>
  <div class="target-view">
    <div>
      <span class="title">学习情况分布</span>
      <div style="float: right">
        <el-tree-select
          v-model="queryParams.deptId"
          :data="deptOptions"
          :props="{ value: 'id', label: 'label', children: 'children' }"
          value-key="id"
          placeholder="请选择部门"
          style="width: 180px; margin-right: 20px"
          clearable
          @change="deptSelect"
        />

        <ul class="timeSlot">
          <li
            v-for="item in dateList"
            @click="changeDate(item.value)"
            :class="activeVal === item.value ? 'active' : ''"
          >
            {{ item.label }}
          </li>
        </ul>
      </div>
    </div>
    <div class="view-con">
      <Echarts :option="option" />
    </div>
  </div>
</template>

<script setup>
  import { graphic } from "echarts"
  import { deptTreeSelect } from "@/api/system/user"
  import { learnDistribution } from "@/api/home/<USER>"

  const activeVal = ref("0")
  const dateList = ref([
    { label: "近1个月", value: "0" },
    { label: "近6个月", value: "1 " },
    { label: "近1年", value: "2" }
  ])
  const deptOptions = ref([])
  const queryParams = reactive({
    deptId: "",
    sortRange: "0"
  })
  // 查询学习情况
  const getData = () => {
    learnDistribution(queryParams).then(res => {
      setOption(res.data)
    })
  }

  const changeDate = value => {
    activeVal.value = value
    queryParams.sortRange = value
    getData()
  }
  const deptSelect = data => {
    queryParams.deptId = data
    getData()
  }

  const option = ref(null)
  const setOption = data => {
    let xarr = []
    let yarr = []
    for (let item of data) {
      yarr.push(item.user_count)
      xarr.push(item.study_time)
    }
    let rotate = 60
    if (queryParams.sortRange === "1") {
      rotate = 20
    } else if (queryParams.sortRange === "2") {
      rotate = 30
    }
    option.value = {
      xAxis: {
        type: "category",
        data: xarr,
        axisLabel: {
          interval: 0,
          rotate
        },
        boundaryGap: false
      },
      tooltip: {
        trigger: "axis",
        axisPointer: {
          type: "cross",
          label: {
            backgroundColor: "#6a7985"
          }
        }
      },
      yAxis: {
        type: "value"
      },
      grid: {
        left: "20",
        top: "30",
        bottom: "0",
        right: "20",
        containLabel: true
      },
      color: ["rgb(1, 191, 236)"],
      series: [
        {
          data: yarr,
          type: "line",
          smooth: true,
          showSymbol: false,
          areaStyle: {
            opacity: 0.8,
            color: new graphic.LinearGradient(0, 0, 1, 0, [
              {
                offset: 0,
                color: "rgb(128, 255, 165)"
              },
              {
                offset: 1,
                color: "rgb(1, 191, 236)"
              }
            ])
          }
        }
      ]
    }
  }

  const getDeptInfo = () => {
    deptTreeSelect().then(res => {
      let data = res.data
      deptOptions.value = data[0]["children"]
    })
  }

  getData()
  getDeptInfo()
</script>

<style scoped lang="scss">
  .timeSlot {
    display: inline-block;
    .active {
      background: #867ffd;
      color: #fff;
    }
    > li {
      display: inline-block;
      width: 80px;
      line-height: 34px;
      background: #fff;
      border-radius: 5px;
      margin: 0 6px;
      text-align: center;
      font-size: 14px;
      cursor: pointer;
    }
  }
  .title {
    line-height: 40px;
    font-weight: bold;
    margin-left: 10px;
  }
  .view-con {
    background: #fff;
    padding: 14px 20px;
    border-radius: 10px;
    height: 298px;
  }
</style>
