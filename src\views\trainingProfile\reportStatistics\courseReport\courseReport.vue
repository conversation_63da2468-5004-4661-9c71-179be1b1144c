<!--
 * @Description: 课程报表统计列表
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-10-11 09:48:01
 * @LastEditTime: 2024-02-23 16:42:25
-->

<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
    >
      <el-form-item label="课程名称" prop="courseName">
        <el-input
          v-model="queryParams.courseName"
          placeholder="请输入课程名称"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="课程目录" prop="catalogueId">
        <el-tree-select
          v-model="queryParams.catalogueId"
          :data="catalogueOptions"
          :props="{
            value: 'catalogueId',
            label: 'catalogueName',
            children: 'children',
            disabled: 'disabled'
          }"
          clearable
          value-key="catalogueId"
          placeholder="选择课程目录"
          check-strictly
          default-expand-all
        />
      </el-form-item>
      <el-form-item
        label="所属分公司"
        prop="domainName"
        v-if="roles.includes('taibao_admin') || user.userId === 1"
      >
        <el-tree-select
          v-model="queryParams.domainName"
          :data="deptOptions"
          :props="{ value: 'domainName', label: 'label', children: 'children' }"
          value-key="id"
          placeholder="请选择所属分公司"
          check-strictly
          clearable
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb-8px">
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport">
          导出
        </el-button>
      </el-col>
      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="tableData">
      <el-table-column label="序号" width="60" type="index" />
      <el-table-column label="课程名称" prop="courseName" />
      <el-table-column label="课程编号" prop="courseCode" width="120" />
      <el-table-column label="所属目录" prop="catalogueName" />
      <el-table-column label="讲师" prop="lecturer">
        <template #default="scope">
          {{ scope.row.lecturer || "-" }}
        </template>
      </el-table-column>
      <el-table-column label="浏览量" prop="hotValue" width="80" />
      <el-table-column label="学习人数" prop="learnedCount" width="80" />
      <el-table-column label="评分" prop="courseGrade" width="80" />
      <el-table-column label="评论数" prop="discussCount" width="80" />
      <el-table-column label="完成人数" prop="completeCount" width="80" />

      <el-table-column label="创建人" prop="createBy" width="120" />
      <el-table-column label="更新时间" prop="updateTime" width="160" />
      <el-table-column
        label="操作"
        width="120"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-button
            link
            type="primary"
            icon="View"
            @click="handleDetail(scope.row)"
          >
            明细
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script setup name="courseReport">
  import dayjs from "dayjs"
  import { listCourse } from "@/api/onlineCourse/course"
  import { catalogueList } from "@/api/system/catalogue.js"
  import catalogue from "@/utils/catalogue.js"
  import { deptTreeSelect } from "@/api/system/user"
  import useUserStore from "@/store/modules/user"

  const { roles, user } = storeToRefs(useUserStore())
  const { proxy } = getCurrentInstance()
  const tableData = ref([])
  const loading = ref(true)
  const showSearch = ref(true)
  const catalogueOptions = ref([])
  const total = ref(0)
  const emit = defineEmits(["updateCurrentView"])

  const data = reactive({
    queryParams: {
      pageNum: 1,
      pageSize: 10
    }
  })

  const { queryParams } = toRefs(data)

  /** 查询目录列表 */
  function getList() {
    loading.value = true
    let queryData = {
      ...queryParams.value,
      sortField: "a.update_time",
      sortOrder: "desc",
      fromType: "learning"
    }
    listCourse(queryData).then(response => {
      tableData.value = response.rows || []
      total.value = response.total
      loading.value = false
    })
  }
  /** 搜索按钮操作 */
  function handleQuery() {
    getList()
  }
  /** 重置按钮操作 */
  function resetQuery() {
    proxy.resetForm("queryRef")
    handleQuery()
  }

  const handleDetail = row => {
    emit("updateCurrentView", "reportDetails", row)
  }

  /** 导出按钮操作 */
  function handleExport() {
    proxy.download(
      "course/base/export",
      {
        ...queryParams.value
      },
      `课程报表_${dayjs().format("YYYY-MM-DD")}.xlsx`
    )
  }
  /** 查询目录下拉树结构 */
  function getTreeselect() {
    catalogueOptions.value = []
    catalogueList({ catalogueType: catalogue.COURSE_CATALOGUE }).then(
      response => {
        const courseCatalogue = {
          catalogueId: 0,
          catalogueName: "主类目",
          children: [],
          disabled: true
        }
        courseCatalogue.children = proxy.handleTree(
          response.rows,
          "catalogueId"
        )
        catalogueOptions.value.push(courseCatalogue)
      }
    )
  }
  const deptOptions = ref([])
  /** 查询部门下拉树结构 */
  const getBranchOfficeTree = async () => {
    const response = await deptTreeSelect({
      parentId: 100
    })
    deptOptions.value = response.data
  }

  getList()
  getTreeselect()
  if (roles.value.includes("taibao_admin") || user.value.userId === 1) {
    getBranchOfficeTree()
  }
</script>
