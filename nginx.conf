worker_processes  1;

events {
    worker_connections  1024;
}

http {
    include       mime.types;
    default_type  application/octet-stream;
    sendfile        on;

    client_max_body_size 1000m;
    client_header_timeout 120s;
    client_body_timeout 120s;
    client_body_buffer_size 256k; 
    keepalive_timeout  100;

    server {
        listen       81;
        server_name  _;

        location / {
            root   /usr/share/nginx/html/;
            try_files $uri $uri/ /index.html;
            index  index.html index.htm;
        }

        location /prod-api/{
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header REMOTE-HOST $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_pass http://beckwell-gateway.beckwell-edu:1949/;
        }

        # 避免actuator暴露
        if ($request_uri ~ "/actuator") {
            return 403;
        }

        error_page   500 502 503 504  /50x.html;
        location = /50x.html {
            root   html;
        }
        #开启gzip
        gzip  on;  
        #低于1kb的资源不压缩 
        gzip_min_length 1k;
        #压缩级别1-9，越大压缩率越高，同时消耗cpu资源也越多，建议设置在5左右。 
        gzip_comp_level 5; 
        #需要压缩哪些响应类型的资源，多个空格隔开。不建议压缩图片.
        gzip_types text/plain application/javascript application/x-javascript text/javascript text/xml text/css;  
        #配置禁用gzip条件，支持正则。此处表示ie6及以下不启用gzip（因为ie低版本不支持）
        gzip_disable "MSIE [1-6]\.";  
        #是否添加“Vary: Accept-Encoding”响应头
        gzip_vary on;
    }
}