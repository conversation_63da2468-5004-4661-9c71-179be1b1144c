<!--
 * @Description: 首页-大数据看板
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-08-03 08:44:18
 * @LastEditTime: 2023-08-03 09:08:29
-->
<template>
  <el-card :bordered="false">
    <template #header> <div class="title">大数据看板</div> </template>

    <el-descriptions :column="2" size="large" class="mt-4">
      <el-descriptions-item label="总访问量">
        {{ infoData.totalVisits || 0 }}
      </el-descriptions-item>
      <el-descriptions-item label="总访问人数">
        {{ infoData.totalVisitCount || 0 }}人
      </el-descriptions-item>
      <el-descriptions-item label="总学习人数">
        {{ infoData.totalStudyCount || 0 }}人
      </el-descriptions-item>
      <el-descriptions-item label="总学习时长"
        >{{ infoData.totalStudyHour || 0 }}分
      </el-descriptions-item>
      <el-descriptions-item label="总学习课程数"
        >{{ infoData.totalCourse || 0 }}次
      </el-descriptions-item>
      <el-descriptions-item label="总考试场数">
        {{ infoData.totalExams || 0 }}场
      </el-descriptions-item>
    </el-descriptions>
  </el-card>
</template>

<script setup name="basicData">
  const props = defineProps({
    infoData: {
      type: Object,
      default: () => ({})
    }
  })
</script>

<style scoped>
  .title {
    padding: 8px;
    font-size: 19px;
    font-weight: bolder;
  }

  :deep(.el-descriptions__label) {
    font-size: 17px;
    font-weight: bold;
  }
  :deep(.el-descriptions__content) {
    font-size: 16px;
    font-weight: bolder;
  }
</style>
