/*
 * @Description: 微信用户管理相关API
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-04-12 13:56:32
 * @LastEditTime: 2023-04-12 13:58:55
 */

import request from "@/utils/request"
// 查询微信用户列表
export function listWxUser(query) {
  return request({
    url: "/system/wx/user/list",
    method: "get",
    params: query
  })
}

// 查询微信用户管理详细
export function getWxUser(userId) {
  return request({
    url: "/system/wx/user/" + userId,
    method: "get"
  })
}

// 新增微信用户管理
export function addWxUser(data) {
  return request({
    url: "/system/wx/user",
    method: "post",
    data: data
  })
}

// 修改微信用户管理
export function updateWxUser(data) {
  return request({
    url: "/system/wx/user",
    method: "put",
    data: data
  })
}

// 删除微信用户管理
export function delWxUser(userIds) {
  return request({
    url: "/system/wx/user/" + userIds,
    method: "delete"
  })
}
