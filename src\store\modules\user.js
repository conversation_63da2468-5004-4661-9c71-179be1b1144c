/*
 * @Description: userStore
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-02-16 14:11:33
 * @LastEditTime: 2024-09-24 16:50:17
 */
import {
  loginRequest,
  logout,
  getInfo,
  getPublic<PERSON>ey,
  handleUnepLogin
} from "@/api/login"
import { getToken, setToken, removeToken } from "@/utils/auth"
import defAva from "@/assets/images/avatar.png"
import { encrypt } from "@/utils/jsencrypt"
import { hasCommonElement } from "@/utils/common"
import { reviewRoleList } from "@/utils/constant"

const useUserStore = defineStore("user", {
  state: () => ({
    token: getToken(),
    name: "",
    avatar: "",
    roles: [],
    permissions: [],
    user: {}
  }),
  getters: {
    // 判断当前登录人是否是审核角色
    isReviewRole() {
      return hasCommonElement(reviewRoleList, this.roles)
    }
  },
  actions: {
    getPub<PERSON><PERSON><PERSON>() {
      return new Promise((resolve, reject) => {
        getPub<PERSON><PERSON>ey()
          .then(res => {
            resolve(res)
          })
          .catch(error => {
            reject(error)
          })
      })
    },
    // 登录
    login(userInfo) {
      return new Promise((resolve, reject) => {
        this.getPublicKey().then(response => {
          const username = userInfo.username.trim()
          // 调用加密方法(传密码和公钥)
          const password = encrypt(userInfo.password, response.publicKey)
          const code = userInfo.code
          const uuid = userInfo.uuid
          const domainName = localStorage.getItem("domainName") || ""
          loginRequest(username, password, code, uuid, domainName)
            .then(res => {
              let data = res.data
              setToken(data.access_token)
              this.token = data.access_token
              resolve(response.publicKey)
            })
            .catch(error => {
              reject(error)
            })
        })
      })
    },
    // 防灾减灾登录
    async unepLogin(userInfo) {
      return new Promise(async (resolve, reject) => {
        let requestData = { ...userInfo, scene: 1 }
        handleUnepLogin(requestData)
          .then(res => {
            setToken(res.data.access_token)
            this.token = res.data.access_token
            resolve()
          })
          .catch(err => {
            reject(err)
          })
      })
    },
    // 获取用户信息
    async getInfo() {
      const res = await getInfo()
      this.user = res.user
      const avatar =
        this.user.avatar == "" || this.user.avatar == null
          ? defAva
          : this.user.avatar

      if (res.roles && res.roles.length > 0) {
        // 验证返回的roles是否是一个非空数组
        this.roles = res.roles
        this.permissions = res.permissions
      } else {
        this.roles = ["ROLE_DEFAULT"]
      }
      this.name = this.user.userName
      this.avatar = avatar
    },
    // 退出系统
    async logOut() {
      await logout(this.token)
      this.token = ""
      this.roles = []
      this.permissions = []
      removeToken()
    }
  }
})

export default useUserStore
