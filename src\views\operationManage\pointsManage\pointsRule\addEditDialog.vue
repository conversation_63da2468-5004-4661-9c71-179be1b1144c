<template>
  <el-dialog
    v-model="visible"
    :title="!dataForm.carouselId ? '新增' : '修改'"
    :close-on-click-modal="false"
    center
  >
    <el-form
      ref="dataFormRef"
      :model="dataForm"
      :rules="dataRules"
      label-width="130px"
      @keyup.enter="submitHandle()"
    >
      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item label="规则类型" prop="ruleType">
            <el-select
              v-model="dataForm.ruleType"
              clearable
              placeholder="规则类型"
            >
              <el-option
                v-for="dict in devops_integral_rule_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="周期类型" prop="cycle">
            <el-select
              v-model="dataForm.cycle"
              clearable
              placeholder="周期类型"
            >
              <el-option
                v-for="dict in devops_integral_cycle_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="周期最多奖励数" prop="maxNumber">
            <el-input-number
              v-model="dataForm.maxNumber"
              :min="0"
              :max="1000"
              :step="1"
              :precision="1"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="单次奖励分值" prop="singleScore">
            <el-input-number
              :min="0"
              :max="100"
              v-model="dataForm.singleScore"
              :step="1"
              :precision="1"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="是否启用" prop="isUsing">
            <el-radio-group v-model="dataForm.isUsing">
              <el-radio label="1">是</el-radio>
              <el-radio label="2">否</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="submitHandle()">确定</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
  import { getRule, addRule, updateRule } from "@/api/opertaionManage/points.js"

  const emit = defineEmits(["refreshDataList"])

  const { proxy } = getCurrentInstance()
  const { devops_integral_cycle_type, devops_integral_rule_type } =
    proxy.useDict("devops_integral_cycle_type", "devops_integral_rule_type")

  const visible = ref(false)
  const dataFormRef = ref()
  let dataForm = ref({})

  // 打开弹窗事件
  const openDialog = async id => {
    // 重置表单数据
    dataForm.value = {}

    if (id) {
      const { data } = await getRule(id)
      Object.assign(dataForm.value, data)
    }
    visible.value = true
  }

  const dataRules = ref({
    ruleType: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
    cycle: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
    maxNumber: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
    singleScore: [
      { required: true, message: "必填项不能为空", trigger: "blur" }
    ],
    isUsing: [{ required: true, message: "必填项不能为空", trigger: "blur" }]
  })

  // 表单提交
  const submitHandle = () => {
    dataFormRef.value.validate(valid => {
      if (!valid) {
        return false
      }
      if (!dataForm.value.integralRuleId) {
        addRule(dataForm.value).then(res => {
          if (res.code === 200) {
            proxy.$modal.msgSuccess("新增成功")
            visible.value = false
            emit("refreshDataList")
          }
        })
      } else {
        updateRule(dataForm.value).then(res => {
          if (res.code === 200) {
            proxy.$modal.msgSuccess("修改成功")
            visible.value = false
            emit("refreshDataList")
          }
        })
      }
    })
  }

  defineExpose({
    openDialog
  })
</script>
