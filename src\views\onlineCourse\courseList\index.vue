<!--
 * @Description: 课程列表
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-04-06 15:54:25
 * @LastEditTime: 2025-06-26 10:34:19
-->
<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
    >
      <el-form-item label="课程编号" prop="courseCode">
        <el-input
          v-model="queryParams.courseCode"
          placeholder="请输入课程编号"
          clearable
          style="width: 250px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="课程名称" prop="courseName">
        <el-input
          v-model="queryParams.courseName"
          placeholder="请输入课程名称"
          clearable
          style="width: 250px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="课程目录" prop="catalogueId">
        <el-tree-select
          v-model="queryParams.catalogueId"
          :data="catalogueOptions"
          :props="{
            value: 'catalogueId',
            label: 'catalogueName',
            children: 'children',
            disabled: 'disabled'
          }"
          clearable
          value-key="catalogueId"
          placeholder="选择课程目录"
          check-strictly
          default-expand-all
        />
      </el-form-item>
      <el-form-item label="课程标签" prop="courseLabel">
        <el-select
          v-model="queryParams.courseLabel"
          placeholder="课程标签"
          clearable
          style="width: 250px"
        >
          <el-option
            v-for="dict in course_label_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="课程状态" prop="courseStatus">
        <el-select
          v-model="queryParams.courseStatus"
          placeholder="课程状态"
          clearable
          style="width: 250px"
        >
          <el-option
            v-for="dict in course_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb-8px">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd">
          新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
        >
          删除
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport">
          导出
        </el-button>
      </el-col>
      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="tableData"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="50" />
      <el-table-column label="课程封面" prop="courseImage" width="100">
        <template #default="scope">
          <el-popover placement="right" :width="400" trigger="hover">
            <img :src="scope.row.courseImage" width="375" height="375" />
            <template #reference>
              <img
                :src="scope.row.courseImage"
                style="max-height: 60px; max-width: 60px"
              />
            </template>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column label="课程编号" prop="courseCode" width="80" />
      <el-table-column label="课程名称" prop="courseName" width="180">
        <template #default="scope">
          <span class="listLink" @click="previewVideo(scope.row)">
            {{ scope.row.courseName }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="所属目录" prop="catalogueName" width="135" />
      <!-- <el-table-column label="课程等级" prop="courseLevel" width="150">
        <template #default="scope">
          <el-rate v-model="scope.row.courseLevel" disabled />
        </template>
      </el-table-column> -->
      <el-table-column label="课程标签" prop="courseLabel" width="170">
        <template #default="scope">
          <span v-if="scope.row.courseLabel">
            <el-tag
              v-for="(labelValue, index) in scope.row.courseLabel.split(',')"
              :key="index"
              :type="getLabelType(labelValue)"
              style="margin-right: 5px; margin-bottom: 5px;"
            >
              {{ getLabelName(labelValue) }}
            </el-tag>
          </span>
        </template>
      </el-table-column>
      <el-table-column label="用户群体" prop="userGroupsName" width="150" />
      <el-table-column label="场所" prop="location" width="220">
        <template #default="scope">
          <dict-tag :options="course_location" :value="scope.row.location" />
        </template>
      </el-table-column>
      <el-table-column label="备注" prop="remark" width="150" />
      <el-table-column label="课程类型" prop="courseTypeName" width="100" />
      <el-table-column label="所属套餐" prop="belongPackage" width="150" />
      <el-table-column
        v-if="!domainName.value"
        label="上传租户"
        prop="uploadTenant"
      />
      <!-- <el-table-column label="课程价格" prop="coursePrice">
        <template #default="scope">
          {{
            scope.row.coursePrice === null ||
            scope.row.coursePrice === undefined
              ? ""
              : `${scope.row.coursePrice}元`
          }}
        </template>
      </el-table-column>
      <el-table-column label="选修设置" prop="optionalSettings">
        <template #default="scope">
          <dict-tag
            :options="optional_settings"
            :value="scope.row.optionalSettings"
          />
        </template>
      </el-table-column> -->
      <el-table-column label="上架时间" prop="onShelfTime" width="100">
        <template #default="scope">
          {{
            scope.row.onShelfTime &&
            dayjs(scope.row.onShelfTime).format("YYYY-MM-DD")
          }}
        </template>
      </el-table-column>
      <el-table-column label="下架时间" prop="offShelfTime" width="100">
        <template #default="scope">
          {{
            scope.row.offShelfTime &&
            dayjs(scope.row.offShelfTime).format("YYYY-MM-DD")
          }}
        </template>
      </el-table-column>
      <el-table-column label="创建人" prop="createBy" />
      <el-table-column label="更新时间" prop="updateTime" width="160" />
      <el-table-column label="课程状态" prop="courseStatus" width="100" fixed="right">
        <template #default="scope">
          <dict-tag :options="course_status" :value="scope.row.courseStatus" />
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        width="280"
        class-name="small-padding fixed-width"
        fixed="right"
      >
        <template #default="scope">
          <el-button
            link
            type="primary"
            icon="View"
            @click="handleDetail(scope.row)"
            >查看</el-button
          >
          <el-button
            v-if="
              (scope.row.courseStatus == 0 || scope.row.courseStatus == 6) &&
              scope.row.submitFlag == true
            "
            link
            type="primary"
            icon="UploadFilled"
            @click="handleUpload(scope.row)"
            >提交</el-button
          >
          <el-button
            link
            type="primary"
            icon="Edit"
            @click="handleUpdate(scope.row)"
            >修改</el-button
          >
          <el-button
            link
            type="primary"
            icon="Delete"
            @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
    <preivewVideoDialog ref="preivewVideoDialogRef" />
  </div>
</template>

<script setup name="courseList">
  import preivewVideoDialog from "./components/preivewVideoDialog"
  import dayjs from "dayjs"
  import {
    delCourse,
    listCourse,
    approvalCourse
  } from "@/api/onlineCourse/course"
  import { catalogueList } from "@/api/system/catalogue.js"
  import catalogue from "@/utils/catalogue.js"
  import useTenantStore from "@/store/modules/tenant"

  const tenantStore = useTenantStore()
  const { domainName } = storeToRefs(tenantStore)
  const { proxy } = getCurrentInstance()
  const route = useRoute()
  const tableData = ref([])
  const loading = ref(true)
  const showSearch = ref(true)
  const total = ref(0)
  const catalogueOptions = ref([])
  const ids = ref([])
  const single = ref(true)
  const multiple = ref(true)
  const {
    course_location,
    pedu_course_type,
    optional_settings,
    course_label_type,
    course_status
  } = proxy.useDict(
    "course_location",
    "pedu_course_type",
    "optional_settings",
    "course_label_type",
    "course_status"
  )

  const data = reactive({
    queryParams: {
      pageNum: 1,
      pageSize: 10
    }
  })
  const { queryParams } = toRefs(data)

  /** 查询目录列表 */
  function getList() {
    loading.value = true
    let queryData = {
      ...queryParams.value,
      sortField: "a.course_code",
      sortOrder: "asc"
    }
    listCourse(queryData).then(response => {
      tableData.value = response.rows || []
      tableData.value.forEach(item => {
        item.courseLevel = Number(item.courseLevel)
      })
      total.value = response.total
      loading.value = false
    })
  }
  /** 查询目录下拉树结构 */
  function getTreeselect() {
    catalogueOptions.value = []
    catalogueList({ catalogueType: catalogue.COURSE_CATALOGUE }).then(
      response => {
        const courseCatalogue = {
          catalogueId: 0,
          catalogueName: "主类目",
          children: [],
          disabled: true
        }
        courseCatalogue.children = proxy.handleTree(
          response.rows,
          "catalogueId"
        )
        catalogueOptions.value.push(courseCatalogue)
      }
    )
  }
  /** 搜索按钮操作 */
  function handleQuery() {
    getList()
  }
  /** 重置按钮操作 */
  function resetQuery() {
    proxy.resetForm("queryRef")
    handleQuery()
  }
  const handleAdd = () => {
    proxy.$tab.closeOpenPage({
      path: "/onlineCourse/courseAdd",
      query: {
        flag: "add"
      }
    })
  }
  /** 修改按钮操作 */
  function handleUpdate(row) {
    proxy.$tab.closeOpenPage({
      path: "/onlineCourse/courseAdd",
      query: {
        flag: "edit",
        ...row,
        queryParams: JSON.stringify(queryParams.value)
      }
    })
  }
  const handleDetail = row => {
    proxy.$tab.closeOpenPage({
      path: "/onlineCourse/courseAdd",
      query: {
        flag: "detail",
        ...row,
        queryParams: JSON.stringify(queryParams.value)
      }
    })
  }
  /** 删除按钮操作 */
  function handleDelete(row) {
    const courseIds = row.courseId || ids.value
    proxy.$modal
      .confirm('是否确认删除id为"' + courseIds + '"的数据项?')
      .then(function () {
        return delCourse(courseIds)
      })
      .then(() => {
        getList()
        proxy.$modal.msgSuccess("删除成功")
      })
      .catch(() => {})
  }
  /** 选择条数  */
  function handleSelectionChange(selection) {
    ids.value = selection.map(item => item.courseId)
    single.value = selection.length != 1
    multiple.value = !selection.length
  }

  const handleUpload = async row => {
    const res = await approvalCourse({
      courseId: row.courseId,
      courseStatus: row.courseStatus,
      approvalResults: 0
    })
    if (res.code === 200) {
      getList()
      proxy.$modal.msgSuccess("提交成功")
    }
  }
  // 视频预览
  const previewVideo = row => {
    proxy.$refs["preivewVideoDialogRef"].openDialog(row)
  }

  // 获取标签名称
  const getLabelName = (value) => {
    const label = course_label_type.value.find(item => item.value === value)
    return label ? label.label : value
  }

  // 获取标签类型
  const getLabelType = (value) => {
    const label = course_label_type.value.find(item => item.value === value)
    return label ? label.elTagType : 'default'
  }

  /** 导出按钮操作 */
  function handleExport() {
    proxy.download(
      "course/base/export",
      {
        ...queryParams.value,
        sortField: "a.update_time",
        sortOrder: "desc"
      },
      `培训课程基本信息数据_${dayjs().format("YYYY-MM-DD")}.xlsx`
    )
  }

  onMounted(() => {
    if (Object.keys(route.query).length !== 0 && route.query.queryParams) {
      queryParams.value = JSON.parse(route.query.queryParams)
    }
    // 主库默认查询培训学习类课程目录下课程
    // if (!domainName.value) queryParams.value.catalogueId = 2054
    getList()
    getTreeselect()
  })
</script>

<style lang="scss" scoped>
  :deep(.el-table .cell) {
    white-space: pre-line;
  }
</style>
