<!--
 * @Description: 刷新华为云域名CDN缓存弹窗
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-05-11 15:58:21
 * @LastEditTime: 2024-05-28 14:24:05
-->
<template>
  <el-dialog
    v-model="dialogVisible"
    title="域名刷新"
    width="1000"
    @close="handleClose"
  >
    <div>
      <el-row class="mb-15px">
        <el-input
          class="!w-300px mr-20px"
          v-model="currentUrl"
          placeholder="请输入想要修改的域名"
        ></el-input>
        <el-button type="primary" @click="addUrl">添加</el-button>
        <el-button type="warning" @click="handleEmpty">清空</el-button>
      </el-row>
      <el-row>
        <el-button type="primary" @click="handleRefreshAll('eduadmin')"
          >刷新所有租户管理端</el-button
        >
        <el-button type="primary" @click="handleRefreshAll('eduuser')"
          >刷新所有租户用户端</el-button
        >
        <el-button type="primary" @click="handleRefreshAll('eduapp')"
          >刷新所有租户H5端</el-button
        >
      </el-row>
      <h3 class="quick-choose">快速选择</h3>
      <div class="tagList">
        <el-check-tag
          v-for="item in allTenantList"
          :checked="currentDomain === item.domain"
          @change="onChange(item)"
        >
          {{ item.domain || item.name }}
        </el-check-tag>
      </div>
      <el-table :data="tableData" stripe style="width: 100%">
        <el-table-column prop="url" label="域名" />
        <el-table-column label="操作" width="120">
          <template #default="scope">
            <el-button link type="danger" size="small" @click="deleteUrl(scope)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleRefresh"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
  import { listTenant, refreshUrl } from "@/api/system/tenant"

  const emit = defineEmits(["refreshDataList"])
  const { proxy } = getCurrentInstance()
  const allTenantList = ref([])
  const dialogVisible = ref(false)
  const tableData = ref([
    { url: "https://eduadmin.bkehs.com/" },
    { url: "https://eduuser.bkehs.com/" },
    { url: "https://eduapp.bkehs.com/" }
  ])
  const currentUrl = ref("")

  // 打开弹窗事件
  const openDialog = async () => {
    dialogVisible.value = true
    const res = await listTenant({ pageNum: 1, pageSize: 999, status: 0 })
    allTenantList.value = res.rows
    allTenantList.value.unshift({ id: 0, name: "全部", domain: "" })
  }

  // 添加域名
  const addUrl = () => {
    if (currentUrl.value) {
      tableData.value.push({ url: currentUrl.value })
      currentUrl.value = ""
    }
  }

  // 删除域名
  const deleteUrl = scope => {
    proxy.$modal
      .confirm("是否删除此项")
      .then(() => {
        tableData.value.splice(scope.$index, 1)
        proxy.$modal.msgSuccess("删除成功")
      })
      .catch(() => {})
  }

  // 关闭窗口
  const handleClose = () => {
    currentDomain.value = ""
    currentUrl.value = ""
    tableData.value = [
      { url: "https://eduadmin.bkehs.com/" },
      { url: "https://eduuser.bkehs.com/" },
      { url: "https://eduapp.bkehs.com/" }
    ]
  }

  // 确定修改
  const handleRefresh = async () => {
    refreshUrl(tableData.value.map(item => item.url))
    proxy.$modal.msgSuccess("刷新成功")
    emit("refreshDataList")
  }
  const currentDomain = ref("")
  const onChange = item => {
    if (item.id === 0) {
      const domainArr = []
      allTenantList.value.forEach(element => {
        domainArr.push({
          url: `https://${element.domain}eduadmin.bkehs.com/`
        })
        domainArr.push({ url: `https://${element.domain}eduuser.bkehs.com/` })
        domainArr.push({ url: `https://${element.domain}eduapp.bkehs.com/` })
      })
      tableData.value = domainArr
    } else {
      tableData.value = [
        { url: `https://${item.domain}eduadmin.bkehs.com/` },
        { url: `https://${item.domain}eduuser.bkehs.com/` },
        { url: `https://${item.domain}eduapp.bkehs.com/` }
      ]
    }
    currentDomain.value = item.domain
  }

  const handleEmpty = () => {
    tableData.value = []
    currentDomain.value = ""
  }

  const handleRefreshAll = endpoint => {
    const domainArr = [`https://${endpoint}.bkehs.com/`]
    allTenantList.value.forEach(element => {
      if (element.id !== 0) {
        domainArr.push(`https://${element.domain}${endpoint}.bkehs.com/`)
      }
    })
    refreshUrl(domainArr)
    proxy.$modal.msgSuccess("刷新成功")
    emit("refreshDataList")
  }
  defineExpose({
    openDialog
  })
</script>

<style lang="scss" scoped>
  .tagList {
    margin: 10px;
    padding-bottom: 10px;
    :deep(.el-check-tag) {
      margin: 0 10px 10px 0;
      max-width: 200px;
      /*强制文字在一行文本框内*/
      white-space: nowrap;
      /*溢出部分文字隐藏*/
      overflow: hidden;
      /*溢出部分省略号处理*/
      text-overflow: ellipsis;
    }
  }
</style>
