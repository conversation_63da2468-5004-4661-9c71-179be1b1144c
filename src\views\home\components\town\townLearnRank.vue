<!--
 * @Description: 北蔡防灾减灾-居委学习排名
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-12-18 17:07:43
 * @LastEditTime: 2024-04-01 11:46:02
-->

<template>
  <div class="target-view" style="height: 100%">
    <div class="header">
      <span class="title">
        {{ activeTab === "1" ? "居委" : "" }}学习排名
        <el-popover placement="right" :width="280" trigger="hover">
          <template #reference>
            <el-icon><QuestionFilled /></el-icon>
          </template>
          总分（100分）= 学习分（满分80分）+
          季度考试分（满分20分）。学习分可通过视频学习、每月习题、每月回顾获得。季度考试有2次，取最高1次分值计入总分。
        </el-popover>
      </span>
      <div class="right">
        <el-button
          style="margin-right: 10px"
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          >导出</el-button
        >

        <el-date-picker
          v-if="activeTab === '3'"
          v-model="selectedMonth"
          type="month"
          value-format="YYYY-MM"
          @change="dateChange"
        />
        <QuarterPicker
          v-else
          ref="quarterPickerRef"
          v-model="selectedQuarter"
          format="YYYY年第q季度"
          @change="dateChange"
        />
      </div>
    </div>
    <div class="view-con">
      <el-table
        :data="tableData"
        v-el-table-infinite-scroll="load"
        stripe
        height="550"
        style="width: 100%"
        :border="false"
      >
        <el-table-column prop="ranking" label="排名" min-width="70" />
        <template v-if="activeTab === '2'">
          <el-table-column prop="phoneNumber" label="手机号" width="120" />
        </template>
        <el-table-column
          prop="deptName"
          :label="activeTab === '2' ? '商铺' : '居委'"
          min-width="160"
        />
        <el-table-column
          v-if="activeTab === '2'"
          prop="orgName"
          label="所属居委"
          min-width="160"
        />
        <template v-if="activeTab !== '3'">
          <el-table-column prop="score" label="总学分" width="70" />
          <el-table-column prop="courseIntegral" label="视频学分" width="80" />
          <el-table-column prop="monthlyPaperScore" label="每月习题" />
          <el-table-column
            prop="mistakesReviewScore"
            label="习题回顾"
            width="80"
          />
          <el-table-column
            prop="quarterlyPaperScore"
            :label="`${activeTab === '1' ? '季' : '月'}度考试`"
            width="80"
          />
        </template>
        <template v-else>
          <el-table-column prop="learners" label="学习人数" />
          <el-table-column prop="courseCount" label="视频学习个数" />
        </template>
      </el-table>
    </div>
  </div>
</template>

<script setup>
  import {
    getTownCommDistRank,
    getTownStoreRank,
    getTownResidentRank
  } from "@/api/home/<USER>/index"
  import dayjs from "dayjs"

  const props = defineProps({
    activeTab: {
      type: String
    }
  })

  const { proxy } = getCurrentInstance()
  const tableData = ref([])
  const queryParam = reactive({
    pageSize: 20,
    pageNum: 1
  })

  const selectedQuarter = ref(new Date().toString())
  const selectedMonth = ref(dayjs().format("YYYY-MM"))
  const total = ref(0)

  watch(
    () => props.activeTab,
    () => {
      queryParam.pageNum = 1
      getData("once")
    }
  )

  const getData = async flag => {
    let res, queryTimeFrom, queryTimeTo, date
    if (props.activeTab === "1" || props.activeTab === "2") {
      date = new Date(selectedQuarter.value)
      queryTimeFrom = dayjs(
        `${date.getFullYear()}-${Math.floor(date.getMonth() / 3) * 3 + 1}`
      ).format("YYYY-MM")
      queryTimeTo = dayjs(queryTimeFrom).add(2, "month").format("YYYY-MM")
      if (props.activeTab === "1") {
        res = await getTownCommDistRank({
          ...queryParam,
          queryTimeFrom,
          queryTimeTo
        })
      } else {
        res = await getTownStoreRank({
          ...queryParam,
          queryTimeFrom,
          queryTimeTo,
          userType: "02"
        })
      }
    } else {
      res = await getTownResidentRank({
        ...queryParam,
        queryTime: selectedMonth.value,
        userType: "03"
      })
    }
    if (flag === "handleMore") {
      tableData.value = tableData.value.concat(res.rows)
    } else {
      tableData.value = res.rows
    }
    // 居委tab数据一览中获取本季度分数最高居委
    if (flag === "once" && props.activeTab !== "3") {
      proxy.$mitt.emit("getTopDeptName", res.rows[0]?.deptName)
    }
    total.value = res.total
  }
  const handelMore = () => {
    queryParam.pageNum += 1
    getData("handleMore")
  }

  const load = () => {
    if (queryParam.pageNum * queryParam.pageSize < total.value) {
      handelMore()
    }
  }

  // 季度切换
  const dateChange = () => {
    queryParam.pageNum = 1
    getData()
  }

  const handleExport = async () => {
    let queryTimeFrom, queryTimeTo, date
    if (props.activeTab === "1" || props.activeTab === "2") {
      date = new Date(selectedQuarter.value)
      queryTimeFrom = dayjs(
        `${date.getFullYear()}-${Math.floor(date.getMonth() / 3) * 3 + 1}`
      ).format("YYYY-MM")
      queryTimeTo = dayjs(queryTimeFrom).add(2, "month").format("YYYY-MM")
      if (props.activeTab === "1") {
        proxy.download(
          "system/dept/export",
          {
            queryTimeFrom,
            queryTimeTo
          },
          `居委学习排行榜_${dayjs().format("YYYY-MM-DD")}.xlsx`
        )
      } else {
        proxy.download(
          "reportforms/report/shops-export",
          {
            queryTimeFrom,
            queryTimeTo,
            userType: "02"
          },
          `商铺学习排行榜_${dayjs().format("YYYY-MM-DD")}.xlsx`
        )
      }
    } else {
      proxy.download(
        "reportforms/report/resident-export",
        {
          queryTime: selectedMonth.value,
          userType: "03"
        },
        `居民学习排行榜_${dayjs().format("YYYY-MM-DD")}.xlsx`
      )
    }
  }

  getData("once")
</script>

<style scoped lang="scss">
  .header {
    display: flex;
    justify-content: space-between;
  }
  .title {
    line-height: 40px;
    font-weight: bold;
    margin-left: 10px;
    display: flex;
    align-items: center;
    .el-icon {
      margin-left: 5px;
      font-size: 18px;
      color: #fe8b25;
    }
  }
  .view-con {
    background: #fff;
    padding: 14px 20px;
    border-radius: 10px;
  }
</style>
