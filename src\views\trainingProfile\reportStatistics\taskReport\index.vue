<!--
 * @Description: 
 * @Author: <PERSON><PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-08-10 16:54:09
 * @LastEditTime: 2023-08-14 09:31:46
-->
<template>
  <section class="index-container">
    <component
      :is="currentView"
      :row="row"
      @updateCurrentView="updateCurrentView"
    ></component>
  </section>
</template>

<script setup name="taskReport">
  import taskList from "./taskList.vue"
  import taskDetail from "./taskDetail.vue"
  import taskPersonDetail from "./taskPersonDetail.vue"

  const row = ref({})
  const currentView = shallowRef(taskList)

  const updateCurrentView = (view, item) => {
    row.value = item
    if (view === "taskDetail") {
      currentView.value = taskDetail
    } else if (view === "taskList") {
      currentView.value = taskList
    } else if (view === "taskPersonDetail") {
      currentView.value = taskPersonDetail
    }
  }
</script>
