<!--
 * @Description: 试卷列表-入口文件
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-03-02 13:43:59
 * @LastEditTime: 2023-12-11 13:55:15
-->

<template>
  <section class="index-container">
    <component
      :is="currentView"
      :row="row"
      @updateCurrentView="updateCurrentView"
    ></component>
  </section>
</template>

<script setup name="hourStatisticsIndex">
  import paperList from "./paperList"
  import paperPreview from "./paperPreview"

  const row = ref({})
  const currentView = shallowRef(paperList)

  const updateCurrentView = (view, item) => {
    row.value = item
    if (view === "paperPreview") {
      currentView.value = paperPreview
    } else if (view === "paperList") {
      currentView.value = paperList
    }
  }
</script>
