<!--
 * @Description: 预览视频弹窗
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-12-13 09:30:49
 * @LastEditTime: 2025-03-05 15:24:28
-->
<template>
  <el-dialog
    v-model="visible"
    title="预览"
    width="80%"
    center
    @close="dialogClose"
  >
    <div class="video-container">
      <!-- 文档预览 -->
      <div v-if="isWordFile" class="document-preview">
        <div class="document-container">
          <iframe
            v-show="!docError"
            ref="dynamicIframe"
            :src="currentWordUrl"
            frameborder="0"
            class="document-iframe"
            scrolling="auto"
            @load="handleIframeLoad"
            @error="handleIframeError"
          />

          <!-- 加载状态 -->
          <div v-if="docLoading" class="doc-loading">
            <el-icon class="is-loading"><loading /></el-icon>
            <span>文档加载中...</span>
          </div>

          <!-- 错误状态 -->
          <div v-if="docError" class="doc-error">
            <el-icon><warning /></el-icon>
            <span>文档加载失败，请刷新重试</span>
            <el-button type="primary" size="small" @click="retryLoadDocument">重试</el-button>
          </div>
        </div>
      </div>

      <!-- 视频预览 -->
      <div v-else class="video-player">
        <video
          ref="videoRef"
          width="900"
          height="600"
          controlslist="nodownload"
          :src="currentPlayItem.fileUrl"
          controls
        ></video>
      </div>

      <div class="detail-header-catalogue">
        <el-scrollbar>
          <div class="detail-header-catalogue-title">章节目录</div>
          <el-divider />
          <div
            class="detail-header-catalogue-content"
            v-for="(item, index) in passageList"
            :key="item.coursePassageId"
          >
            <div class="passage-title"
              >{{ index + 1 }} . {{ item.coursePassageName }}
            </div>
            <div
              class="passage-file-list"
              v-if="item.videoFileList && item.videoFileList.length > 0"
            >
              <div
                @click="setActive(item2)"
                class="passage-file-item"
                v-for="(item2, index2) in item.videoFileList"
                :key="item2.passageId"
                :class="{
                  active: currentPlayItem?.passageId === item2.passageId
                }"
              >
                <div class="left">
                  {{ item2.fileName || getFileName(item2.fileUrl) }}</div
                >
              </div>
            </div>
          </div>
        </el-scrollbar>
      </div>
    </div>
    <template #footer>
      <el-button @click="visible = false">关闭</el-button>
    </template>
  </el-dialog>
</template>

<script setup name="preivewVideoDialog">
  import { getFileName } from "@/utils/common.js"
  import { listPassage } from "@/api/onlineCourse/passage.js"
  import { Base64 } from "js-base64"
  import { FILE_TYPE } from "@/utils/constant.js"

  const visible = ref(false)
  const openDialog = async row => {
    fetchPassageData(row.courseId)
    visible.value = true
  }

  const passageList = ref([])
  // 获取章节列表
  const fetchPassageData = async courseId => {
    let queryData = {
      courseId,
      pageSize: 999
    }
    const { rows } = await listPassage(queryData)
    passageList.value = rows
    if (passageList.value.length > 0) {
      currentPlayItem.value = passageList.value[0].videoFileList[0]
    }
  }

  const currentPlayItem = ref({})
  const setActive = async item => {
    currentPlayItem.value = item
  }

  const videoRef = ref(null)
  const dialogClose = () => {
    if (!isWordFile.value) {
      videoRef && videoRef.value.pause()
    }
    visible.value = false
  }

  // 文档预览相关
  const docLoading = ref(true)
  const docError = ref(false)
  const dynamicIframe = ref()

  // 判断是否为文档文件
  const isWordFile = computed(() => {
    if (!currentPlayItem.value?.fileUrl) return false
    const fileExt = currentPlayItem.value.fileUrl.split('.').pop().toLowerCase()
    return FILE_TYPE[1].includes(fileExt)
  })

  // 获取文档预览URL
  const currentWordUrl = computed(() => {
    if (!currentPlayItem.value?.fileUrl) return ""
    const fileUrl = currentPlayItem.value.fileUrl
    const encodedUrl = encodeURIComponent(Base64.encode(fileUrl))
    return `${import.meta.env.VITE_APP_KK_URL}/onlinePreview?url=${encodedUrl}`
  })

  // iframe加载完成处理
  const handleIframeLoad = () => {
    docLoading.value = false
  }

  // iframe加载错误处理
  const handleIframeError = () => {
    docLoading.value = false
    docError.value = true
  }

  // 重试加载文档
  const retryLoadDocument = () => {
    docError.value = false
    docLoading.value = true
    if (dynamicIframe.value) {
      dynamicIframe.value.src = currentWordUrl.value
    }
  }

  // 监听文档变化
  watch(
    () => currentPlayItem.value?.fileUrl,
    () => {
      if (isWordFile.value) {
        docLoading.value = true
        docError.value = false
      }
    }
  )

  defineExpose({
    openDialog
  })
</script>

<style lang="scss" scoped>
  .video-container {
    display: flex;
    .video-player {
      flex: 1;
      > video {
        width: 100%;
      }
    }
    .document-preview {
      flex: 1;
      height: 600px;
      position: relative;
      background: #fff;
      border-radius: 4px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      display: flex;
      justify-content: center;

      .document-container {
        width: 100%;
        max-width: 1000px;
        height: 100%;
        position: relative;
        overflow: hidden;
        padding: 20px;
        box-sizing: border-box;

        .document-iframe {
          width: 100%;
          height: 100%;
          border-radius: 4px;
          background: #fff;
        }

        .doc-loading {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 8px;
          background: rgba(255, 255, 255, 0.9);
          padding: 20px;
          border-radius: 4px;

          .el-icon {
            font-size: 24px;
            color: var(--el-color-primary);
          }

          span {
            color: var(--el-text-color-secondary);
            font-size: 14px;
          }
        }

        .doc-error {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 12px;
          background: #fff;
          padding: 24px;
          border-radius: 4px;
          box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

          .el-icon {
            font-size: 32px;
            color: var(--el-color-danger);
          }

          span {
            color: var(--el-text-color-regular);
            font-size: 14px;
          }
        }
      }
    }
    .detail-header-catalogue {
      height: 600px;
      width: 25%;
      background-color: #f1f2f3;
      position: relative;

      .detail-header-catalogue-title {
        margin-top: 20px;
        margin-left: 30px;
      }

      .detail-header-catalogue-content {
        margin-bottom: 10px;
        border-bottom: 1px solid #dcdfe6;
        padding-left: 20px;
        font-weight: 700;
        display: flex;
        flex-direction: column;

        .passage-title {
          display: flex;
          justify-content: space-between;
          font-size: 14px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          width: 100%;
        }

        .passage-file-list {
          padding-bottom: 5px;
          .passage-file-item {
            display: flex;
            justify-content: space-between;
            margin: 5px 30px 5px 22px;
            .left {
              width: 100%;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              font-weight: 500;
            }
          }
        }
      }

      .passage-file-item:hover {
        color: #40acea;
        transition: 0.2s;
        cursor: pointer;
      }

      .completion-icon {
        position: absolute;
        top: 25%;
        left: 30%;

        .iconfont {
          font-size: 130px;
          font-weight: normal;
          color: #d81e06;
        }
      }
    }
  }
  .active {
    color: #40acea;
  }
</style>
