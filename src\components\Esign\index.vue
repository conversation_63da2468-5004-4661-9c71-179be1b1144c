<template>
  <div class="sign-finish">
    <div class="wrap1" v-show="showFull">
      <div class="sign-title">请在区域内签字</div>
      <canvas class="canvas1" ref="canvas1"></canvas>
      <div class="actions">
        <button class="danger" @click="handleClear1">清除</button>
        <button class="warning" @click="handleUndo1">撤销</button>
        <!-- <button class="primary" @click="handleFull">横屏</button> -->
        <!-- <button class="success" @click="handlePreview1">保存</button> -->
      </div>
    </div>
    <!-- <div class="wrap2" v-show="!showFull">
      <div class="actionsWrap">
        <div class="actions">
          <button class="danger" @click="handleClear2">清除</button>
          <button class="warning" @click="handleUndo2">撤销</button>
          <button class="primary" @click="handleFull">竖屏</button>
          <button class="success" @click="handlePreview2">保存</button>
        </div>
      </div>
      <canvas class="canvas" ref="canvas2"></canvas>
    </div> -->
  </div>
</template>

<script setup>
  import SmoothSignature from "smooth-signature"

  //组件电子签名
  const canvas = document.querySelector("canvas")
  // const signature = new SmoothSignature(canvas)
  const showFull = ref(true)
  const canvas2 = ref(null)
  const canvas1 = ref(null)
  const signature1 = ref(null)
  const signature2 = ref(null)
  const emit = defineEmits(["closeson"])

  //坚屏横屏
  const handleFull = () => {
    showFull.value = !showFull.value
  }
  const initSignature1 = () => {
    // const canvas = this.$refs["canvas1"]
    const canvas = canvas1.value
    const options = {
      width: 1000,
      height: 200,
      minWidth: 2,
      maxWidth: 6,
      openSmooth: true,
      // color: "#1890ff",
      bgColor: "#f6f6f6"
    }
    signature1.value = new SmoothSignature(canvas, options)
  }
  const initSignture2 = () => {
    // const canvas = this.$refs["canvas2"]
    const canvas = canvas2.value
    const options = {
      width: window.innerWidth - 120,
      height: window.innerHeight - 80,
      minWidth: 3,
      maxWidth: 10,
      openSmooth: true,
      // color: "#1890ff",
      bgColor: "#f6f6f6"
    }
    signature2.value = new SmoothSignature(canvas, options)
  }

  const handleClear1 = () => {
    const sgn = signature1.value
    sgn.clear()
  }
  const handleClear2 = () => {
    const sgn2 = signature2.value
    sgn2.clear()
  }
  const handleUndo1 = () => {
    const sgn = signature1.value
    sgn.undo()
  }
  const handleUndo2 = () => {
    const sgn2 = signature2.value
    sgn2.undo()
  }
  const handlePreview1 = () => {
    const sgn = signature1.value
    const isEmpty = sgn.isEmpty()
    if (isEmpty) {
      alert("isEmpty")
      return
    }
    // const pngUrl = sgn.getPNG()
    const pngUrl = sgn.getJPG()
    // console.log(pngUrl)
    emit("closeson", pngUrl)
  }
  const handlePreview2 = () => {
    const sgn2 = signature2.value
    const isEmpty = sgn2.isEmpty()
    if (isEmpty) {
      alert("isEmpty")
      return
    }
    const canvas = sgn2.getRotateCanvas(-90)
    const pngUrl = canvas.toDataURL()
    // console.log("pngUrl", pngUrl)
    // 生成JPG
    //signature.getJPG() // 或者 signature.toDataURL('image/jpeg')
  }

  onMounted(() => {
    initSignature1()
    initSignture2()
  })

  // onUnmounted(() => {
  //   removeEventListener()
  // })

  defineExpose({ handlePreview1 })
</script>

<style scoped lang="scss">
  .sign-finish {
    height: 300px;
    width: 100%;
    button {
      height: 32px;
      padding: 0 8px;
      font-size: 12px;
      border-radius: 2px;
    }
    .danger {
      color: #fff;
      background: #ee0a24;
      border: 1px solid #ee0a24;
    }
    .warning {
      color: #fff;
      background: #ff976a;
      border: 1px solid #ff976a;
    }
    .primary {
      color: #fff;
      background: #1989fa;
      border: 1px solid #1989fa;
    }
    .success {
      color: #fff;
      background: #07c160;
      border: 1px solid #07c160;
    }
    canvas {
      border-radius: 10px;
      border: 2px dashed #ccc;
    }
    .wrap1 {
      height: 100%;
      width: 96%;
      margin: auto;
      .actions {
        margin-top: 20px;
        display: flex;
        justify-content: space-around;
        button {
          width: 200px;
          cursor: pointer;
        }
      }
    }
    .wrap2 {
      padding: 15px;
      height: 100%;
      display: flex;
      justify-content: center;
      .actionsWrap {
        width: 50px;
        display: flex;
        justify-content: center;
        align-items: center;
      }
      .canvas {
        flex: 1;
      }
      .actions {
        margin-right: 10px;
        white-space: nowrap;
        transform: rotate(90deg);
        button {
          margin-right: 20px;
        }
      }
    }
  }
</style>
