<!--
 * @Description: 调查问卷列表
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-05-10 16:11:58
 * @LastEditTime: 2024-12-06 14:15:34
-->
<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
    >
      <el-form-item label="调查标题" prop="surveyTitle">
        <el-input
          v-model="queryParams.surveyTitle"
          placeholder="调查标题"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="资料目录" prop="catalogueId">
        <el-tree-select
          v-model="queryParams.catalogueId"
          :data="catalogueOptions"
          :props="{
            value: 'catalogueId',
            label: 'catalogueName',
            children: 'children',
            disabled: 'disabled'
          }"
          clearable
          value-key="catalogueId"
          placeholder="选择资料目录"
          check-strictly
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb-8px">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd">
          新增
        </el-button>
      </el-col>
      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="tableData" show-overflow-tooltip>
      <el-table-column label="问卷封面" prop="questionnaireCover">
        <template #default="scope">
          <el-popover
            v-if="scope.row.questionnaireCover"
            placement="right"
            width="400"
            trigger="hover"
          >
            <template #reference>
              <el-image
                style="width: 80px; height: 80px"
                :src="scope.row.questionnaireCover"
              ></el-image>
            </template>
            <el-image :src="scope.row.questionnaireCover"></el-image>
          </el-popover>
          <div v-else>--</div>
        </template>
      </el-table-column>
      <el-table-column label="调查标题" prop="surveyTitle" />
      <el-table-column label="所属目录" prop="catalogueName" />
      <el-table-column label="提醒规则" prop="reminderRules">
        <template #default="scope">
          <span v-if="scope.row.reminderRules !== 0"
            >过期前{{ scope.row.reminderRules }}天提醒</span
          >
          <span v-else>不提醒</span>
        </template>
      </el-table-column>
      <el-table-column
        label="调查说明"
        prop="surveyDescription"
        min-width="150"
      >
        <template #default="scope">
          <div
            style="
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            "
          >
            {{ scope.row.surveyDescription }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="是否可查看结果" prop="isResult" width="80">
        <template #default="scope">
          <dict-tag :options="is_or_not" :value="scope.row.isResult" />
        </template>
      </el-table-column>
      <el-table-column label="积分" prop="integral" width="70" />
      <el-table-column label="创建人" prop="createBy" width="90" />
      <el-table-column label="创建时间" prop="createTime" />
      <el-table-column
        label="操作"
        width="350"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-button
            v-if="domainName === 'yygf'"
            link
            type="primary"
            icon="Setting"
            @click="handleStatistics(scope.row)"
            >统计</el-button
          >
          <el-button
            link
            type="primary"
            icon="View"
            @click="handleResult(scope.row)"
            >结果</el-button
          >
          <el-button
            link
            type="primary"
            icon="Promotion"
            @click="handleDistribute(scope.row)"
            >下发</el-button
          >
          <el-button
            link
            type="primary"
            icon="Edit"
            @click="handleUpdate(scope.row)"
            >修改</el-button
          >
          <el-button
            link
            type="primary"
            icon="Delete"
            @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script setup name="questionnaireList">
  import {
    delQuestionnaire,
    listQuestionnaire
  } from "@/api/trainingImplement/questionnaire.js"
  import { catalogueList } from "@/api/system/catalogue.js"
  import catalogue from "@/utils/catalogue.js"
  import useTenantStore from "@/store/modules/tenant"

  const tenantStore = useTenantStore()
  const { domainName } = storeToRefs(tenantStore)
  const emit = defineEmits(["updateCurrentView"])
  const { proxy } = getCurrentInstance()
  const { is_or_not } = proxy.useDict("is_or_not")

  const tableData = ref([])
  const loading = ref(true)
  const showSearch = ref(true)
  const total = ref(0)
  const catalogueOptions = ref([])

  const data = reactive({
    queryParams: {
      pageNum: 1,
      pageSize: 10
    }
  })

  const { queryParams } = toRefs(data)

  /** 查询目录列表 */
  function getList() {
    loading.value = true
    listQuestionnaire(queryParams.value).then(response => {
      tableData.value = response.rows || []
      total.value = response.total
      loading.value = false
    })
  }
  /** 查询目录下拉树结构 */
  function getTreeselect() {
    catalogueOptions.value = []
    catalogueList({ catalogueType: catalogue.QUESTIONNAIRE_CATALOGUE }).then(
      response => {
        const questionnaireCatalogue = {
          catalogueId: 0,
          catalogueName: "主类目",
          children: [],
          disabled: true
        }
        questionnaireCatalogue.children = proxy.handleTree(
          response.rows,
          "catalogueId"
        )
        catalogueOptions.value.push(questionnaireCatalogue)
      }
    )
  }
  /** 搜索按钮操作 */
  function handleQuery() {
    getList()
  }
  /** 重置按钮操作 */
  function resetQuery() {
    proxy.resetForm("queryRef")
    handleQuery()
  }
  const handleAdd = () => {
    proxy.$tab.closeOpenPage({
      path: "/questionnaire/questionnaireAdd"
    })
  }
  /** 修改按钮操作 */
  function handleUpdate(row) {
    proxy.$tab.closeOpenPage({
      path: "/questionnaire/questionnaireAdd",
      query: row
    })
  }
  /** 删除按钮操作 */
  function handleDelete(row) {
    proxy.$modal
      .confirm('是否确认删除名称为"' + row.surveyTitle + '"的数据项?')
      .then(function () {
        return delQuestionnaire(row.questionnaireId)
      })
      .then(() => {
        getList()
        proxy.$modal.msgSuccess("删除成功")
      })
      .catch(() => {})
  }

  const handleDistribute = row => {
    emit("updateCurrentView", "distributeList", row)
  }

  const handleResult = row => {
    emit("updateCurrentView", "resultList", row)
  }

  const handleStatistics = row => {
    emit("updateCurrentView", "statisticsList", row)
  }

  onMounted(() => {
    getList()
    getTreeselect()
  })
</script>
<style lang="scss" scoped></style>
