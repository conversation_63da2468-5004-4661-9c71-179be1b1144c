<!--
 * @Description: 任务实施报表人员详情
 * @Author: <PERSON><PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-08-11 09:03:01
 * @LastEditTime: 2025-03-17 10:55:50
-->
<template>
  <div class="p-4">
    <!-- 返回按钮 -->
    <el-button
      type="primary"
      icon="ArrowLeftBold"
      class="mb-8px"
      @click="handleBack"
    >
      返 回
    </el-button>

    <!-- 个人信息卡片 -->
    <div class="bg-gray-50 rounded-lg shadow-md p-6">
      <div class="flex justify-center items-center">
        <!-- 左侧用户信息 -->
        <div class="flex items-center gap-4 px-8">
          <img
            :src="getAssetURL(row.row.avatar)"
            class="w-16 h-16 rounded-full"
          />
          <div class="flex flex-col">
            <span class="text-lg font-bold">{{ row.row.studentName }}</span>
            <span class="text-gray-600">{{ row.row.localDeptName }}</span>
          </div>
        </div>

        <!-- 分割线 -->
        <div class="w-px h-16 bg-gray-300 mx-30"></div>

        <!-- 右侧任务信息 -->
        <div class="flex flex-col items-center px-8">
          <div class="text-lg font-bold mb-2">课程：{{ row.taskName }}</div>
          <div class="flex items-center">
            状态：
            <dict-tag
              :options="completion_status"
              :value="row.row.completionStatus"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 任务流程图容器 -->
    <div class="relative mx-auto px-40 pt-20">
      <!-- 任务节点列表 -->
      <div class="relative">
        <template v-for="(row, rowIndex) in taskRows" :key="rowIndex">
          <!-- 每一行的任务流程 -->
          <div class="relative my-20" :style="getRowContainerStyle(rowIndex)">
            <!-- 节点容器 -->
            <div
              :class="[
                'relative flex items-center h-[120px]',
                rowIndex % 2 === 0 ? 'flex-row' : 'flex-row-reverse'
              ]"
            >
              <!-- 开始节点 -->
              <template v-if="rowIndex === 0">
                <div class="absolute left-30px top-[60px] -translate-y-1/2 z-10">
                  <svg-icon
                    icon-class="start-node.svg"
                    class="text-orange-500"
                    size="50px"
                    color="#ff9800"
                  />
                </div>
              </template>

              <!-- 任务节点列表 -->
              <div class="flex-1 relative">
                <!-- 水平虚线和箭头 -->
                <div
                  class="absolute top-[60px] left-0 right-0 flex items-center"
                  style="height: 2px;"
                >
                  <!-- 固定宽度的虚线容器 -->
                  <div class="w-full mx-20 relative">
                    <div
                      class="absolute inset-0 dashed-line"
                    ></div>
                    <!-- 箭头 -->
                    <template v-for="(_, index) in row.length - 1" :key="index">
                      <div
                        :class="[
                          'absolute top-[-6px] w-3 h-3 border-t-2 border-r-2 border-orange-500 z-10',
                          rowIndex % 2 === 0 ? 'rotate-45' : '-rotate-135'
                        ]"
                        :style="{
                          [rowIndex % 2 === 0 ? 'left' : 'right']: `${
                            getArrowPositions(row.length)[index]
                          }%`
                        }"
                      ></div>
                    </template>
                  </div>
                </div>

                <!-- 节点列表 -->
                <div
                  class="grid gap-4 px-20 min-h-[160px]"
                  :style="{
                    'grid-template-columns': getGridColumns(row.length)
                  }"
                >
                  <template
                    v-for="(item, colIndex) in getRowItems(row, rowIndex)"
                    :key="item.id"
                  >
                    <!-- 节点容器 -->
                    <div
                      class="flex flex-col items-center justify-start h-full relative"
                      :style="{
                        'grid-column': getNodeGridPosition(colIndex, row.length)
                      }"
                    >
                      <!-- 任务名称 -->
                      <div
                        class="text-sm font-medium mb-4 w-full text-center truncate"
                        :title="item.optName"
                      >
                        {{ item.optName }}
                      </div>

                      <!-- 节点圆圈 -->
                      <div
                        class="w-4 h-4 rounded-full border-2 bg-white absolute left-1/2 -translate-x-1/2 -translate-y-1/2 top-[60px] z-10"
                        :class="getNodeBorderColor(item)"
                      ></div>

                      <!-- 状态框 -->
                      <template v-if="['C', 'E'].includes(item.taskType)">
                        <div
                          class="mt-20 px-4 py-2 rounded text-sm min-w-[120px]"
                          :class="getStatusBoxClass(item)"
                        >
                          <!-- 课程类型 -->
                          <template v-if="item.taskType === 'C'">
                            <template v-if="Number(item.process || 0) > 0">
                              <div class="text-center">
                                课程进度：{{ Number(item.process) * 100 }}%
                              </div>
                              <div class="text-center mt-1">
                                {{ formatDuration(item.result) }} /
                                {{ formatDuration(item.duration) }}
                              </div>
                              <div
                                v-if="item.optTime"
                                class="text-xs text-gray-500 mt-1 text-center"
                              >
                                {{ formatTime(item.optTime) }}
                              </div>
                            </template>
                            <template v-else>
                              <div class="text-center">未开始</div>
                            </template>
                          </template>
                          <!-- 考试类型 -->
                          <template v-if="item.taskType === 'E'">
                            <div class="text-center">
                              {{ getExamStatusText(item) }}
                            </div>
                            <div
                              v-if="item.optTime"
                              class="text-xs text-gray-500 mt-1 text-center"
                            >
                              {{ formatTime(item.optTime) }}
                            </div>
                          </template>
                        </div>
                      </template>
                    </div>
                  </template>
                </div>
              </div>

              <!-- 终点节点 -->
              <template v-if="isEndRow(rowIndex, row)">
                <div
                  :class="[
                    'absolute transform -translate-y-1/2 top-[60px] z-10',
                    rowIndex % 2 === 0 ? 'right-[30px]' : 'left-[30px]'
                  ]"
                >
                  <svg-icon
                    icon-class="end-node.svg"
                    class="text-orange-500"
                    size="50px"
                    color="#ff9800"
                  />
                </div>
              </template>
            </div>

            <!-- 垂直连接线和箭头 -->
            <template v-if="rowIndex < taskRows.length - 1">
              <div
                :class="[
                  'absolute w-[2px]',
                  rowIndex % 2 === 0 ? 'right-[80px]' : 'left-[80px]'
                ]"
                :style="{
                  height: getVerticalLineHeight(rowIndex),
                  top: '120px'
                }"
              >
                <!-- 垂直虚线 -->
                <div class="h-full dashed-line-vertical"></div>
                <!-- 垂直箭头 -->
                <div
                  class="absolute left-1/2 -translate-x-1/2 z-10"
                  :style="{
                    top: `calc(${getVerticalLineHeight(rowIndex)} / 2 - 8px)`
                  }"
                >
                  <div
                    class="w-4 h-4 rotate-45 border-b-2 border-r-2 border-orange-500"
                  ></div>
                </div>
              </div>
            </template>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script setup name="taskPersonDetail">
  import { trainingTrackUserDetail } from "@/api/trainingImplement/trainingTask.js"
  import { getAssetURL } from "@/utils/common.js"

  const { proxy } = getCurrentInstance()
  const { completion_status } = proxy.useDict("completion_status")
  const emit = defineEmits(["updateCurrentView"])
  const props = defineProps({
    row: {
      type: Object,
      default: () => ({})
    }
  })

  const detailInfo = ref([])

  /** 查询详情信息 */
  const getDetail = async () => {
    let queryData = {
      taskId: props.row.row.taskId,
      studentUserId: props.row.row.studentUserId
    }
    const response = await trainingTrackUserDetail(queryData)
    detailInfo.value = (response.data || []).map(item => ({
      ...item,
      type: item.optType === 1 ? "course" : "exam"
    }))
  }

  const handleBack = () => {
    emit("updateCurrentView", "taskDetail", {
      taskId: props.row.taskId
    })
  }

  const formatTime = time => {
    return time ? time.split(" ")[0] : ""
  }

  // 计算是否是终点行
  const isEndRow = (rowIndex, currentRow) => {
    // 如果是最后一行，一定是终点
    if (rowIndex === taskRows.value.length - 1) return true

    // 如果不是最后一行，但是当前行的节点数量不足4个，也是终点
    if (currentRow.length < 4) return true

    return false
  }

  // 修改垂直连接线高度计算
  const getVerticalLineHeight = rowIndex => {
    const ROW_HEIGHT = 120 // 每行固定高度
    const VERTICAL_SPACING = 80 // 行间距

    // 计算从当前行底部到下一行顶部的总距离
    const totalHeight = VERTICAL_SPACING + ROW_HEIGHT

    return `${totalHeight}px`
  }

  // 修改原有的taskRows计算逻辑，确保每行最多4个节点
  const taskRows = computed(() => {
    const rows = []
    const itemsPerRow = 4
    let currentRow = []

    detailInfo.value.forEach((item, index) => {
      currentRow.push(item)

      // 当达到每行最大数量或是最后一个元素时，将当前行添加到rows中
      if (
        currentRow.length === itemsPerRow ||
        index === detailInfo.value.length - 1
      ) {
        rows.push([...currentRow])
        currentRow = []
      }
    })

    return rows
  })

  // 获取行项目，偶数行反转
  const getRowItems = (row, rowIndex) => {
    return rowIndex % 2 === 0 ? row : [...row].reverse()
  }

  // 修改节点布局逻辑
  const getNodeGridPosition = (index, totalNodes) => {
    // 计算每个节点应该占据的列数
    const totalColumns = 12 // 使用12列网格系统
    const columnsPerNode = Math.floor(totalColumns / totalNodes)
    const startColumn = index * columnsPerNode + 1

    if (totalNodes === 1) {
      return "6 / span 2" // 居中显示
    }
    if (totalNodes === 2) {
      return index === 0 ? "4 / span 2" : "8 / span 2" // 在4/12和8/12处显示
    }
    if (totalNodes === 3) {
      return `${1 + index * 4} / span 2` // 在2/12、6/12和10/12处显示
    }
    return `${startColumn} / span ${columnsPerNode}` // 均匀分布
  }

  // 修改网格列模板
  const getGridColumns = nodeCount => {
    return "repeat(12, 1fr)" // 统一使用12列网格系统
  }

  // 修改箭头位置计算
  const getArrowPositions = nodeCount => {
    if (nodeCount <= 1) return []
    if (nodeCount === 2) return [50]
    if (nodeCount === 3) return [33, 66]
    return Array.from(
      { length: nodeCount - 1 },
      (_, i) => ((i + 1) * 100) / nodeCount
    )
  }

  // 添加新的计算属性来处理垂直连接线
  const shouldShowVerticalLine = computed(() => {
    return taskRows.value.length > 1
  })

  // 修改行容器的样式计算
  const getRowContainerStyle = rowIndex => {
    return {
      marginTop: rowIndex === 0 ? "0" : "80px",
      marginBottom: shouldShowVerticalLine.value ? "80px" : "0"
    }
  }

  // 获取节点边框颜色
  const getNodeBorderColor = item => {
    const colorMap = {
      C: "border-orange-500", // 课程-橙色
      E: "border-green-500", // 考试-绿色
      Q: "border-gray-500", // 问卷-黑色
      F: "border-gray-500" // 资料-黑色
    }
    return colorMap[item.taskType] || "border-gray-500"
  }

  // 获取状态框样式
  const getStatusBoxClass = item => {
    if (item.taskType === "C") {
      return "border border-orange-500 bg-orange-50"
    }
    if (item.taskType === "E") {
      return "border border-green-500 bg-green-50"
    }
    return ""
  }

  // 格式化时长显示
  const formatDuration = seconds => {
    if (!seconds) return "0分钟"
    const minutes = Math.floor(seconds / 60)
    if (seconds > 0 && minutes === 0) {
      return "1分钟"
    } else {
      return `${minutes}分钟`
    }
  }

  // 获取考试状态文本
  const getExamStatusText = item => {
    if (!item.optTime) return "未完成"
    const score = Number(item.result)
    return `${item.duration} ${score}分`
  }

  onMounted(() => {
    getDetail()
  })
</script>

<style lang="scss" scoped>
  .dashed-line {
    background-image: linear-gradient(90deg, #ff9800 50%, transparent 50%);
    background-size: 12px 2px;
    background-repeat: repeat-x;
    position: absolute;
    width: 100%;
    height: 2px;
  }

  .dashed-line-vertical {
    background-image: linear-gradient(0deg, #ff9800 50%, transparent 50%);
    background-size: 2px 12px;
    background-repeat: repeat-y;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    width: 2px;
    height: 100%;
  }

  /* 添加网格布局容器样式 */
  .grid-container {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;
    padding: 0 4rem;
  }

  /* 确保任务节点垂直对齐 */
  .task-node {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    position: relative;
    padding: 0 1rem;
    height: 100%;
  }

  /* 修改SVG图标颜色 */
  :deep(.svg-icon) {
    fill: #ff9800;
  }

  /* 确保垂直连接线的定位 */
  .vertical-connector {
    position: absolute;
    width: 2px;
    transform: translateX(-50%);
  }

  /* 优化箭头样式 */
  .arrow-down {
    position: absolute;
    left: 50%;
    transform: translate(-50%, -50%) rotate(45deg);
  }

  /* 添加新的grid布局样式 */
  .grid {
    display: grid;
    width: 100%;
    position: relative;
    margin: 0 auto;
    max-width: 1200px;
  }

  /* 确保内容容器高度一致 */
  .content-container {
    min-height: 160px;
    display: flex;
    flex-direction: column;
    align-items: center;
  }
</style>
