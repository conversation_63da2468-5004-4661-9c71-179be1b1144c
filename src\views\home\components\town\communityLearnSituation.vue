<!--
 * @Description: 北蔡防灾减灾-各社区学习情况
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-12-18 17:07:43
 * @LastEditTime: 2024-04-02 15:20:43
-->
<template>
  <div class="target-view">
    <div class="header">
      <span class="title">各社区总分构成情况</span>

      <el-date-picker
        v-if="activeTab === '3'"
        v-model="selectedMonth"
        type="month"
        value-format="YYYY-MM"
        @change="getData"
      />
      <QuarterPicker
        v-else
        ref="quarterPickerRef"
        v-model="selectedQuarter"
        format="YYYY年第q季度"
        @change="getData"
      />
    </div>
    <div class="view-con">
      <Echarts :option="option" />
    </div>
  </div>
</template>

<script setup>
  import dayjs from "dayjs"
  import {
    towncommDist,
    towncommStore,
    towncommResident
  } from "@/api/home/<USER>/index"

  const { proxy } = getCurrentInstance()
  const props = defineProps({
    activeTab: {
      type: String
    }
  })
  const selectedQuarter = ref(new Date().toString())
  const selectedMonth = ref(dayjs().format("YYYY-MM"))
  const getData = async () => {
    let res, queryTimeFrom, queryTimeTo, date
    if (props.activeTab === "1" || props.activeTab === "2") {
      date = new Date(selectedQuarter.value)
      queryTimeFrom = dayjs(
        `${date.getFullYear()}-${Math.floor(date.getMonth() / 3) * 3 + 1}`
      ).format("YYYY-MM")
      queryTimeTo = dayjs(queryTimeFrom).add(2, "month").format("YYYY-MM")

      if (props.activeTab === "1") {
        res = await towncommDist({
          queryTimeFrom,
          queryTimeTo
        })
      } else {
        res = await towncommStore({
          userType: "02",
          queryTimeFrom,
          queryTimeTo
        })
      }
    } else {
      res = await towncommResident({
        userType: "03",
        queryTime: selectedMonth.value
      })
    }
    setOption(res.data)
    proxy.$mitt.emit("event", {
      queryTime: selectedMonth.value,
      queryTimeFrom,
      queryTimeTo,
      deptOption: res.data
    })
  }

  watch(
    () => props.activeTab,
    () => {
      getData()
    }
  )

  const option = ref(null)
  const setOption = data => {
    const color = ["#5470c6", "#fac858"]
    option.value = {
      color,
      legend: {
        data: [props.activeTab === "3" ? "学习人数" : "考试成绩", "学习时长"]
      },
      tooltip: {},
      grid: {
        left: "30",
        top: "60",
        bottom: "30",
        right: "30",
        containLabel: true
      },
      xAxis: {
        type: "category",
        data: data.map(item => item.dept_name)
      },
      yAxis: [
        {
          type: "value",
          name: "分数",
          alignTicks: true,
          axisLine: {
            lineStyle: {
              color: color[0]
            }
          },
          nameTextStyle: {
            lineHeight: 50
          }
        },
        {
          type: "value",
          name: "时长(分钟)",
          alignTicks: true,
          axisLine: {
            lineStyle: {
              color: color[1]
            }
          },
          nameTextStyle: {
            lineHeight: 50
          }
        }
      ],
      series: [
        {
          name: props.activeTab === "3" ? "学习人数" : "考试成绩",
          type: "bar",
          barWidth: "20%",
          yAxisIndex: 0,
          data: data.map(item => {
            if (props.activeTab === "3") {
              return item.learners
            } else {
              return item.avg_paper_score
            }
          })
        },
        {
          name: "学习时长",
          type: "bar",
          barWidth: "20%",
          yAxisIndex: 1,
          data: data.map(item => Math.floor(item.total_study_duration / 60))
        }
      ]
    }
  }

  setTimeout(() => {
    getData()
  }, 1000)
</script>

<style scoped lang="scss">
  .target-view {
    margin-bottom: 20px;
  }
  .header {
    display: flex;
    justify-content: space-between;
  }
  .title {
    line-height: 40px;
    font-weight: bold;
    margin-left: 10px;
  }
  .view-con {
    background: #fff;
    padding: 20px 20px;
    border-radius: 10px;
    height: 578px;
  }
</style>
