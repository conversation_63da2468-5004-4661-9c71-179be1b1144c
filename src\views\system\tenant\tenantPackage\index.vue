<!--
 * @Description: 租户套餐
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-06-13 16:02:54
 * @LastEditTime: 2023-07-06 13:20:23
-->
<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
    >
      <el-form-item label="套餐名" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入套餐名"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="状态"
          clearable
          style="width: 200px"
        >
          <el-option
            v-for="dict in sys_normal_disable"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间" style="width: 308px">
        <el-date-picker
          v-model="dateRange"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb-8px">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd"
          >新增</el-button
        >
      </el-col>
      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="packageList">
      <el-table-column label="套餐编号" prop="id" />
      <el-table-column label="套餐名" prop="name" />
      <el-table-column label="状态" prop="status">
        <template #default="scope">
          <dict-tag :options="sys_normal_disable" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="备注" prop="remark" />
      <el-table-column label="创建时间" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        width="180"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-button
            link
            type="primary"
            icon="Edit"
            @click="handleUpdate(scope.row)"
            >修改</el-button
          >
          <el-button
            link
            type="primary"
            icon="Delete"
            @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改租户对话框 -->
    <el-dialog :title="title" v-model="open" width="700px" append-to-body>
      <el-form ref="packageRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="套餐名" prop="name">
          <el-input v-model="form.name" placeholder="请输入套餐名" />
        </el-form-item>
        <el-form-item label="菜单权限">
          <el-checkbox v-model="menuExpand" @change="handleCheckedTreeExpand">
            展开/折叠
          </el-checkbox>
          <el-checkbox v-model="menuNodeAll" @change="handleCheckedTreeNodeAll">
            全选/全不选
          </el-checkbox>
          <!-- <el-checkbox
            v-model="form.menuCheckStrictly"
            @change="handleCheckedTreeConnect"
          >
            父子联动
          </el-checkbox> -->
          <el-tree
            ref="menuRef"
            class="tree-border"
            :data="menuOptions"
            show-checkbox
            node-key="id"
            empty-text="加载中，请稍候"
            :props="{ label: 'label', children: 'children' }"
          ></el-tree>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="dict in sys_normal_disable"
              :key="dict.value"
              :label="dict.value"
              >{{ dict.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="packagePackage">
  import {
    listPackage,
    addPackage,
    delPackage,
    getPackage,
    updatePackage
  } from "@/api/system/tenant"
  import { treeselect as menuTreeselect } from "@/api/system/menu"

  const { proxy } = getCurrentInstance()
  const { sys_normal_disable } = proxy.useDict("sys_normal_disable")

  const dateRange = ref([])
  const packageList = ref([])
  const open = ref(false)
  const loading = ref(true)
  const showSearch = ref(true)
  const total = ref(0)
  const title = ref("")
  const menuOptions = ref([])
  const menuExpand = ref(false)
  const menuNodeAll = ref(false)
  const menuRef = ref(null)

  const data = reactive({
    form: {},
    queryParams: {
      pageNum: 1,
      pageSize: 10
    },
    rules: {
      name: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
      status: [{ required: true, message: "必填项不能为空", trigger: "blur" }]
    }
  })

  const { queryParams, form, rules } = toRefs(data)

  /** 查询租户套餐列表 */
  function getList() {
    loading.value = true
    listPackage(proxy.addDateRange(queryParams.value, dateRange.value)).then(
      response => {
        packageList.value = response.rows
        total.value = response.total
        loading.value = false
      }
    )
  }
  /** 取消按钮 */
  function cancel() {
    open.value = false
    reset()
  }
  /** 表单重置 */
  function reset() {
    if (menuRef.value != undefined) {
      menuRef.value.setCheckedKeys([])
    }
    menuExpand.value = false
    menuNodeAll.value = false
    form.value = {}
    proxy.resetForm("packageRef")
  }
  /** 搜索按钮操作 */
  function handleQuery() {
    queryParams.value.pageNum = 1
    getList()
  }
  /** 重置按钮操作 */
  function resetQuery() {
    proxy.resetForm("queryRef")
    handleQuery()
  }
  /** 新增按钮操作 */
  function handleAdd() {
    reset()
    getMenuTreeselect()
    open.value = true
    title.value = "添加租户"
  }
  /** 修改按钮操作 */
  function handleUpdate(row) {
    reset()
    getMenuTreeselect()
    getPackage(row.id).then(response => {
      form.value = response.data
      form.value.status = String(form.value.status)
      open.value = true
      title.value = "修改租户套餐"
      let checkedKeys = form.value.menuIds.split(",")
      checkedKeys.forEach(v => {
        setTimeout(() => {
          menuRef.value.setChecked(v, true, false)
        }, 100)
      })
    })
  }
  /** 查询菜单树结构 */
  function getMenuTreeselect() {
    menuTreeselect().then(response => {
      menuOptions.value = response.data
    })
  }
  /** 提交按钮 */
  function submitForm() {
    proxy.$refs["packageRef"].validate(valid => {
      if (valid) {
        form.value.menuIds = getMenuAllCheckedKeys()
        if (!form.value.menuIds) {
          return proxy.$modal.msgWarning("请至少添加一个菜单权限")
        }
        if (form.value.id != undefined) {
          updatePackage(form.value).then(response => {
            proxy.$modal.msgSuccess("修改成功")
            open.value = false
            getList()
          })
        } else {
          addPackage(form.value).then(response => {
            proxy.$modal.msgSuccess("新增成功")
            open.value = false
            getList()
          })
        }
      }
    })
  }
  /** 删除按钮操作 */
  function handleDelete(row) {
    proxy.$modal
      .confirm('是否确认删除套餐编号为"' + row.id + '"的数据项？')
      .then(function () {
        return delPackage(row.id)
      })
      .then(() => {
        getList()
        proxy.$modal.msgSuccess("删除成功")
      })
      .catch(() => {})
  }

  /** 树权限（展开/折叠）*/
  function handleCheckedTreeExpand(value) {
    let treeList = menuOptions.value
    for (let i = 0; i < treeList.length; i++) {
      menuRef.value.store.nodesMap[treeList[i].id].expanded = value
    }
  }
  /** 树权限（全选/全不选） */
  function handleCheckedTreeNodeAll(value) {
    menuRef.value.setCheckedNodes(value ? menuOptions.value : [])
  }
  /** 树权限（父子联动） */
  // function handleCheckedTreeConnect(value) {
  //   form.value.menuCheckStrictly = value ? true : false
  // }
  /** 所有菜单节点数据 */
  function getMenuAllCheckedKeys() {
    // 目前被选中的菜单节点
    let checkedKeys = menuRef.value.getCheckedKeys()
    // 半选中的菜单节点
    let halfCheckedKeys = menuRef.value.getHalfCheckedKeys()
    checkedKeys.unshift.apply(checkedKeys, halfCheckedKeys)
    return checkedKeys.join()
  }

  getList()
</script>
