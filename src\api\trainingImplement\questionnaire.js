/*
 * @Description: 问卷调查相关API
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-05-10 16:12:41
 * @LastEditTime: 2024-12-06 13:14:48
 */

import request from "@/utils/request"
// ================================ 问卷调查 ================================
// 查询问卷调查列表
export function listQuestionnaire(query) {
  return request({
    url: "/course/questionnaire/list",
    method: "get",
    params: query
  })
}

// 查询问卷调查详细
export function getQuestionnaire(questionnaireId) {
  return request({
    url: "/course/questionnaire/" + questionnaireId,
    method: "get"
  })
}

// 新增问卷调查
export function addQuestionnaire(data) {
  return request({
    url: "/course/questionnaire",
    method: "post",
    data: data
  })
}

// 修改问卷调查
export function updateQuestionnaire(data) {
  return request({
    url: "/course/questionnaire",
    method: "put",
    data: data
  })
}

// 删除问卷调查
export function delQuestionnaire(questionnaireId) {
  return request({
    url: "/course/questionnaire/" + questionnaireId,
    method: "delete"
  })
}

// ================================ 问卷调查下发 ================================
// 查询问卷调查下发列表
export function listIssued(query) {
  return request({
    url: "/course/issued/list",
    method: "get",
    params: query
  })
}

// 查询问卷调查下发详细
export function getIssued(issuedId) {
  return request({
    url: "/course/issued/" + issuedId,
    method: "get"
  })
}

// 新增问卷调查下发
export function addIssued(data) {
  return request({
    url: "/course/issued",
    method: "post",
    data: data
  })
}

// 修改问卷调查下发
export function updateIssued(data) {
  return request({
    url: "/course/issued",
    method: "put",
    data: data
  })
}

// 删除问卷调查下发
export function delIssued(questionnaireId) {
  return request({
    url: "/course/issued/" + questionnaireId,
    method: "delete"
  })
}

// ================================ 问卷试题管理 ================================
// 查询问卷试题列表
export function listQuestion(query) {
  return request({
    url: "/course/question/list",
    method: "get",
    params: query
  })
}

// 查询问卷试题详细
export function getQuestion(questionId) {
  return request({
    url: "/course/question/" + questionId,
    method: "get"
  })
}

// 新增问卷试题
export function addQuestion(data) {
  return request({
    url: "/course/question",
    method: "post",
    data: data
  })
}

// 修改问卷试题
export function updateQuestion(data) {
  return request({
    url: "/course/question",
    method: "put",
    data: data
  })
}

// 删除问卷试题
export function delQuestion(questionnaireId) {
  return request({
    url: "/course/question/" + questionnaireId,
    method: "delete"
  })
}

// ================================ 问卷结果 ================================
// 问卷结果列表
export function listQuestionResult(params) {
  return request({
    url: "/course/question/paperList",
    method: "get",
    params
  })
}

// 获取问卷参与人数
export function getParticipantsCount(params) {
  return request({
    url: "/course/questionnaire/getCount",
    method: "get",
    params
  })
}

// 问卷结果详情
export function questionResultDetail(params) {
  return request({
    url: "/course/answer/user-list",
    method: "get",
    params
  })
}


/* 问卷统计 */
export function questionnaireStatistics(params) {
  return request({
    url: "/course/questionnaire/user-list",
    method: "get",
    params
  })
}

