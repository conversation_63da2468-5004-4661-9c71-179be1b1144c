<!--
 * @Description: 首页-任务列表
 * @Author: sun<PERSON><PERSON>
 * @LastEditors: sunyunwu
 * @Date: 2023-09-08 15:40:09
 * @LastEditTime: 2023-10-07 10:00:42
-->
<template>
  <div class="target-list">
    <div>
      任务列表
      <span class="more" @click="handleLink">更多任务</span>
    </div>
    <ul class="list-detail">
      <template v-if="allTaskList.length > 0">
        <li v-for="item in allTaskList">
          <ul class="target-name">
            <li>任务名称：【{{ item.taskName }}】</li>
            <li
              :style="{
                color: item.taskStatus === '3' ? '#67C23A' : '#ed9d32'
              }"
            >
              <span class="icon">
                <el-icon size="20">
                  <i class="iconfont icon-xuanzhong1"></i>
                </el-icon>
              </span>
              <dict-tag
                :options="training_task_status"
                :value="item.taskStatus"
                style="display: inline-block"
              />
            </li>
          </ul>
          <ul class="target-detail">
            <li>发起部门：{{ item.startDeptName }}</li>
            <li>开始时间：{{ item.startTime }}</li>
            <li>结束时间：{{ item.endTime }}</li>
          </ul>
        </li>
      </template>
      <el-empty v-else description="暂无任务"></el-empty>
      <div v-if="showCom" class="load-complete">已加载完毕</div>
    </ul>
  </div>
</template>

<script setup>
  import { taskList } from "@/api/home/<USER>"
  const { proxy } = getCurrentInstance()
  const { training_task_status } = proxy.useDict("training_task_status")
  const showCom = ref(false)
  const router = useRouter()
  const allTaskList = ref([])
  taskList({}).then(res => {
  
    if (res.rows && res.rows.length < 3 && res.rows.length !== 0) {
      showCom.value = true
    } else {
      showCom.value = false
    }
    allTaskList.value = res.rows
  })

  const handleLink = () => {
    router.push({
      path: "/trainingImplement/trainingTask"
    })
  }
</script>

<style scoped lang="scss">
  .load-complete {
    line-height: 44px;
    text-align: center;
    font-size: 14px;
    color: #7f7f7f;
  }
  .target-list {
    color: #222d61;
    > div {
      line-height: 40px;
      font-weight: bold;
      text-indent: 10px;
      .more {
        float: right;
        color: #aaaaaa;
        font-size: 14px;
        font-weight: normal;
        display: inline-block;
        margin-right: 20px;
        cursor: pointer;
      }
    }
  }
  .list-detail {
    background: #fff;
    padding: 14px 20px;
    border-radius: 10px;
    height: 298px;
    overflow: hidden;
    > li {
      border-bottom: 1px dashed #d7d7d7;
      padding: 10px 0;
    }
  }
  .target-name {
    white-space: nowrap;

    li {
      display: inline-block;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      line-height: 30px;
    }
    li:nth-child(1) {
      width: 70%;
      padding-left: 20px;
      font-weight: bold;
    }
    li:nth-child(2) {
      width: 30%;
      text-align: right;
      padding-right: 20px;
      font-size: 14px;
      color: #efa832;
    }
    .icon {
      vertical-align: middle;
      padding-right: 4px;
    }
  }
  .target-detail {
    white-space: nowrap;
    li {
      display: inline-block;
      overflow: hidden;
      text-overflow: ellipsis;
      line-height: 30px;
    }
    li:nth-child(1) {
      width: 40%;
      padding-left: 20px;
      font-size: 16px;
    }
    li:nth-child(2) {
      width: 30%;
      font-size: 14px;
      color: #7f7f7f;
    }
    li:nth-child(3) {
      width: 30%;
      text-align: right;
      padding-right: 20px;
      font-size: 14px;
      color: #7f7f7f;
    }
  }
</style>
