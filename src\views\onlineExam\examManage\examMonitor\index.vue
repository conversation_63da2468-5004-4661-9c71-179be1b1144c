<template>
  <section class="index-container">
    <component
      :is="currentView"
      :row="row"
      @updateCurrentView="updateCurrentView"
    ></component>
  </section>
</template>

<script setup name="examMonitorIndex">
  import monitorList from "./monitorList.vue"
  import personnelList from "./personnelList.vue"
  import examPaperDetails from "./examPaperDetails.vue"

  const row = ref({})
  const currentView = shallowRef(monitorList)

  const updateCurrentView = (view, item) => {
    row.value = item
    if (view === "personnelList") {
      currentView.value = personnelList
    } else if (view === "monitorList") {
      currentView.value = monitorList
    } else if (view === "examPaperDetails") {
      currentView.value = examPaperDetails
    }
  }
</script>
