<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    :close-on-click-modal="false"
    center
  >
    <el-form
      v-if="formType !== 'detail'"
      ref="formRef"
      :model="form"
      :rules="formRules"
      label-width="130px"
    >
      <el-form-item label="证件类型" prop="idType">
        <el-input
          :disabled="formType === 'update'"
          v-model="form.idType"
          placeholder="请输入证件类型"
        />
      </el-form-item>
      <el-form-item label="用户姓名" prop="userName">
        <el-input
          :disabled="formType === 'update'"
          v-model="form.userName"
          placeholder="请输入用户姓名"
        />
      </el-form-item>
      <el-form-item label="证件号" prop="idNumber">
        <el-input
          :disabled="formType === 'update'"
          v-model="form.idNumber"
          placeholder="请输入证件号"
        />
      </el-form-item>
      <el-form-item label="手机号" prop="phone">
        <el-input
          :disabled="formType === 'update'"
          v-model="form.phone"
          placeholder="请输入手机号"
        />
      </el-form-item>
      <!-- <el-form-item label="性别" prop="gender">
        <el-input v-model="form.gender" placeholder="请输入性别" />
      </el-form-item> -->
      <el-form-item label="工作单位" prop="workUnit">
        <el-input
          :disabled="formType === 'update'"
          v-model="form.workUnit"
          placeholder="请输入工作单位"
        />
      </el-form-item>
      <el-form-item label="文化程度" prop="eduLevel">
        <el-input
          :disabled="formType === 'update'"
          v-model="form.eduLevel"
          placeholder="请输入文化程度"
        />
      </el-form-item>
      <!-- <el-form-item label="单位地址" prop="unitAddr">
        <el-input v-model="form.unitAddr" placeholder="请输入单位地址" />
      </el-form-item>
      <el-form-item label="职务" prop="position">
        <el-input v-model="form.position" placeholder="请输入职务" />
      </el-form-item>
      <el-form-item label="职称" prop="profTitle">
        <el-input v-model="form.profTitle" placeholder="请输入职称" />
      </el-form-item> -->
      <el-form-item label="邮箱" prop="email">
        <el-input
          :disabled="formType === 'update'"
          v-model="form.email"
          placeholder="请输入邮箱"
        />
      </el-form-item>
      <el-divider />
      <el-form-item label="审核结果" prop="status">
        <el-radio-group v-model="form.status">
          <el-radio label="1" size="large">通过</el-radio>
          <el-radio label="2" size="large">驳回</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="备注" prop="remarks">
        <el-input
          v-model="form.remarks"
          type="textarea"
          placeholder="请输入内容"
        />
      </el-form-item>
      <!-- <el-form-item label="证件照" prop="idPhoto">
        <el-input v-model="form.idPhoto" placeholder="请输入证件照" />
      </el-form-item>
      <el-form-item label="报名照片" prop="regPhoto">
        <el-input v-model="form.regPhoto" placeholder="请输入报名照片" />
      </el-form-item> -->
    </el-form>
    <template v-if="formType !== 'detail'" #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="submitHandle()">确定</el-button>
    </template>
    <el-descriptions v-if="formType === 'detail'" :column="1" border>
      <el-descriptions-item label="用户姓名">
        {{ form.userName }}
      </el-descriptions-item>
      <el-descriptions-item label="证件类型">
        {{ form.idType }}
      </el-descriptions-item>
      <el-descriptions-item label="证件号">
        {{ form.idNumber }}
      </el-descriptions-item>
      <el-descriptions-item label="手机号">
        {{ form.phone }}
      </el-descriptions-item>
      <el-descriptions-item label="性别">
        {{ form.gender }}
      </el-descriptions-item>
      <el-descriptions-item label="文化程度">
        <dict-tag :options="educational_background" :value="form.eduLevel" />
      </el-descriptions-item>
      <el-descriptions-item label="工作单位">
        {{ form.workUnit }}
      </el-descriptions-item>
      <el-descriptions-item label="单位地址">
        {{ form.unitAddr }}
      </el-descriptions-item>
      <el-descriptions-item label="职务">
        {{ form.position }}
      </el-descriptions-item>
      <el-descriptions-item label="职称">
        {{ form.profTitle }}
      </el-descriptions-item>
      <el-descriptions-item label="邮箱">
        {{ form.email }}
      </el-descriptions-item>
      <el-descriptions-item label="证件照">
        {{ form.idPhoto }}
      </el-descriptions-item>
      <el-descriptions-item label="报名照片">
        {{ form.regPhoto }}
      </el-descriptions-item>
      <el-descriptions-item label="状态">
        {{
          form.status === "0"
            ? "待审核"
            : form.status === "1"
            ? "已通过"
            : "已驳回"
        }}
      </el-descriptions-item>
      <el-descriptions-item label="备注">
        {{ form.remarks }}
      </el-descriptions-item>
    </el-descriptions>
  </el-dialog>
</template>

<script setup>
  import {
    getRegistration,
    addRegistration,
    updateRegistration
  } from "@/api/registrationInfo/index"

  const emit = defineEmits(["refreshDataList"])

  const { proxy } = getCurrentInstance()
  const { educational_background } = proxy.useDict("educational_background")
  const visible = ref(false)
  const formRef = ref()
  let form = ref({})
  const dialogTitle = ref("") // 弹窗的标题
  const formType = ref("") // 表单的类型：create - 新增；update - 修改

  // 打开弹窗事件
  const openDialog = async (type, id) => {
    // 重置表单数据
    form.value = {}
    dialogTitle.value =
      type === "create" ? "新增" : type === "create" ? "修改" : "详情"
    formType.value = type

    if (id) {
      const { data } = await getRegistration(id)
      Object.assign(form.value, data)
    }
    visible.value = true
  }

  const formRules = ref({
    userName: [{ required: true, message: "用户姓名不能为空", trigger: "blur" }]
  })

  // 表单提交
  const submitHandle = () => {
    formRef.value.validate(valid => {
      if (!valid) {
        return false
      }
      if (formType.value === "create") {
        addRegistration(form.value).then(res => {
          if (res.code === 200) {
            proxy.$modal.msgSuccess("新增成功")
            visible.value = false
            emit("refreshDataList")
          }
        })
      } else {
        updateRegistration(form.value).then(res => {
          if (res.code === 200) {
            proxy.$modal.msgSuccess("修改成功")
            visible.value = false
            emit("refreshDataList")
          }
        })
      }
    })
  }

  defineExpose({
    openDialog
  })
</script>
