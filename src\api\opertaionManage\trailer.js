/*
 * @Description:
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-11-15 09:25:20
 * @LastEditTime: 2024-11-15 09:25:38
 */
import request from "@/utils/request"

// 查询公益宣传片列表
export function listVideo(query) {
  return request({
    url: "/devops/video/list",
    method: "get",
    params: query
  })
}

// 查询公益宣传片详细
export function getVideo(psaId) {
  return request({
    url: "/devops/video/" + psaId,
    method: "get"
  })
}

// 新增公益宣传片
export function addVideo(data) {
  return request({
    url: "/devops/video",
    method: "post",
    data: data
  })
}

// 修改公益宣传片
export function updateVideo(data) {
  return request({
    url: "/devops/video",
    method: "put",
    data: data
  })
}

// 删除公益宣传片
export function delVideo(psaId) {
  return request({
    url: "/devops/video/" + psaId,
    method: "delete"
  })
}

// 查询公益宣传片审批记录列表
export function listApprove(query) {
  return request({
    url: "/devops/approve/list",
    method: "get",
    params: query
  })
}

// 查询公益宣传片审批记录详细
export function getApprove(approveId) {
  return request({
    url: "/devops/approve/" + approveId,
    method: "get"
  })
}

// 新增公益宣传片审批记录
export function addApprove(data) {
  return request({
    url: "/devops/approve",
    method: "post",
    data: data
  })
}

// 修改公益宣传片审批记录
export function updateApprove(data) {
  return request({
    url: "/devops/approve",
    method: "put",
    data: data
  })
}

// 删除公益宣传片审批记录
export function delApprove(approveId) {
  return request({
    url: "/devops/approve/" + approveId,
    method: "delete"
  })
}
