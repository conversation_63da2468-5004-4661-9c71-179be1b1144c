<!--
 * @Description: 调查问卷选择弹窗
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-06-16 11:19:47
 * @LastEditTime: 2023-11-02 08:49:52
-->

<template>
  <div class="app-container">
    <el-dialog
      v-model="dialogVisible"
      title="选择调查问卷"
      width="50%"
      top="110px"
      @close="close"
      :key="new Date().getTime()"
      center
    >
      <el-form
        :model="queryParams"
        ref="queryRef"
        :inline="true"
        v-show="showSearch"
      >
        <el-form-item label="调查标题" prop="surveyTitle">
          <el-input
            v-model="queryParams.surveyTitle"
            placeholder="调查标题"
            clearable
            style="width: 240px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="资料目录" prop="catalogueId">
          <el-tree-select
            v-model="queryParams.catalogueId"
            :data="catalogueOptions"
            :props="{
              value: 'catalogueId',
              label: 'catalogueName',
              children: 'children',
              disabled: 'disabled'
            }"
            clearable
            value-key="catalogueId"
            placeholder="选择资料目录"
            check-strictly
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">
            搜索
          </el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
      <el-table
        ref="chooseQuestionnaireTableRef"
        :data="tableData"
        highlight-current-row
        @current-change="handleCurrentChange"
      >
        <el-table-column label="问卷封面" prop="questionnaireCover">
          <template #default="scope">
            <el-popover
              v-if="scope.row.questionnaireCover"
              placement="right"
              width="400"
              trigger="hover"
            >
              <template #reference>
                <el-image
                  style="width: 80px; height: 80px"
                  :src="scope.row.questionnaireCover"
                ></el-image>
              </template>
              <el-image :src="scope.row.questionnaireCover"></el-image>
            </el-popover>
            <div v-else>--</div>
          </template>
        </el-table-column>
        <el-table-column label="调查标题" prop="surveyTitle" />
        <el-table-column label="所属目录" prop="catalogueName" />
        <el-table-column label="提醒规则" prop="reminderRules">
          <template #default="scope">
            <span v-if="scope.row.reminderRules !== 0"
              >过期前{{ scope.row.reminderRules }}天提醒</span
            >
            <span v-else>不提醒</span>
          </template>
        </el-table-column>
        <el-table-column label="调查说明" prop="surveyDescription" />
        <el-table-column label="是否可查看结果" prop="isResult">
          <template #default="scope">
            <dict-tag :options="is_or_not" :value="scope.row.isResult" />
          </template>
        </el-table-column>
        <el-table-column label="积分" prop="integral" width="70" />
        <el-table-column label="创建人" prop="createBy" width="90" />
        <el-table-column label="创建时间" prop="createTime"
      /></el-table>

      <pagination
        class="dialog-pagination"
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
        :autoScroll="false"
      />
      <template #footer>
        <el-button @click="close">取消</el-button>
        <el-button type="primary" @click="save">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="ChooseQuestionnaireDialog">
  import { listQuestionnaire } from "@/api/trainingImplement/questionnaire"
  import { catalogueList } from "@/api/system/catalogue.js"
  import catalogue from "@/utils/catalogue.js"

  const emit = defineEmits(["fetch-data"])
  const { proxy } = getCurrentInstance()

  const tableData = ref([])
  const showSearch = ref(true)
  const dialogVisible = ref(false)
  const total = ref(0)
  const catalogueOptions = ref([])
  const { is_or_not } = proxy.useDict("is_or_not")

  const data = reactive({
    queryParams: {
      pageNum: 1,
      pageSize: 10
    }
  })

  const { queryParams } = toRefs(data)

  //** 弹框打开事件 */
  const openDialog = async () => {
    dialogVisible.value = true
    await getList()
    await getTreeselect()
  }

  /** 查询目录列表 */
  const getList = async () => {
    const res = await listQuestionnaire(queryParams.value)
    if (res.code === 200) {
      tableData.value = res.rows
      total.value = res.total
    }
  }
  /** 查询目录下拉树结构 */
  function getTreeselect() {
    catalogueOptions.value = []
    catalogueList({ catalogueType: catalogue.QUESTIONNAIRE_CATALOGUE }).then(
      response => {
        const courseCatalogue = {
          catalogueId: 0,
          catalogueName: "主类目",
          children: [],
          disabled: true
        }
        courseCatalogue.children = proxy.handleTree(
          response.rows,
          "catalogueId"
        )
        catalogueOptions.value.push(courseCatalogue)
      }
    )
  }

  /** 搜索按钮操作 */
  function handleQuery() {
    getList()
  }
  /** 重置按钮操作 */
  function resetQuery() {
    proxy.resetForm("queryRef")
    handleQuery()
  }
  // 关闭弹框并重置操作
  const close = () => {
    proxy.resetForm("queryRef")
    dialogVisible.value = false
  }
  const save = () => {
    emit("fetch-data", currentRow.value)
    proxy.$modal.msgSuccess("操作成功")
    close()
  }

  const currentRow = ref()
  const handleCurrentChange = val => {
    currentRow.value = val
  }

  defineExpose({
    openDialog
  })
</script>

<style lang="scss" scoped>
  :deep(.pagination-container .el-pagination) {
    position: static;
  }
</style>
