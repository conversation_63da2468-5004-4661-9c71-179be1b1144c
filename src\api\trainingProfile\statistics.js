/*
 * @Description: 个人培训档案-学时统计相关API
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-06-27 11:28:36
 * @LastEditTime: 2023-06-27 11:35:02
 */
import request from "@/utils/request"

// 学时统计列表
export function getTrainingTaskStatistics(params) {
  return request({
    url: "/reportforms/report/list",
    method: "get",
    params
  })
}

// 培训总统计
export function getTrainingStatisticsInfo(params) {
  return request({
    url: "/reportforms/report/statistic",
    method: "get",
    params
  })
}
// 培训课程统计
export function getTrainingCourseList(params) {
  return request({
    url: "/reportforms/report/course-list",
    method: "get",
    params
  })
}
// 培训考试统计
export function getTrainingExamList(params) {
  return request({
    url: "/reportforms/report/exam-list",
    method: "get",
    params
  })
}
// 培训问卷统计
export function getTrainingQuestionnaireList(params) {
  return request({
    url: "/reportforms/report/questionnaire-list",
    method: "get",
    params
  })
}
