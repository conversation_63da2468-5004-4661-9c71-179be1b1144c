<template>
  <div class="navbar" :style="isTaiBao ? 'background-color: #0065b3' : ''">
    <hamburger
      id="hamburger-container"
      :is-active="appStore.sidebar.opened"
      class="hamburger-container"
      @toggleClick="toggleSideBar"
    />
    <breadcrumb
      id="breadcrumb-container"
      class="breadcrumb-container"
      v-if="!settingsStore.topNav"
    />
    <top-nav
      id="topmenu-container"
      class="topmenu-container"
      v-if="settingsStore.topNav"
    />

    <div class="right-menu">
      <template v-if="appStore.device !== 'mobile'">
        <el-tooltip
          content="返回学习平台"
          effect="dark"
          placement="bottom"
          v-if="domainName !== 'eduxd'"
        >
          <div
            class="study-front"
            :style="isTaiBao ? 'color: #fff' : ''"
            @click="goBack"
          >
            <img src="@/assets/images/back.png" alt="" />
            {{
              domainName === "town" ? "北蔡镇安全发展和综合减灾" : ""
            }}学习平台
          </div>
        </el-tooltip>

        <el-tooltip content="全局搜索" effect="dark" placement="bottom">
          <header-search
            id="header-search"
            :style="isTaiBao ? 'color: #fff' : ''"
            class="right-menu-item"
          />
        </el-tooltip>

        <el-tooltip content="全屏显示" effect="dark" placement="bottom">
          <screenfull
            id="screenfull"
            :style="isTaiBao ? 'color: #fff' : ''"
            class="right-menu-item hover-effect"
          />
        </el-tooltip>

        <el-tooltip content="布局大小" effect="dark" placement="bottom">
          <size-select id="size-select" class="right-menu-item hover-effect" />
        </el-tooltip>
      </template>
      <div class="avatar-container">
        <el-dropdown
          @command="handleCommand"
          :style="isTaiBao ? 'color: #fff' : ''"
          class="right-menu-item hover-effect"
          trigger="click"
        >
          <div class="avatar-wrapper">
            <img :src="userStore.avatar" class="user-avatar" />
            <el-icon><caret-bottom /></el-icon>
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <router-link to="/user/profile">
                <el-dropdown-item>个人中心</el-dropdown-item>
              </router-link>
              <el-dropdown-item command="setLayout">
                <span>布局设置</span>
              </el-dropdown-item>
              <el-dropdown-item divided command="goBack">
                <span>返回学习平台</span>
              </el-dropdown-item>
              <el-dropdown-item command="logout">
                <span>退出登录</span>
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ElMessageBox } from "element-plus"
  import Breadcrumb from "./Breadcrumb"
  import TopNav from "./TopNav"
  import Hamburger from "./Hamburger"
  import Screenfull from "./Screenfull"
  import SizeSelect from "./SizeSelect"
  import HeaderSearch from "./HeaderSearch"
  import useAppStore from "@/store/modules/app"
  import useUserStore from "@/store/modules/user"
  import useTenantStore from "@/store/modules/tenant"
  import useSettingsStore from "@/store/modules/settings"
  import { getToken } from "@/utils/auth"
  import { getSubdomain } from "@/utils/common.js"
  import { productionEnvList } from "@/utils/constant"

  const appStore = useAppStore()
  const userStore = useUserStore()
  const settingsStore = useSettingsStore()
  const tenantStore = useTenantStore()
  const { domainName } = storeToRefs(tenantStore)
  const backUrl = import.meta.env.VITE_APP_FRONT_URL
  const env = import.meta.env.VITE_APP_ENV
  const subDomain = getSubdomain()
  const isTaiBao = computed(() => domainName.value === "taibao")

  function toggleSideBar() {
    appStore.toggleSideBar()
  }

  function handleCommand(command) {
    switch (command) {
      case "setLayout":
        setLayout()
        break
      case "logout":
        logout()
        break
      case "goBack":
        goBack()
        break
      default:
        break
    }
  }

  function logout() {
    ElMessageBox.confirm("确定注销并退出系统吗？", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
    })
      .then(() => {
        userStore.logOut().then(() => {
          location.href = "/index"
        })
      })
      .catch(() => {})
  }

  const emits = defineEmits(["setLayout"])
  function setLayout() {
    emits("setLayout")
  }

  const goBack = () => {
    const testTenant = localStorage.getItem("testTenant")
    if (productionEnvList.includes(env)) {
      window.location.href = `https://${subDomain}${backUrl}?token=${getToken()}`
    } else {
      window.location.href = `${backUrl}?token=${getToken()}&testTenant=${testTenant}`
    }
  }
</script>

<style lang="scss" scoped>
  .navbar {
    height: 50px;
    overflow: hidden;
    position: relative;
    background: #fff;
    box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);

    .hamburger-container {
      line-height: 46px;
      height: 100%;
      float: left;
      cursor: pointer;
      transition: background 0.3s;
      -webkit-tap-highlight-color: transparent;

      &:hover {
        background: rgba(0, 0, 0, 0.025);
      }
    }

    .breadcrumb-container {
      float: left;
    }

    .topmenu-container {
      position: absolute;
      left: 50px;
    }

    .errLog-container {
      display: inline-block;
      vertical-align: top;
    }

    .right-menu {
      float: right;
      height: 100%;
      line-height: 50px;
      display: flex;
      .study-front {
        font-size: 18px;
        height: auto;
        cursor: pointer;
        margin-right: 15px;
        color: #5a5e66;
        font-weight: bolder;

        > img {
          width: 2em;
          height: 2em;
          line-height: 2em;
          vertical-align: -10px;
        }
      }
      &:focus {
        outline: none;
      }

      .right-menu-item {
        display: inline-block;
        padding: 0 8px;
        height: 100%;
        font-size: 18px;
        color: #5a5e66;
        vertical-align: text-bottom;

        &.hover-effect {
          cursor: pointer;
          transition: background 0.3s;

          &:hover {
            background: rgba(0, 0, 0, 0.025);
          }
        }
      }

      .avatar-container {
        margin-right: 40px;

        .avatar-wrapper {
          margin-top: 5px;
          position: relative;

          .user-avatar {
            cursor: pointer;
            width: 40px;
            height: 40px;
            border-radius: 10px;
          }

          i {
            cursor: pointer;
            position: absolute;
            right: -20px;
            top: 25px;
            font-size: 12px;
          }
        }
      }
    }
  }
</style>
