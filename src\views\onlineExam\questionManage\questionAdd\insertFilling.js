/*
 * @Description: 富文本自定义插入填空功能
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-02-28 11:00:41
 * @LastEditTime: 2023-03-24 09:25:59
 */
export default class InsertGapFillingMenu {
  constructor() {
    this.title = "插入填空" // 自定义菜单标题
    this.tag = "button"
    this.currentIndex = 1
  }

  // 获取菜单执行时的 value ，用不到则返回空 字符串或 false
  getValue(editor) {
    return ""
  }

  // 菜单是否需要激活（如选中加粗文本，“加粗”菜单会激活），用不到则返回 false
  isActive(editor) {
    return false
  }

  // 菜单是否需要禁用（如选中 H1 ，“引用”菜单被禁用），用不到则返回 false
  isDisabled(editor) {
    return false
  }

  // 点击菜单时触发的函数  value 即 this.value(editor) 的返回值
  exec(editor, value) {
    if (this.isDisabled(editor)) return
    editor.dangerouslyInsertHtml(
      `&nbsp;&nbsp;&nbsp;&nbsp;填空&nbsp;&nbsp;&nbsp;&nbsp;`
    )
  }
}
