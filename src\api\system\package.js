/*
 * @Description: 课程套餐相关API
 * @Author: <PERSON><PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-06-28 14:42:13
 * @LastEditTime: 2023-07-07 08:46:45
 */
import request from "@/utils/request"

// 查询课程套餐列表
export function listPackage(query) {
  return request({
    url: "/system/course/package/list",
    method: "get",
    params: query
  })
}

// 新增课程套餐
export function addPackage(data) {
  return request({
    url: "/system/course/package",
    method: "post",
    data: data
  })
}

// 修改课程套餐
export function updatePackage(data) {
  return request({
    url: "/system/course/package",
    method: "put",
    data: data
  })
}

// 删除课程套餐
export function delPackage(packageId) {
  return request({
    url: "/system/course/package/" + packageId,
    method: "delete"
  })
}

// ================================== 套餐详情 ==================================
// 查询课程套餐详细
export function getPackageById(packageId) {
  return request({
    url: "/system/course/package/" + packageId,
    method: "get"
  })
}

// 查询课程套餐明细列表
export function listDetail(query) {
  return request({
    url: "/system/course/package/detail/list",
    method: "get",
    params: query
  })
}

// 批量新增课程套餐明细
export function batchInsert(data) {
  return request({
    url: "/system/course/package/detail/batchInsert",
    method: "post",
    data: data
  })
}

// 删除课程套餐明细
export function delDetail(id) {
  return request({
    url: "/system/course/package/detail/" + id,
    method: "delete"
  })
}

// 获取可分配容量
export function getAvailableCapacity() {
  return request({
    url: "/system/tenant/capacityInfo",
    method: "get"
  })
}
