<!--
 * @Description: 上传视频
 * @Author: <PERSON><PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-05-08 11:06:23
 * @LastEditTime: 2024-07-08 16:50:04
-->
<template>
  <el-dialog v-model="visible" title="新增章节" center>
    <el-form
      ref="dataFormRef"
      :model="dataForm"
      :rules="dataRules"
      label-width="130px"
    >
      <el-row :gutter="10">
        <el-col :span="24">
          <el-form-item label="章节名称" prop="coursePassageName">
            <el-input v-model="dataForm.coursePassageName" />
          </el-form-item>
        </el-col>
        <!-- <el-col :span="24">
          <el-form-item label="视频形式" prop="videoType">
            <el-radio-group v-model="dataForm.videoType">
              <el-radio-button label="SC">上传文件</el-radio-button>
              <el-radio-button label="WL">网络视频</el-radio-button>
            </el-radio-group>
          </el-form-item>
        </el-col> -->
        <el-col :span="24">
          <el-form-item label="章节文件" prop="videoFileList">
            <VideoUpload
              v-model:videoFileList="dataForm.videoFileList"
              :fileSize="2048"
              :limit="30"
              :fileType="FILE_COURSE_TYPE_MAP[COURSE_TYPE.VIDEO_COURSE]"
              :canDownload="false"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="submitHandle()">确定</el-button>
    </template>
  </el-dialog>
</template>

<script setup name="chooseVideoDialog">
  import { COURSE_TYPE, FILE_COURSE_TYPE_MAP } from "@/utils/constant.js"

  const { proxy } = getCurrentInstance()
  const visible = ref(false)
  const dataForm = ref({})
  const dataFormRef = ref()
  const props = defineProps({
    courseId: {
      type: String
    }
  })

  const emit = defineEmits(["refreshDataList"])

  const dataRules = ref({
    coursePassageName: [
      { required: true, message: "必填项不能为空", trigger: "blur" }
    ],
    videoType: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
    videoFileList: [
      { required: true, message: "必填项不能为空", trigger: "blur" }
    ]
  })

  const editIndex = ref(null)
  const openDialog = async (row, index) => {
    editIndex.value = index
    dataForm.value = {}
    if (row) {
      dataForm.value = JSON.parse(JSON.stringify(row))
    }
    visible.value = true
    if (props.courseId) {
      dataForm.value.courseId = props.courseId
    }
  }

  // 表单提交
  const submitHandle = () => {
    dataFormRef.value.validate(valid => {
      if (!valid) {
        return false
      }
      if (!editIndex.value) {
        proxy.$modal.msgSuccess("新增成功")
        visible.value = false
        emit("refreshDataList", dataForm.value)
        editIndex.value = null
      } else {
        proxy.$modal.msgSuccess("修改成功")
        visible.value = false
        emit("refreshDataList", dataForm.value, editIndex.value)
        editIndex.value = null
      }
    })
  }

  defineExpose({
    openDialog
  })
</script>
