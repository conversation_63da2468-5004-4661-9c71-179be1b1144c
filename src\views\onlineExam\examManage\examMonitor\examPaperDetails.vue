<!--
 * @Description: 个人考试明细
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-08-10 14:11:31
 * @LastEditTime: 2024-05-28 16:23:15
-->

<template>
  <div class="app-container">
    <div class="header-info">
      <el-icon class="backIcon" size="20" @click="handleBack">
        <ArrowLeftBold />
      </el-icon>
      <div class="personal-info">
        <div class="info-left">
          <div class="avatar">
            <img src="@/assets/images/avatar.png" alt="" />
          </div>
          <div class="info-text">
            <div class="info-line">
              姓名： {{ completedPaperInfo?.answerUserName }}
            </div>
            <div class="info-line">
              所属部门： {{ completedPaperInfo?.deptName }}
            </div>
          </div>
        </div>
        <div class="signature">
          <img
            crossorigin="anonymous"
            :src="completedPaperInfo?.signature"
            alt=""
          />
        </div>
      </div>
    </div>
    <div class="exam-info">
      <div class="base-info">
        <el-row :gutter="10">
          <el-col :span="7">
            试卷总分：
            <span class="bold-text">
              {{ completedPaperInfo?.paperScore }}分
            </span>
          </el-col>
          <el-col :span="7">
            通过条件：
            <span class="bold-text">
              {{ completedPaperInfo?.lowestScore }}分
            </span>
          </el-col>
          <el-col :span="10">
            本场考试通过状态：
            <span
              class="bold-text"
              :style="
                completedPaperInfo?.isPass === '通过'
                  ? 'color: #04c877;'
                  : 'color: #ff4d4f;'
              "
              >{{ completedPaperInfo?.isPass }}
            </span>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="7">
            考试得分：
            <span class="bold-text">
              {{ completedPaperInfo?.userPaperScore }}分
            </span>
          </el-col>
          <el-col :span="7">
            考试用时：
            <span class="bold-text">
              {{
                formatSeconds(
                  calculateTimeDifference(
                    completedPaperInfo?.startTime,
                    completedPaperInfo?.submitTime
                  )
                )
              }}
            </span>
          </el-col>
          <el-col :span="10">
            提交时间：
            <span class="bold-text">{{ completedPaperInfo?.submitTime }}</span>
          </el-col>
        </el-row>
      </div>
      <div class="export-info">
        <div class="exam-duration">
          {{ props.row.userStartTime }} ~ {{ props.row.submitTime }}
        </div>
        <el-button type="primary" @click="exportPDF">导出试卷</el-button>
      </div>
    </div>
    <div class="paper-detail">
      <div class="paper-title"> {{ completedPaperInfo?.paperName }}</div>
      <div class="question-list">
        <template v-for="(item, index) in examQuestionList">
          <div class="box-item" :ref="`questionBox${index}`">
            <div class="questionType">
              {{ index + 1 }}.{{
                proxy.selectDictLabel(exam_question_type, item.questionType)
              }}
            </div>
            <div class="questionName">
              {{ item?.questionName }}
            </div>
            <div class="choice-box">
              <div class="choice-tap-item">
                <template v-for="choice in 11">
                  <div class="choice-item" v-if="item[`item${choice}`]">
                    <div class="index">
                      <img
                        v-if="isCorrect(item, String.fromCharCode(64 + choice))"
                        src="@/assets/images/correct.png"
                        alt=""
                      />
                      <img
                        v-else-if="
                          isWrong(item, String.fromCharCode(64 + choice))
                        "
                        src="@/assets/images/error.png"
                        alt=""
                      />
                      <span class="letterOnly" v-else>{{
                        String.fromCharCode(64 + choice)
                      }}</span>
                    </div>
                    <div class="content">
                      {{ item[`item${choice}`] }}
                    </div>
                  </div>
                </template>
              </div>
            </div>
            <div class="analysis-box">
              <div class="answer">
                <i>&nbsp;</i>
                <span>答案：{{ item.answer }}</span>
              </div>
              <div class="myAnswer" v-if="item.answer !== item.userAnswer">
                <i>&nbsp;</i>
                <span v-if="item.userAnswer"
                  >我的答案：{{
                    Array.isArray(item.userAnswer)
                      ? item.userAnswer?.sort().join()
                      : item.userAnswer
                  }}</span
                >
                <span v-else>我的答案：未选择</span>
              </div>
              <div class="score">
                <i>&nbsp;</i>
                <span>得分：{{ item.userQuestionScore || 0 }}</span>
              </div>
              <div class="edit-score" data-html2canvas-ignore="true">
                <el-input-number
                  v-model="item.editNumber"
                  :min="0"
                  :max="999"
                  :precision="1"
                  :step="0.5"
                />
                <el-button
                  style="margin-left: 5px"
                  type="primary"
                  @click="editScore(item)"
                  >修改</el-button
                >
              </div>
            </div>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script setup name="examPaperDetails">
  import html2canvas from "html2canvas"
  import jsPDF from "jspdf"
  import { formatSeconds, calculateTimeDifference } from "@/utils/common.js"
  import {
    completedPaperDetail,
    editQuestionScore,
    editPaperScore
  } from "@/api/onlineExam/exam.js"

  const { proxy } = getCurrentInstance()
  const { exam_question_type } = proxy.useDict("exam_question_type")
  const completedPaperInfo = ref()
  const paperTacticsList = ref()
  const examQuestionList = ref([])
  const emit = defineEmits(["updateCurrentView"])
  const props = defineProps({
    row: {
      type: Object,
      default: () => ({})
    }
  })

  const loadData = async () => {
    completedPaperInfo.value = {}
    paperTacticsList.value = []
    examQuestionList.value = []
    let queryData = {
      baseId: props.row.baseId,
      paperAnswerId: props.row.paperAnswerId
    }
    const { data } = await completedPaperDetail(queryData)
    completedPaperInfo.value = data
    paperTacticsList.value = data.paperTactics
    paperTacticsList.value?.forEach(item => {
      item?.examQuestions.forEach(question => {
        examQuestionList.value.push(question)
      })
    })
  }

  // 正确的选项
  function isCorrect(item, choice) {
    if (item.questionType === "S" || item.questionType === "J") {
      return item.answer === choice
    } else if (item.questionType === "M") {
      if (item.answer.includes(",")) {
        return item.answer.split(",").indexOf(choice) > -1
      }
      return item.answer === choice
    }
    return false
  }

  // 错误的选项
  function isWrong(item, choice) {
    if (!item.userAnswer) return false
    if (item.questionType === "S" || item.questionType === "J") {
      return item.userAnswer === choice && item.userAnswer !== item.answer
    } else if (item.questionType === "M") {
      return item.userAnswer.includes(choice) && !item.answer.includes(choice)
    }
    return false
  }

  const handleBack = () => {
    emit("updateCurrentView", "personnelList", props.row)
  }

  const editScore = async item => {
    // 修改单题分数
    const res = await editQuestionScore({
      questionAnswerId: item.questionAnswerId,
      userQuestionScore: item.editNumber
    })

    // 修改整卷分数
    const res2 = await editPaperScore({
      paperAnswerId: props.row.paperAnswerId,
      editPaperScore: item.editNumber - item.userQuestionScore
    })

    if (res.code === 200 && res2.code === 200) {
      proxy.$modal.msgSuccess("操作成功")
      loadData()
    }
  }

  const exportPDF = async () => {
    // 获取要导出的Vue组件
    const exportContent = document.querySelector(".app-container")
    let domHeight = exportContent.offsetHeight
    let maxHeight = 64257
    html2canvas(exportContent, {
      useCORS: true, // 如果截图的内容里有图片,可能会有跨域的情况,加上这个参数,解决文件跨域问题
      scale: maxHeight / domHeight > 1 ? 1 : maxHeight / domHeight
    }).then(canvas => {
      const contentWidth = canvas.width
      const contentHeight = canvas.height
      let pageHeight = (contentWidth / 592.28) * 841.89
      let leftHeight = contentHeight
      //页面偏移
      var position = 0
      //a4纸的尺寸[595.28,841.89]，html页面生成的canvas在pdf中图片的宽高
      var imgWidth = 595.28 // A4 宽度
      var imgHeight = (592.28 / contentWidth) * contentHeight // A4总高度
      var pageData = canvas.toDataURL("image/jpeg", 1.0)
      var pdf = new jsPDF("", "pt", "a4")
      //有两个高度需要区分，一个是html页面的实际高度，和生成pdf的页面高度(841.89)
      //当内容未超过pdf一页显示的范围，无需分页
      if (leftHeight < pageHeight) {
        pdf.addImage(pageData, "JPEG", 10, 0, imgWidth - 20, imgHeight)
      } else {
        while (leftHeight > 0) {
          pdf.addImage(pageData, "JPEG", 10, position, imgWidth - 20, imgHeight)
          leftHeight -= pageHeight
          position -= 841.89
          // 避免添加空白页
          if (leftHeight > 0) {
            pdf.addPage()
          }
        }
      }
      pdf.save(
        `${completedPaperInfo.value?.paperName}-${completedPaperInfo.value.answerUserName}.pdf`
      )
    })
  }

  loadData()
</script>

<style scoped lang="scss">
  .app-container {
    background-color: #f5f6fa;
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: center;
  }
  .header-info,
  .exam-info,
  .paper-detail {
    display: flex;
    background-color: #fff;
    padding: 20px;
    width: 80%;
  }
  .header-info {
    flex-direction: column;
    .backIcon {
      cursor: pointer;
      vertical-align: text-bottom;
      margin-bottom: 20px;
    }
    .personal-info {
      display: flex;
      justify-content: space-between;
      .info-left {
        display: flex;
        .avatar {
          width: 100px;
          height: 100px;
          margin: 10px 30px 10px 50px;

          > img {
            width: 100%;
            height: 100%;
          }
        }
        .info-text {
          margin-left: 30px;
          display: flex;
          justify-content: center;
          flex-direction: column;
          .info-line {
            display: flex;
            margin-bottom: 20px;
          }
        }
      }
      .signature {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-right: 120px;
        width: 400px;
        img {
          width: 100%;
        }
      }
    }
  }

  .exam-info {
    margin: 30px 0;

    .base-info {
      width: 70%;
      padding-left: 50px;

      .el-row {
        line-height: 50px;
      }
      .el-col {
        font-size: 14px;

        .bold-text {
          font-size: 18px;
          font-weight: bold;
        }
      }
    }

    .export-info {
      display: flex;
      flex-direction: column;
      align-items: center;
      .exam-duration {
        font-weight: bold;
        margin: 20px 0 15px 0;
      }
    }
  }

  .paper-detail {
    flex-direction: column;
    .paper-title {
      display: flex;
      justify-content: center;
      align-items: center;
      font-weight: bolder;
      font-size: 20px;
      margin: 25px 0;
    }

    .question-list {
      .box-item {
        display: flex;
        flex-direction: column;
        padding: 30px;
        margin: 40px 0 0 20px;
        background-color: #fff;
        border-radius: 10px;

        .questionType {
          display: flex;
          font-size: 20px;
        }

        .questionName {
          margin-top: 20px;
          font-size: 16px;
        }

        .choice-box {
          padding: 15px 0;
          display: flex;
          .choice-tap-item {
            cursor: pointer;
            line-height: 40px;
            display: flex;
            flex-direction: column;
            .choice-item {
              display: flex;
              align-items: center;

              .choice-selected {
                background-color: #3ca7fa;
                border: 1px solid #3ca7fa;
              }

              .index {
                display: flex;
                justify-content: center;
                align-items: center;
                width: 24px;
                height: 24px;

                > img {
                  width: 95%;
                  height: 95%;
                }

                .letterOnly {
                  display: flex;
                  justify-content: center;
                  align-items: center;
                  border-radius: 50% 50%;
                  width: 22px;
                  height: 22px;
                  border: 1px solid #ccc;
                }
              }

              .content {
                padding-left: 10px;
                font-size: 16px;
              }
            }
          }
        }

        .analysis-box {
          display: flex;
          height: auto;
          background: #f4fafe;
          border-radius: 10px;
          padding: 13px 20px;
          flex-direction: row;
          align-items: center;
          justify-content: flex-start;

          .answer {
            margin-right: 30px;
            > i {
              width: 4px;
              height: 18px;
              border-radius: 2px;
              margin-right: 10px;
              background: #04c877;
            }
          }
          .myAnswer {
            margin-right: 30px;
            > i {
              width: 4px;
              height: 18px;
              border-radius: 2px;
              margin-right: 10px;
              background: #ff4d4f;
            }
          }
          .score {
            > i {
              width: 4px;
              height: 18px;
              border-radius: 2px;
              margin-right: 10px;
              background: #3ca7fa;
            }
          }

          .edit-score {
            margin-left: 30px;
            display: flex;
            justify-content: center;
            align-items: center;
          }
        }
      }
    }
  }
</style>
