<!--
 * @Description: 新增课程-入口文件
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-06-01 10:32:43
 * @LastEditTime: 2024-03-15 13:40:11
-->

<template>
  <section class="index-container">
    <component
      :is="currentView"
      :row="row"
      @updateCurrentView="updateCurrentView"
    ></component>
  </section>
</template>

<script setup name="courseAddIndex">
  import addEditCourse from "./addEditCourse"
  import userServiceAgree from "./userServiceAgree"
  import useUserStore from "@/store/modules/user"

  const { user } = storeToRefs(useUserStore())
  const row = ref({})
  const route = useRoute()
  const currentView = shallowRef(addEditCourse)

  if (route.query.flag === "add" && user.value.userId !== 1) {
    row.value.agreeFlag = "course"
    currentView.value = userServiceAgree
  }

  const updateCurrentView = (view, item) => {
    row.value = item
    if (view === "userServiceAgree") {
      currentView.value = userServiceAgree
    } else {
      currentView.value = addEditCourse
    }
  }
</script>
