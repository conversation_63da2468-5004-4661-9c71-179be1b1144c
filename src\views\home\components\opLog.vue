<!--
 * @Description: 首页-操作记录
 * @Author: <PERSON><PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-04-11 14:50:40
 * @LastEditTime: 2023-06-28 11:29:25
-->
<template>
  <el-card :bordered="false">
    <template #header>
      <div class="header">
        <span class="title">操作记录</span>
        <a class="showMore" @click="leaveFor('/system/log/operlog')">更多</a>
      </div>
    </template>
    <div class="timeline-div" v-if="opLogList.length > 0">
      <el-timeline>
        <el-timeline-item
          :key="opLog.id"
          v-for="opLog in opLogList"
          color="blue"
          >{{ opLog.operTime }} &nbsp;&nbsp;&nbsp;{{ opLog.title }}
        </el-timeline-item>
      </el-timeline>
    </div>
    <el-empty v-else />
  </el-card>
</template>

<script setup name="indexOpLog">
  import router from "@/router"
  import { onMounted } from "vue"
  import useUserStore from "@/store/modules/user"
  import { list } from "@/api/system/operlog"

  const userStore = useUserStore()
  const opLogList = ref([])
  const loading = ref(true)

  /** 查询登录日志 */
  function seleOpLogList() {
    loading.value = true
    list().then(response => {
      opLogList.value = response.rows
      loading.value = false
    })
  }
  onMounted(() => {
    // 进来后执行查询
    seleOpLogList()
  })

  const leaveFor = (url = "/") => {
    router.replace({
      path: url
    })
  }
</script>
<style scoped lang="scss">
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .title {
      padding: 8px;
      font-size: 19px;
      font-weight: bolder;
    }

    .showMore {
      color: #1890ff;
      font-size: 15px;
      margin-right: 10px;
    }
  }
  .timeline-div {
    height: 300px;
    /* overflow: auto; */
    overflow-y: scroll;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE 10+ */
  }
  .timeline-div::-webkit-scrollbar {
    display: none;
  }
</style>
