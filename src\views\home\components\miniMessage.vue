<!--
 * @Description: 首页-消息列表
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-04-11 09:59:45
 * @LastEditTime: 2023-07-24 17:25:44
-->
<template>
  <el-card :bordered="false" :bodyStyle="miniMessageBodyStyle">
    <template #header>
      <div class="header">
        <span class="title">站内信</span>
      </div>
    </template>

    <div class="index-message-list">
      <el-table v-loading="loading" :data="messageList">
        <el-table-column label="公告标题" prop="noticeTitle" />
        <el-table-column label="公告类型" prop="noticeType" width="100">
          <template #default="scope">
            <dict-tag
              :options="sys_notice_type"
              :value="scope.row.noticeType"
            />
          </template>
        </el-table-column>
        <el-table-column label="状态" prop="status" width="100">
          <template #default="scope">
            <dict-tag :options="sys_notice_status" :value="scope.row.status" />
          </template>
        </el-table-column>
        <el-table-column label="公告内容" prop="noticeContent" />
        <template #empty>
          <el-empty />
        </template>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </div>
  </el-card>
</template>

<script setup name="miniMessage">
  import { listNotice } from "@/api/system/notice"

  const { proxy } = getCurrentInstance()
  const { sys_notice_status, sys_notice_type } = proxy.useDict(
    "sys_notice_status",
    "sys_notice_type"
  )

  const loading = ref(false)
  const messageList = ref([])
  const miniMessageBodyStyle = ref({
    "padding-top": "10px",
    height: "630px"
  })
  const queryParams = ref({
    pageSize: 10,
    pageNum: 1
  })
  const total = ref(0)

  // 点击详情
  const messageDetail = message => {
    visible.value = true
    const param = {
      id: message.id
    }
  }

  /** 查询公告列表 */
  function getList() {
    loading.value = true
    listNotice(queryParams.value).then(response => {
      messageList.value = response.rows
      total.value = response.total
      loading.value = false
    })
  }

  getList()
</script>

<style scoped lang="scss">
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .title {
      padding: 8px;
      font-size: 19px;
      font-weight: bolder;
    }
  }
</style>
