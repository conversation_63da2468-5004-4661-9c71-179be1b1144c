<!--
 * @Description: 添加题目弹窗
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-03-06 16:02:06
 * @LastEditTime: 2025-06-19 14:24:34
-->
<template>
  <div class="app-container">
    <el-dialog
      v-model="dialogVisible"
      title="添加题目"
      width="50%"
      top="110px"
      @close="close"
      :key="new Date().getTime()"
      center
    >
      <el-form
        :model="queryParams"
        ref="queryRef"
        :inline="true"
        v-show="showSearch"
      >
        <el-form-item label="题干" prop="questionName">
          <el-input
            v-model="queryParams.questionName"
            placeholder="题干"
            clearable
            style="width: 240px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="Refresh" @click="resetQ<PERSON>">重置</el-button>
        </el-form-item>
      </el-form>

      <el-table
        ref="questionAddTableRef"
        v-loading="loading"
        :data="tableData"
        row-key="questionId"
        class="table"
      >
        <el-table-column type="selection" width="55" reserve-selection />
        <el-table-column label="试题类型" width="100" prop="questionType">
          <template #default="scope">
            <dict-tag
              :options="exam_question_type"
              :value="scope.row.questionType"
            />
          </template>
        </el-table-column>
        <el-table-column label="所属目录" width="150" prop="catalogueName">
        </el-table-column>
        <el-table-column label="题干" prop="questionName"> </el-table-column>
        <el-table-column label="难度" width="60" prop="degreeType">
          <template #default="scope">
            <dict-tag
              :options="exam_degree_type"
              :value="scope.row.degreeType"
            />
          </template>
        </el-table-column>
      </el-table>

      <pagination
        class="dialog-pagination"
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
        :autoScroll="false"
      />
      <template #footer>
        <el-button @click="close">取消</el-button>
        <el-button type="primary" @click="save">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="addQuestionDialog">
  import { listQuestion } from "@/api/onlineExam/question"

  const emit = defineEmits(["fetch-data"])
  const { proxy } = getCurrentInstance()
  const { exam_question_type, exam_degree_type } = proxy.useDict(
    "exam_question_type",
    "exam_degree_type"
  )

  const tableData = ref([])
  const loading = ref(true)
  const showSearch = ref(true)
  const dialogVisible = ref(false)
  const total = ref(0)
  const currentIndex = ref(0)
  const associateCourseIds = ref('')

  const data = reactive({
    queryParams: {
      pageNum: 1,
      pageSize: 10
    },
    selectedRows: []
  })

  const { queryParams, selectedRows } = toRefs(data)

  //** 弹框打开事件 */
  const openDialog = async (item, index, courseIds = '') => {
    dialogVisible.value = true
    queryParams.value.questionType = item.questionType
    currentIndex.value = index
    associateCourseIds.value = courseIds
    
    // 重置查询参数
    queryParams.value.pageNum = 1
    queryParams.value.questionId = ''
    queryParams.value.questionName = ''
    selectedRows.value = []
    
    item.examQuestions.forEach(question => {
      selectedRows.value.push(question.questionId)
    })
    await getList()
    if (selectedRows.value) {
      nextTick(() => {
        tableData.value.forEach((item, index) => {
          if (selectedRows.value.find(v => v == item.questionId)) {
            proxy.$refs["questionAddTableRef"].toggleRowSelection(
              proxy.$refs["questionAddTableRef"].data[index],
              true
            )
          }
        })
      })
    }
  }
  /** 查询目录列表 */
  const getList = async () => {
    loading.value = true
    const requestParams = {
      ...queryParams.value
    }
    
    // 如果有关联课程ID，添加到查询参数中
    if (associateCourseIds.value) {
      requestParams.associateCourseIds = associateCourseIds.value
    }
    
    const response = await listQuestion(requestParams)
    if (response.code === 200) {
      tableData.value = response.rows || []
      total.value = response.total
      loading.value = false
    }
  }
  /** 搜索按钮操作 */
  function handleQuery() {
    queryParams.value.pageNum = 1
    getList()
  }
  /** 重置按钮操作 */
  function resetQuery() {
    proxy.resetForm("queryRef")
    queryParams.value.pageNum = 1
    handleQuery()
  }
  // 关闭弹框并重置操作
  const close = () => {
    proxy.resetForm("queryRef")
    selectedRows.value = []
    associateCourseIds.value = ''
    dialogVisible.value = false
  }
  const save = () => {
    const selectionRows = proxy.$refs["questionAddTableRef"].getSelectionRows()
    emit("fetch-data", currentIndex.value, selectionRows)
    proxy.$modal.msgSuccess("操作成功")
    close()
  }

  defineExpose({
    openDialog
  })
</script>

<style lang="scss" scoped>
  :deep(.pagination-container .el-pagination) {
    position: static;
  }
  :deep(.cell) {
    display: flex;
    justify-content: center;
    width: auto !important;
  }
</style>
