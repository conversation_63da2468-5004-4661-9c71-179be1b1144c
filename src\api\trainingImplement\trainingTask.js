/*
 * @Description: 培训任务相关API
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-05-10 16:46:27
 * @LastEditTime: 2024-03-28 15:44:56
 */

import request from "@/utils/request"

// 查询培训任务列表
export function listTrainingTask(query) {
  return request({
    url: "/course/task/list",
    method: "get",
    params: query
  })
}

// 查询培训任务详细
export function getTrainingTask(taskId) {
  return request({
    url: "/course/task/" + taskId,
    method: "get"
  })
}

// 新增培训任务
export function addTrainingTask(data) {
  return request({
    url: "/course/task",
    method: "post",
    data: data
  })
}

// 修改培训任务
export function updateTrainingTask(data) {
  return request({
    url: "/course/task",
    method: "put",
    data: data
  })
}

// 删除培训任务
export function delTrainingTask(taskIds) {
  return request({
    url: "/course/task/" + taskIds,
    method: "delete"
  })
}

// 查看全部所选人员列表
export function getAllSelectedUserList(params) {
  return request({
    url: "/course/task/userList",
    method: "get",
    params
  })
}

// ==================================== 培训任务跟踪 ==========================================
// 查询培训任务跟踪列表
export function listTrainingTrack(query) {
  return request({
    url: "/course/record/list",
    method: "get",
    params: query
  })
}

// 查询培训任务跟踪详细
export function getTrainingTrack(studyRecordId) {
  return request({
    url: "/course/record/" + studyRecordId,
    method: "get"
  })
}

// 新增培训任务跟踪
export function addTrainingTrack(data) {
  return request({
    url: "/course/record",
    method: "post",
    data: data
  })
}

// 修改培训任务跟踪
export function updateTrainingTrack(data) {
  return request({
    url: "/course/record",
    method: "put",
    data: data
  })
}

// 删除培训任务跟踪
export function delTrainingTrack(studyRecordIds) {
  return request({
    url: "/course/record/" + studyRecordIds,
    method: "delete"
  })
}

// 培训任务跟踪人员列表
export function trainingTrackUserList(query) {
  return request({
    url: "/course/record/userList",
    method: "get",
    params: query
  })
}

// 培训任务跟踪人员详情
export function trainingTrackUserDetail(query) {
  return request({
    url: "/course/record/info",
    method: "get",
    params: query
  })
}

// 培训任务跟踪课程列表
export function trainingTrackCourseList(query) {
  return request({
    url: "/course/record/courseList",
    method: "get",
    params: query
  })
}

// 培训任务跟踪考试列表
export function trainingTrackExamList(query) {
  return request({
    url: "/course/record/examList",
    method: "get",
    params: query
  })
}

// 培训任务跟踪调查问卷列表
export function trainingTrackQuestionnaireList(query) {
  return request({
    url: "/course/record/questionnaireList",
    method: "get",
    params: query
  })
}
