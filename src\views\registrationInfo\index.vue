<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true">
      <el-form-item label="用户姓名" prop="userName">
        <el-input
          v-model="queryParams.userName"
          placeholder="请输入用户姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="证件号" prop="idNumber">
        <el-input
          v-model="queryParams.idNumber"
          placeholder="请输入证件号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="getList"
          >搜索</el-button
        >
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <!-- <el-row :gutter="10" class="mb-8px">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="openForm('create')">
          新增
        </el-button>
      </el-col>
    </el-row> -->
    <el-table
      v-loading="loading"
      :data="tableData"
      :stripe="true"
      :show-overflow-tooltip="true"
    >
      <el-table-column label="用户姓名" align="center" prop="userName" />
      <el-table-column label="手机号" align="center" prop="phone" />
      <el-table-column label="证件类型" align="center" prop="idType" />
      <el-table-column label="证件号" align="center" prop="idNumber" />
      <el-table-column label="工作单位" align="center" prop="workUnit" />
      <el-table-column label="文化程度" align="center" prop="eduLevel">
        <template #default="scope">
          <dict-tag
            :options="educational_background"
            :value="scope.row.eduLevel"
          />
        </template>
      </el-table-column>
      <el-table-column label="状态 " align="center" prop="status">
        <template #default="scope">
          {{
            scope.row.status === "0"
              ? "待审核"
              : scope.row.status === "1"
              ? "已通过"
              : "已驳回"
          }}
        </template>
      </el-table-column>
      <!-- <el-table-column label="备注" align="center" prop="remarks" /> -->
      <el-table-column
        label="操作"
        width="250"
        class-name="small-padding fixed-width"
        fixed="right"
      >
        <template #default="scope">
          <el-button
            v-if="scope.row.status == 0"
            link
            type="danger"
            icon="Edit"
            @click="openForm('update', scope.row.id)"
            >审核</el-button
          >
          <el-button
            link
            type="primary"
            icon="View"
            @click="openForm('detail', scope.row.id)"
            >查看</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 弹窗, 新增 / 修改 -->
    <addEditDialog
      ref="addEditDialogRef"
      @refreshDataList="getList"
    ></addEditDialog>
  </div>
</template>

<script setup name="videoList">
  import addEditDialog from "./addEditDialog.vue"
  import { listRegistration } from "@/api/registrationInfo/index"

  const { proxy } = getCurrentInstance()
  const { educational_background } = proxy.useDict("educational_background")
  const tableData = ref([])
  const loading = ref(false)
  const total = ref(0)
  const addEditDialogRef = ref()

  const data = reactive({
    queryParams: {
      pageNum: 1,
      pageSize: 10,
      userName: undefined,
      idNumber: undefined
    }
  })

  const { queryParams } = toRefs(data)

  const openForm = (type, id) => {
    addEditDialogRef.value.openDialog(type, id)
  }

  /** 查询目录列表 */
  function getList() {
    loading.value = true
    listRegistration(queryParams.value).then(response => {
      tableData.value = response.rows || []
      total.value = response.total
      loading.value = false
    })
  }

  function resetQuery() {
    proxy.resetForm("queryRef")
    getList()
  }

  getList()
</script>
<style lang="scss" scoped></style>
