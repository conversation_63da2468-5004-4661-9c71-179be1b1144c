<!--
 * @Description: 课程选择弹窗
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-06-09 13:33:15
 * @LastEditTime: 2024-04-15 10:05:32
-->
<template>
  <div class="app-container">
    <el-dialog
      v-model="dialogVisible"
      title="选择课程"
      width="70%"
      top="110px"
      @close="close"
      :key="new Date().getTime()"
      center
    >
      <el-form
        :model="queryParams"
        ref="queryRef"
        :inline="true"
        v-show="showSearch"
      >
        <el-form-item label="课程名称" prop="courseName">
          <el-input
            v-model="queryParams.courseName"
            placeholder="请输入课程名称"
            clearable
            style="width: 240px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="课程目录" prop="catalogueId">
          <el-tree-select
            v-model="queryParams.catalogueId"
            :data="catalogueOptions"
            :props="{
              value: 'catalogueId',
              label: 'catalogueName',
              children: 'children',
              disabled: 'disabled'
            }"
            clearable
            value-key="catalogueId"
            placeholder="选择课程目录"
            check-strictly
            default-expand-all
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 新增：已选择课程信息显示 -->
      <div v-if="props.selectionType === 'multiple'" class="selected-info">
        <span class="selected-count">
          已选择 {{ selectedCourses.length }} 门课程
        </span>
        <el-button 
          v-if="selectedCourses.length > 0" 
          type="text" 
          size="small" 
          @click="clearAllSelection"
        >
          清空选择
        </el-button>
        <el-button 
          v-if="selectedCourses.length > 0" 
          type="text" 
          size="small" 
          @click="showSelectedCourses"
        >
          查看已选择
        </el-button>
      </div>

      <el-table
        ref="chooseCourseTableRef"
        :data="tableData"
        highlight-current-row
        @current-change="handleCurrentChange"
        @selection-change="handleSelectionChange"
        row-key="courseId"
      >
        <el-table-column
          :selectable="selectable"
          v-if="props.selectionType === 'multiple'"
          type="selection"
          width="55"
          reserve-selection
        />
        <el-table-column label="课程封面" prop="courseImage" width="100">
          <template #default="scope">
            <el-popover placement="right" :width="400" trigger="hover">
              <img :src="scope.row.courseImage" width="375" height="375" />
              <template #reference>
                <img
                  :src="scope.row.courseImage"
                  style="max-height: 60px; max-width: 60px"
                />
              </template>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column label="课程名称" prop="courseName" />
        <el-table-column label="所属目录" prop="catalogueName" width="135" />
        <el-table-column label="课程编号" prop="courseCode" width="100" />
        <el-table-column label="课程类型" prop="courseTypeName" width="150" />
      </el-table>

      <pagination
        class="dialog-pagination"
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
        :autoScroll="false"
      />
      <template #footer>
        <el-button @click="close">取消</el-button>
        <el-button type="primary" @click="save">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="ChooseCourseDialog">
  import { listCourse } from "@/api/onlineCourse/course"
  import { catalogueList } from "@/api/system/catalogue.js"
  import catalogue from "@/utils/catalogue.js"

  const emit = defineEmits(["fetch-data"])
  const { proxy } = getCurrentInstance()
  const props = defineProps({
    selectionType: {
      type: String,
      default: "single"
    },
    disabledList: {
      type: Array,
      default: () => []
    }
  })

  const tableData = ref([])
  const showSearch = ref(true)
  const dialogVisible = ref(false)
  const total = ref(0)
  const catalogueOptions = ref([])
  const selectedCourses = ref([])
  const { pedu_course_type, optional_settings } = proxy.useDict(
    "pedu_course_type",
    "optional_settings"
  )

  const data = reactive({
    queryParams: {
      pageNum: 1,
      pageSize: 10,
      courseStatus: "2"
    }
  })

  const { queryParams } = toRefs(data)

  //** 弹框打开事件 */
  const openDialog = async id => {
    reset()
    if (id) {
      if (props.selectionType === "single") {
        currentRow.value = id
      } else {
        if (Array.isArray(id)) {
          selectedCourses.value = [...id]
        } else {
          selectedCourses.value = id.split(",").map(item => ({ courseId: item }))
        }
      }
    }
    dialogVisible.value = true
    await getList()
    await getTreeselect()
  }

  const reset = () => {
    currentRow.value = ""
    selectedCourses.value = []
    queryParams.value = {
      pageNum: 1,
      pageSize: 10,
      courseStatus: "2"
    }
  }

  const isCourseSelected = (courseId) => {
    return selectedCourses.value.some(course => course.courseId === courseId)
  }

  const handleSelectionChange = (selection) => {
    if (props.selectionType !== "multiple") return
    
    const currentPageCourseIds = tableData.value.map(item => item.courseId)
    
    selectedCourses.value = selectedCourses.value.filter(course => 
      !currentPageCourseIds.includes(course.courseId)
    )
    
    selectedCourses.value.push(...selection)
  }

  const tableSelectChange = () => {
    if (props.selectionType === "multiple") {
      nextTick(() => {
        tableData.value.forEach((item, index) => {
          if (isCourseSelected(item.courseId)) {
            proxy.$refs["chooseCourseTableRef"].toggleRowSelection(
              proxy.$refs["chooseCourseTableRef"].data[index],
              true
            )
          }
        })
      })
    }
  }

  /** 查询目录列表 */
  const getList = async () => {
    let requestData = {
      ...queryParams.value,
      fromType: "training",
      sortField: "a.update_time",
      sortOrder: "desc"
    }
    const res = await listCourse(requestData)
    if (res.code === 200) {
      tableData.value = res.rows
      total.value = res.total
      tableSelectChange()
    }
  }
  /** 查询目录下拉树结构 */
  function getTreeselect() {
    catalogueOptions.value = []
    catalogueList({ catalogueType: catalogue.COURSE_CATALOGUE }).then(
      response => {
        const courseCatalogue = {
          catalogueId: 0,
          catalogueName: "主类目",
          children: [],
          disabled: true
        }
        courseCatalogue.children = proxy.handleTree(
          response.rows,
          "catalogueId"
        )
        catalogueOptions.value.push(courseCatalogue)
      }
    )
  }

  /** 搜索按钮操作 */
  function handleQuery() {
    getList()
  }
  /** 重置按钮操作 */
  function resetQuery() {
    reset()
    handleQuery()
  }
  // 关闭弹框并重置操作
  const close = () => {
    reset()
    dialogVisible.value = false
  }
  const save = () => {
    if (props.selectionType === "multiple") {
      emit("fetch-data", selectedCourses.value)
    } else {
      emit("fetch-data", currentRow.value)
    }
    proxy.$modal.msgSuccess("操作成功")
    close()
  }

  const currentRow = ref()
  const handleCurrentChange = val => {
    currentRow.value = val
  }

  const selectable = row => {
    if (!props.disabledList || props.disabledList.length === 0) return true
    if (props.disabledList.includes(row.courseId)) {
      return false // 禁用
    } else {
      return true // 可选
    }
  }

  // 新增：清空所有选择
  const clearAllSelection = () => {
    selectedCourses.value = []
    // 清空当前页面的表格选择
    if (proxy.$refs["chooseCourseTableRef"]) {
      proxy.$refs["chooseCourseTableRef"].clearSelection()
    }
  }

  // 新增：显示已选择的课程
  const showSelectedCourses = () => {
    const courseNames = selectedCourses.value.map(course => course.courseName).join('、')
    proxy.$alert(courseNames, '已选择的课程', {
      confirmButtonText: '确定',
      type: 'info'
    })
  }

  defineExpose({
    openDialog
  })
</script>

<style lang="scss" scoped>
  :deep(.el-table .cell) {
    white-space: pre-line;
  }
  :deep(.pagination-container .el-pagination) {
    position: static;
  }
  
  .selected-info {
    margin-bottom: 15px;
    padding: 10px;
    background-color: #f5f7fa;
    border-radius: 4px;
    display: flex;
    align-items: center;
    gap: 10px;
    
    .selected-count {
      color: #409eff;
      font-weight: 500;
    }
  }
</style>
