/*
 * @Description: 登录相关API
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-02-16 14:11:33
 * @LastEditTime: 2024-09-24 16:35:36
 */
import request from "@/utils/request"

// 登录方法
export function loginRequest(username, password, code, uuid, domainName) {
  return request({
    url: "/auth/login",
    headers: {
      isToken: false
    },
    method: "post",
    data: { username, password, code, uuid, domainName, fromType: 0 }
  })
}

// 注册方法
export function register(data) {
  return request({
    url: "/auth/register",
    headers: {
      isToken: false
    },
    method: "post",
    data: data
  })
}

// 刷新方法
export function refreshToken() {
  return request({
    url: "/auth/refresh",
    method: "post"
  })
}

// 获取用户详细信息
export async function getInfo() {
  return await request({
    url: "/system/user/getInfo",
    method: "get"
  })
}

// 退出方法
export async function logout() {
  return await request({
    url: "/auth/logout",
    method: "delete"
  })
}

// 获取验证码
export function getCodeImg() {
  return request({
    url: "/code",
    headers: {
      isToken: false
    },
    method: "get",
    timeout: 20000
  })
}

// 获取key
export function getPublicKey() {
  return request({
    url: "/auth/publicKey",
    method: "get"
  })
}

// 发送验证码
export function sendCode(data) {
  return request({
    url: "/auth/send-sms-code",
    method: "post",
    data: data
  })
}
// 登录
export function handleUnepLogin(data) {
  return request({
    url: "/auth/sms-login",
    headers: {
      isToken: false
    },
    method: "post",
    data: data
  })
}

// 双控跳转自动登录
export async function sysLogin(data) {
  return await request({
    url: "/auth/sysLogin",
    method: "post",
    data: data
  })
}
