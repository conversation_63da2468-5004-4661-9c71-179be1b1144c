/*
 * @Description: 在线考试-考试管理相关api
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-03-06 16:17:49
 * @LastEditTime: 2023-10-31 11:13:08
 */
import request from "@/utils/request"

// 查询考试列表
export function listExam(query) {
  return request({
    url: "/exam/base/list",
    method: "get",
    params: query
  })
}

// 查询考试详细
export function getExam(examId) {
  return request({
    url: "/exam/base/" + examId,
    method: "get"
  })
}

// 新增考试
export function addExam(data) {
  return request({
    url: "/exam/base",
    method: "post",
    data: data
  })
}

// 修改考试
export function updateExam(data) {
  return request({
    url: "/exam/base",
    method: "put",
    data: data
  })
}

// 删除考试
export function delExam(examId) {
  return request({
    url: "/exam/base/" + examId,
    method: "delete"
  })
}

// 查询考试安排列表
export function listArrange(query) {
  return request({
    url: "/exam/arrange/list",
    method: "get",
    params: query
  })
}

// 查询考试安排详细
export function getArrange(arrangeId) {
  return request({
    url: "/exam/arrange/" + arrangeId,
    method: "get"
  })
}

// 新增考试安排
export function addArrange(data) {
  return request({
    url: "/exam/arrange",
    method: "post",
    data: data
  })
}

// 修改考试安排
export function updateArrange(data) {
  return request({
    url: "/exam/arrange",
    method: "put",
    data: data
  })
}

// 删除考试安排
export function delArrange(arrangeId) {
  return request({
    url: "/exam/arrange/" + arrangeId,
    method: "delete"
  })
}

// 考场监控-人员列表
export function userListByArrange(query) {
  return request({
    url: "/exam/arrange/userListByArrange",
    method: "get",
    params: query
  })
}

// 考试报表统计列表
export function examReportList(query) {
  return request({
    url: "/exam/arrange/statistics/list",
    method: "get",
    params: query
  })
}

// 学员考试明细列表
export function getExamTakers(query) {
  return request({
    url: "/exam/arrange/examTakers",
    method: "get",
    params: query
  })
}

// 查询已考完的试卷详情
export function completedPaperDetail(data) {
  return request({
    url: "/exam/paper-answer/completedPaperDetail",
    method: "put",
    data: data
  })
}

// 修改单题分数
export function editQuestionScore(data) {
  return request({
    url: "/exam/question-answer",
    method: "put",
    data
  })
}

// 修改整卷分数
export function editPaperScore(data) {
  return request({
    url: "exam/paper-answer",
    method: "put",
    data
  })
}
