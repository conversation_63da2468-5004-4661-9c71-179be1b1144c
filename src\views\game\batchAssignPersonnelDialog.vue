<!--
 * @Description: 批量设置人员弹窗
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2025-07-08 09:33:10
 * @LastEditTime: 2025-07-08 10:12:41
-->
<template>
  <el-dialog
    v-model="visible"
    title="批量设置人员"
    width="60%"
    :close-on-click-modal="false"
    center
  >
    <div class="batch-assign-container">
      <!-- 选中游戏展示 -->
      <div class="selected-games-section">
        <h4 class="section-title">已选择游戏 ({{ selectedGames.length }}个)</h4>
        <div class="games-list">
          <el-tag
            v-for="game in selectedGames"
            :key="game.id"
            type="info"
            class="game-tag"
          >
            {{ game.gameName }}
          </el-tag>
        </div>
      </div>

      <!-- 人员分配区域 -->
      <div class="personnel-section">
        <h4 class="section-title">人员分配</h4>
        <div class="personnel-content">
          <div class="mt-2 flex flex-wrap gap-2">
            <div
              v-for="(person, index) in displayPersonnelList"
              :key="person.userId"
              class="bg-blue-50 h-9 rounded-3xl flex items-center pr-2 relative border border-blue-200"
            >
              <el-avatar
                class="!m-1"
                :size="28"
                :style="{ backgroundColor: '#409eff' }"
              >
                {{ person.userName.substring(0, 1) }}
              </el-avatar>
              <span class="mx-2 text-sm">{{ person.userName }}</span>
              <el-icon
                class="ml-2 cursor-pointer hover:text-red-500"
                @click="handleRemovePerson(index)"
              >
                <Close />
              </el-icon>
            </div>
            
            <!-- 展开/收起按钮 -->
            <div
              v-if="selectedPersonnelList.length > 20"
              class="bg-gray-100 h-9 rounded-3xl flex items-center px-3 border border-gray-200 cursor-pointer hover:bg-gray-200"
              @click="toggleExpanded"
            >
              <span class="text-sm text-gray-600">
                {{ isExpanded ? '收起' : `还有${selectedPersonnelList.length - 20}人...` }}
              </span>
              <el-icon class="ml-1 text-gray-600">
                <component :is="isExpanded ? 'ArrowUp' : 'ArrowDown'" />
              </el-icon>
            </div>
            
            <el-button type="primary" link @click="choosePersonnel">
              <el-icon><Plus /></el-icon> 选择人员
            </el-button>
          </div>
        </div>
      </div>

      <!-- 操作说明 -->
      <div class="operation-tips">
        <el-alert
          title="操作说明"
          type="info"
          :closable="false"
          show-icon
        >
          <template #default>
            <p>• 将为所选择的 <strong>{{ selectedGames.length }}</strong> 个游戏批量设置相同的人员分配</p>
            <p>• 此操作将覆盖游戏原有的人员分配设置</p>
          </template>
        </el-alert>
      </div>
    </div>

    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button 
        type="primary" 
        @click="submitHandle"
        :loading="submitting"
        :disabled="selectedPersonnelList.length === 0"
      >
        确定设置
      </el-button>
    </template>
  </el-dialog>

  <!-- 人员选择弹窗 -->
  <ChoosePersonnelOrDeptDialog
    ref="choosePersonnelOrDeptDialogRef"
    @fetch-data="choosePersonnelDone"
  />
</template>

<script setup>
  import { Close, Plus, ArrowUp, ArrowDown } from "@element-plus/icons-vue"
  import { batchAssignPersonnelToGames } from "@/api/game"

  const emit = defineEmits(["refreshDataList"])
  const { proxy } = getCurrentInstance()

  const visible = ref(false)
  const submitting = ref(false)
  const selectedGames = ref([])
  const choosePersonnelOrDeptDialogRef = ref()

  // 人员分配相关
  const selectedPersonnelList = ref([])
  const isExpanded = ref(false)

  // 显示的人员列表（支持展开/收起）
  const displayPersonnelList = computed(() => {
    if (selectedPersonnelList.value.length <= 20 || isExpanded.value) {
      return selectedPersonnelList.value
    }
    return selectedPersonnelList.value.slice(0, 20)
  })

  // 切换展开/收起状态
  const toggleExpanded = () => {
    isExpanded.value = !isExpanded.value
  }

  // 选择人员
  const choosePersonnel = () => {
    const currentIds = selectedPersonnelList.value
      .map(person => person.userId)
      .join(",")
    const currentNames = selectedPersonnelList.value
      .map(person => person.userName)
      .join(",")
    choosePersonnelOrDeptDialogRef.value.openDialog(
      currentIds,
      currentNames,
      null,
      "personnel"
    )
  }

  // 人员选择完成
  const choosePersonnelDone = (userList, fieldType) => {
    if (fieldType === "personnel") {
      selectedPersonnelList.value = userList.map(user => ({
        userId: user.userId,
        userName: user.userName
      }))
    }
  }

  // 删除选中的人员
  const handleRemovePerson = (index) => {
    // 计算实际索引（考虑展开/收起状态）
    const actualIndex = isExpanded.value || selectedPersonnelList.value.length <= 20 
      ? index 
      : index < 20 ? index : -1
    
    if (actualIndex >= 0 && actualIndex < selectedPersonnelList.value.length) {
      selectedPersonnelList.value.splice(actualIndex, 1)
    }
  }

  // 打开弹窗
  const openDialog = (games) => {
    selectedGames.value = games
    selectedPersonnelList.value = []
    isExpanded.value = false
    visible.value = true
  }

  // 提交批量设置
  const submitHandle = async () => {
    if (selectedPersonnelList.value.length === 0) {
      proxy.$modal.msgWarning("请先选择人员")
      return
    }

    submitting.value = true
    try {
      // 构造批量更新数据
      const linkUserIds = selectedPersonnelList.value.map(user => user.userId)
      const linkUserNames = selectedPersonnelList.value.map(user => user.userName)
      
      const batchData = {
        gameIds: selectedGames.value.map(game => game.id),
        userIds: linkUserIds,
        userNames: linkUserNames
      }
      await batchAssignPersonnelToGames(batchData)
      
      // 模拟接口调用
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      proxy.$modal.msgSuccess(`成功为 ${selectedGames.value.length} 个游戏设置人员`)
      visible.value = false
      emit("refreshDataList")
    } catch (error) {
      proxy.$modal.msgError("批量设置失败")
    } finally {
      submitting.value = false
    }
  }

  defineExpose({
    openDialog
  })
</script>

<style lang="scss" scoped>
  .batch-assign-container {
    padding: 20px 0;
  }

  .section-title {
    margin: 0 0 16px 0;
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    padding-bottom: 8px;
    border-bottom: 2px solid #e4e7ed;
  }

  .selected-games-section {
    margin-bottom: 24px;
  }

  .games-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }

  .game-tag {
    margin: 0;
    padding: 8px 12px;
    border-radius: 6px;
    font-weight: 500;
  }

  .personnel-section {
    margin-bottom: 24px;
  }

  .personnel-content {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 16px;
    border: 1px solid #e9ecef;
  }

  .operation-tips {
    margin-bottom: 16px;

    :deep(.el-alert) {
      border-radius: 8px;
    }

    :deep(.el-alert__content) {
      p {
        margin: 4px 0;
      }
    }
  }
</style> 