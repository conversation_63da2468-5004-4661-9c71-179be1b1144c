<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true">
      <el-form-item label="终端来源" prop="terminalSource">
        <el-select
          v-model="queryParams.terminalSource"
          placeholder="请选择终端来源"
          clearable
          class="!w-230px"
        >
          <el-option
            v-for="dict in terminal_source"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="时间" prop="captureTime">
        <el-date-picker
          v-model="dateRange"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="selectDate"
        ></el-date-picker>
      </el-form-item>

      <el-form-item label="部门" prop="deptId">
        <el-tree-select
          v-model="queryParams.deptId"
          :data="deptOptions"
          :props="{ value: 'id', label: 'label', children: 'children' }"
          value-key="id"
          placeholder="请选择部门"
          check-strictly
          clearable
        />
      </el-form-item>
      <el-form-item label="姓名" prop="userName">
        <el-input
          v-model="queryParams.userName"
          placeholder="请输入姓名"
          clearable
          class="!w-230px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="培训任务" prop="subTaskName">
        <el-input
          v-model="queryParams.subTaskName"
          placeholder="请输入培训任务"
          clearable
          class="!w-230px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="loading" :data="tableData">
      <el-table-column label="序号" width="80" type="index">
        <template #default="scope">
          <span>{{ (pageNum - 1) * pageSize + scope.$index + 1 }}</span>
        </template>
      </el-table-column>
      <el-table-column label="姓名" prop="userName" />
      <el-table-column label="所属部门" prop="deptName" />
      <el-table-column label="培训任务" prop="subTaskName" />
      <el-table-column label="抓拍照片" prop="capturePhoto" min-width="200">
        <template #default="scope">
          <div
            class="flex justify-center"
            v-for="url in scope.row.capturePhoto?.split(',')"
          >
            <el-image
              class="w-100px h-100px"
              :src="scope.row.selfPhoto"
              :preview-src-list="[url]"
            />
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button
            link
            type="primary"
            icon="View"
            @click="handleDetail(scope.row)"
          >
            详情
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="pageNum"
      v-model:limit="pageSize"
    />

    <!-- 详情弹窗 -->
    <DetailDialog ref="detailDialogRef" />
  </div>
</template>

<script setup name="VideoMonitor">
  import { deptTreeSelect } from "@/api/system/user"
  import { videoMonitorList } from "@/api/monitor/video"
  import DetailDialog from "./components/detailDialog.vue"

  const detailDialogRef = ref()
  const dateRange = ref([])
  const { proxy } = getCurrentInstance()
  const { terminal_source, traning_learned_type } = proxy.useDict(
    "terminal_source",
    "traning_learned_type"
  )
  const tableData = ref([])
  const loading = ref(true)
  const total = ref(0)
  const pageNum = ref(1)
  const pageSize = ref(10)
  const deptOptions = ref()
  const queryParams = ref({
    terminalSource: undefined, // 终端来源 0-电脑 1-手机
    userId: undefined, // 用户ID
    userName: undefined, // 用户名
    deptId: undefined, // 部门ID
    deptName: undefined, // 部门名称
    taskId: undefined, // 任务ID
    taskName: undefined, // 任务名称
    captureTime: undefined, // 抓拍时间
    subTaskType: undefined, // 子任务类型 0-课程 1-考试 2-资料
    subTaskId: undefined, // 子任务ID
    subTaskName: undefined, // 子任务名称
    capturePhoto: undefined, // 抓拍照片
    matchResult: undefined, // 匹配结果
    queryTimeFrom: undefined, // 查询时间-开始
    queryTimeTo: undefined // 查询时间-结束
  })

  /** 查询登录日志列表 */
  const getList = async () => {
    loading.value = true
    const response = await videoMonitorList(queryParams.value)
    tableData.value = response.rows
    total.value = response.total
    loading.value = false
  }
  /** 搜索按钮操作 */
  const handleQuery = () => {
    pageNum.value = 1
    getList()
  }
  /** 重置按钮操作 */
  function resetQuery() {
    proxy.resetForm("queryRef")
    handleQuery()
  }

  const handleDetail = row => {
    detailDialogRef.value.openDialog(row.taskId)
  }
  const selectDate = () => {
    if (dateRange.value != null) {
      queryParams.value.queryTimeFrom = dateRange.value[0]
      queryParams.value.queryTimeTo = dateRange.value[1]
    } else {
      queryParams.value.queryTimeFrom = ""
      queryParams.value.queryTimeTo = ""
    }
  }

  /** 查询部门下拉树结构 */
  function getDeptTree() {
    deptTreeSelect().then(response => {
      deptOptions.value = response.data
    })
  }

  onMounted(() => {
    getList()
    getDeptTree()
  })
</script>
