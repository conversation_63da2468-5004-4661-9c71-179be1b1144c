<!--
 * @Description: 新增试题页面
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-02-27 16:44:46
 * @LastEditTime: 2023-04-03 10:35:17
-->
<template>
  <div class="app-container">
    <el-form :model="form" ref="formRef" label-width="100px" :rules="rules">
      <el-form-item label="试题类型" prop="questionType">
        <el-select
          v-model="form.questionType"
          clearable
          placeholder="试题类型"
          @change="questionTypeChange"
        >
          <el-option
            v-for="dict in exam_question_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="试题目录" prop="catalogueId">
        <el-tree-select
          v-model="form.catalogueId"
          :data="catalogueOptions"
          :props="{
            value: 'catalogueId',
            label: 'catalogueName',
            children: 'children',
            disabled: 'disabled'
          }"
          value-key="catalogueId"
          placeholder="选择试题目录"
          check-strictly
          @current-change="catalogueSelect"
          default-expand-all
        />
      </el-form-item>
      <el-form-item label="题干" prop="questionName">
        <el-input
          style="width: 500px"
          v-model="rawForm.questionName"
          @focus="inputClick(form.questionName, 'questionName')"
        />
      </el-form-item>

      <!-- 单选题或多选题 -->
      <template v-if="form.questionType === 'S' || form.questionType === 'M'">
        <el-form-item label="选项" porp="item1">
          <el-form-item
            v-for="(item, index) in items"
            :label="item.prefix"
            :key="item.prefix"
            label-width="50px"
            class="question-item-label"
          >
            <el-input
              v-model="item.prefix"
              style="width: 50px; display: inline-block"
            />
            <el-input
              v-model="rawForm[`item${index + 1}`]"
              @focus="inputClick(form[`item${index + 1}`], `item${index + 1}`)"
              class="question-item-content-input"
            />
            <el-button
              v-if="index !== 0"
              type="danger"
              class="question-item-remove"
              @click="questionItemRemove(index)"
              >删除</el-button
            >
          </el-form-item>
          <el-button
            type="primary"
            class="question-item-remove"
            @click="questionItemAdd()"
            >添加选项</el-button
          >
        </el-form-item>

        <el-form-item label="正确答案" prop="answer">
          <el-radio-group
            v-model="form.answer"
            v-if="form.questionType === 'S'"
          >
            <el-radio
              v-for="item in items"
              :key="item.prefix"
              :label="item.prefix"
              >{{ item.prefix }}</el-radio
            >
          </el-radio-group>

          <el-checkbox-group
            v-model="form.answer"
            v-else-if="form.questionType === 'M'"
          >
            <el-checkbox
              v-for="item in items"
              :key="item.prefix"
              :label="item.prefix"
            />
          </el-checkbox-group>
        </el-form-item>
      </template>
      <!-- 判断题 -->
      <template v-else-if="form.questionType === 'J'">
        <el-form-item label="选项" prop="item1">
          <el-form-item
            :label="item.prefix"
            :key="item.prefix"
            v-for="(item, index) in items"
            label-width="50px"
            class="question-item-label"
          >
            <el-input v-model="item.prefix" style="width: 50px" disabled />
            <el-input
              v-model="rawForm[`item${index + 1}`]"
              @focus="inputClick(form[`item${index + 1}`], `item${index + 1}`)"
              class="question-item-content-input"
              disabled
            />
          </el-form-item>
        </el-form-item>

        <el-form-item label="正确答案" prop="answer">
          <el-radio-group v-model="form.answer">
            <el-radio
              v-for="item in items"
              :key="item.prefix"
              :label="item.prefix"
              >{{ item.prefix }}</el-radio
            >
          </el-radio-group>
        </el-form-item>
      </template>
      <!-- 填空题 -->
      <template v-else-if="form.questionType === 'F'">
        <template v-for="item in fillCount">
          <el-form-item :label="`填空${item}答案`" prop="answer">
            <el-input
              v-model="rawForm.answer"
              class="question-item-content-input"
              style="width: 80%"
            />
          </el-form-item>
        </template>
      </template>
      <!-- 简答题 -->
      <template v-else-if="form.questionType === 'Q'">
        <el-form-item label="答案" prop="answer">
          <el-input
            v-model="rawForm.answer"
            @focus="inputClick(form.answer, 'answer')"
          />
        </el-form-item>
      </template>
      <!-- ================== 案例题todo ==================== -->

      <el-form-item label="解析" prop="analysis">
        <el-input
          v-model="rawForm.analysis"
          @focus="inputClick(form.analysis, 'analysis')"
        />
      </el-form-item>
      <el-form-item label="难度" prop="degreeType">
        <el-select v-model="form.degreeType" placeholder="难度">
          <el-option
            v-for="dict in exam_degree_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="submitForm">提交</el-button>
        <el-button @click="reset">重置</el-button>
      </el-form-item>
    </el-form>

    <richEditorDialog ref="richEditorDialogRef" @ok="handleRichEditorContent" />
  </div>
</template>

<script setup name="QuestionAdd">
  import { addQuestion } from "@/api/onlineExam/question"
  import { catalogueList } from "@/api/system/catalogue"
  import richEditorDialog from "./components/richEditorDialog"
  import catalogue from "@/utils/catalogue.js"
  import { countSubstr } from "@/utils/common.js"

  const { proxy } = getCurrentInstance()
  const { exam_question_type, exam_degree_type } = proxy.useDict(
    "exam_question_type",
    "exam_degree_type"
  )

  const catalogueOptions = ref([])
  const items = ref([])

  const data = reactive({
    form: {
      // questionType: undefined,
      // catalogueId: undefined,
      // degreeType: undefined,
      // item1: undefined,
      // item2: undefined,
      // item3: undefined,
      // item4: undefined,
      // item5: undefined,
      // item6: undefined,
      // item7: undefined,
      // item8: undefined,
      // item9: undefined,
      // item10: undefined
    },
    rawForm: {},
    rules: {
      questionType: [
        { required: true, message: "试题类型不能为空", trigger: "change" }
      ],
      catalogueId: [
        { required: true, message: "试题目录不能为空", trigger: "change" }
      ],
      questionName: [
        { required: true, message: "题干不能为空", trigger: "change" }
      ],
      item1: [{ required: true, message: "选项不能为空", trigger: "change" }],
      answer: [{ required: true, message: "答案不能为空", trigger: "change" }],
      analysis: [
        { required: true, message: "分析不能为空", trigger: "change" }
      ],
      degreeType: [
        { required: true, message: "试题难度不能为空", trigger: "change" }
      ]
    }
  })
  const { form, rules, rawForm } = toRefs(data)

  /** 查询菜单下拉树结构 */
  function getTreeselect() {
    catalogueOptions.value = []
    catalogueList({ catalogueType: catalogue.SUBJECT_CATALOGUE }).then(
      response => {
        const catalogueTree = {
          catalogueId: 0,
          catalogueName: "主类目",
          children: [],
          disabled: true
        }
        catalogueTree.children = proxy.handleTree(response.rows, "catalogueId")
        catalogueOptions.value.push(catalogueTree)
      }
    )
  }

  const inputClick = (valueHtml, parameterKey) => {
    proxy.$refs["richEditorDialogRef"].show(
      valueHtml,
      parameterKey,
      form.value.questionType
    )
  }

  // 插入填空题数量
  const fillCount = ref()
  const handleRichEditorContent = (htmlValue, key, rawValue) => {
    fillCount.value = countSubstr(htmlValue, "填空")
    form.value[key] = htmlValue
    rawForm.value[key] = rawValue
  }

  const questionTypeChange = type => {
    resetRichEditor()
    items.value = []
    if (type === "S" || type === "M") {
      items.value = [
        { prefix: "A", content: "" },
        { prefix: "B", content: "" },
        { prefix: "C", content: "" },
        { prefix: "D", content: "" }
      ]
      if (type === "S") {
        form.value.answer = ""
      } else {
        form.value.answer = []
      }
    } else if (type === "J") {
      items.value = [
        { id: null, prefix: "A", content: "是" },
        { id: null, prefix: "B", content: "否" }
      ]
      form.value.item1 = "是"
      rawForm.value.item1 = "是"
      form.value.item2 = "否"
      rawForm.value.item2 = "否"
    } else if (type === "F") {
      items.value = []
    }
  }

  const resetRichEditor = () => {
    rawForm.value = {}
    for (let i = 1; i < 11; i++) {
      form.value[`item${i}`] = undefined
    }
    form.value.questionName = undefined
    form.value.answer = undefined
    form.value.analysis = undefined
  }

  const questionItemRemove = index => {
    items.value.splice(index, 1)
  }
  const questionItemAdd = () => {
    if (items.value.length >= 10) {
      proxy.$modal.msgError("最多添加10个选项")
      return
    }
    items.value.push({ prefix: getNextLetter(), content: "" })
  }

  const getNextLetter = () => {
    if (items.value.length === 0) return
    const prevLetter = items.value[items.value.length - 1].prefix
    if (!prevLetter) return
    return String.fromCharCode(prevLetter.charCodeAt(0) + 1)
  }

  const submitForm = () => {
    proxy.$refs["formRef"].validate(valid => {
      if (valid) {
        if (form.value.questionType === "M") {
          form.value.answer = form.value.answer.sort().join()
        }
        addQuestion(form.value).then(res => {
          if (res.code === 200) {
            reset()
            proxy.$modal.msgSuccess("新增成功")
            proxy.$tab.closeOpenPage({
              path: "/onlineExam/questionManage"
            })
          }
        })
      }
    })
  }

  const catalogueSelect = (data, node) => {
    form.value.catalogueName = data.catalogueName
  }

  /** 表单重置 */
  function reset() {
    form.value = {}
    rawForm.value = {}
  }

  getTreeselect()
</script>
<style lang="scss" scoped>
  :deep(.el-form-item__content) {
    display: block;
  }
  :deep(.el-input) {
    display: inline-block;
    width: auto;
  }
  :deep(.el-input__wrapper) {
    width: 100%;
  }
</style>
