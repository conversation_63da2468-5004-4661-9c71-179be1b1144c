import request from "@/utils/request"

// 添加课程章节
export function addPassage(data) {
  return request({
    url: "/course/passage",
    method: "post",
    data: data
  })
}

// 查询章节列表
export function listPassage(query) {
  return request({
    url: "/course/passage/list",
    method: "get",
    params: query
  })
}

// 通过章节Id获取某一章节信息
export function getInfoPassage(coursePassageId) {
  return request({
    url: "/course/passage/" + coursePassageId,
    method: "get"
  })
}

// 修改章节
export function updatePassage(data) {
  return request({
    url: "/course/passage",
    method: "put",
    data: data
  })
}

// 删除章节
export function delPassage(coursePassageId) {
  return request({
    url: "/course/passage/" + coursePassageId,
    method: "delete"
  })
}
