/*
 * @Description: 考试相关API
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-02-27 14:23:35
 * @LastEditTime: 2023-11-02 09:41:17
 */
import request from "@/utils/request"

// 查询试卷列表
export function listPaper(query) {
  return request({
    url: "/exam/paper/list",
    method: "get",
    params: query
  })
}

// 查询试卷详细
export function getPaper(paperId) {
  return request({
    url: "/exam/paper/getInfo/" + paperId,
    method: "get"
  })
}

// 新增试卷
export function addPaper(data) {
  return request({
    url: "/exam/paper",
    method: "post",
    data: data
  })
}

// 修改试卷
export function updatePaper(data) {
  return request({
    url: "/exam/paper",
    method: "put",
    data: data
  })
}

// 删除试卷
export function delPaper(paperId) {
  return request({
    url: "/exam/paper/" + paperId,
    method: "delete"
  })
}

// 查询某类题型试题目录下题目总数
export function queryQuestionSum(data) {
  return request({
    url: "/exam/question/getCount",
    method: "post",
    data
  })
}

// 预览试卷详情信息
export function previewPaper(paperId) {
  return request({
    url: "/exam/paper/preview/" + paperId,
    method: "get"
  })
}

// 导出试卷详情word
export function exportPaperWord(data) {
  return request({
    url: "/exam/paper/exportPaper",
    method: "post",
    data,
    responseType: "blob" //将文件流转成blob对象
    // responseType: `arraybuffer`
  })
}
