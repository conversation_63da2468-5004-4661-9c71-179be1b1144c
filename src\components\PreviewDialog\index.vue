<!--
 * @Description: 
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-11-13 13:49:01
 * @LastEditTime: 2024-11-13 14:05:51
-->
<template>
  <div class="filePreviewDialog">
    <el-dialog
      title="文件预览"
      v-model="dialogFormVisible"
      :close-on-click-modal="false"
      @close="close"
      :key="new Date().getTime()"
      align-center
      :append-to-body="true"
      width="1580px"
    >
      <vue-office-docx
        v-if="isWordFile(currentFile)"
        :src="currentFile"
        style="height: 100vh"
      />
      <vue-office-pdf
        v-else-if="isPdfFile(currentFile)"
        :src="currentFile"
        style="height: 100vh"
      />
      <iframe
        v-else
        ref="dynamicIframe"
        frameborder="0"
        :src="iframeUrl"
        style="width: 100%; height: calc(100vh - 60px)"
      />
    </el-dialog>
  </div>
</template>
<script setup name="PreviewDialog">
  import VueOfficeDocx from "@vue-office/docx"
  import VueOfficePdf from "@vue-office/pdf"
  import "@vue-office/docx/lib/index.css"
  import { Base64 } from "js-base64"
  import { isWordFile, isPdfFile } from "@/utils"

  const emit = defineEmits("closeDialog")
  const dialogFormVisible = ref(false)
  const iframeUrl = ref("")
  const kkFileURL = import.meta.env.VITE_APP_KK_URL
  const currentFile = ref("")
  const fileLoad = (url, islocal) => {
    currentFile.value = url
    dialogFormVisible.value = true
    if (islocal) {
      iframeUrl.value = url
    } else {
      if (isWordFile(currentFile.value) || isPdfFile(currentFile.value)) return
      iframeUrl.value =
        `${kkFileURL}/onlinePreview?url=` +
        encodeURIComponent(Base64.encode(currentFile.value))
    }
  }
  const close = () => {
    dialogFormVisible.value = false
    emit("closeDialog")
  }

  defineExpose({
    fileLoad
  })
</script>

<style lang="scss" scoped>
  .filePreviewDialog {
    :deep(.el-dialog__body) {
      padding: 0 16px !important;
    }
  }
</style>
