<!--
 * @Description: 富文本编辑页面
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-02-28 13:28:26
 * @LastEditTime: 2023-07-13 14:29:05
-->
<template>
  <el-dialog v-model="visible" width="70%" center>
    <Toolbar
      v-if="visible"
      style="border-bottom: 1px solid #ccc"
      :editor="editorRef"
      :defaultConfig="toolbarConfig"
      mode="simple"
    />
    <Editor
      v-if="visible"
      style="height: 500px; overflow-y: hidden"
      v-model="valueHtml"
      :defaultConfig="editorConfig"
      mode="simple"
      @onCreated="handleCreated"
    />
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="handleConfirm">确 定</el-button>
        <el-button @click="visible = false">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup name="richEditorDialog">
  import "@wangeditor/editor/dist/css/style.css" // 引入 css
  import { Editor, Toolbar } from "@wangeditor/editor-for-vue"
  import InsertGapFillingMenu from "../insertFilling"
  import { Boot } from "@wangeditor/editor"

  const visible = ref(false)
  const editorRef = shallowRef(null)
  const valueHtml = ref("")
  const key = ref("")
  const toolbarConfig = {}
  const editorConfig = { placeholder: "请输入内容..." }
  const questionType = ref(null)

  const emit = defineEmits(["ok"])

  /** 查询参数列表 */
  function show(value, parameterKey, type) {
    valueHtml.value = value || ""
    visible.value = true
    key.value = parameterKey
    if (type) questionType.value = type
  }

  const handleCreated = editor => {
    editorRef.value = editor // 记录 editor 实例，重要！
    if (questionType.value === "F") {
      // 新增toolbar菜单
      const menu1Conf = {
        key: "menu1", // 定义 menu key ：要保证唯一、不重复（重要）
        factory() {
          return new InsertGapFillingMenu()
        }
      }
      const allMenuKeys = editorRef.value?.getAllMenuKeys()
      if (!allMenuKeys) return
      if (allMenuKeys.includes("menu1")) return
      Boot.registerMenu(menu1Conf)
      toolbarConfig.insertKeys = {
        index: 32, // 插入的位置，基于当前的 toolbarKeys
        keys: ["menu1"]
      }
    }
  }

  const handleConfirm = () => {
    const editor = editorRef.value
    const text = editor.getText()
    // todo=============== 填空暂时不做
    if (questionType.value === "F" && key.value === "answer") {
      let spanRegex = new RegExp("<u>(.*?)<\\/u>", "g")
      let gapfillingItems = valueHtml.value.match(spanRegex)
      if (gapfillingItems === null) {
        proxy.$modal.msgError("请插入填空")
        return
      }
    }
    visible.value = false
    emit("ok", valueHtml.value, key.value, text)
  }

  onBeforeUnmount(() => {
    editorRef.value && editorRef.value.destroy()
  })

  defineExpose({
    show
  })
</script>
