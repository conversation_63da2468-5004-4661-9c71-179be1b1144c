<!--
 * @Description: 新闻管理
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-04-04 13:19:35
 * @LastEditTime: 2023-09-15 16:32:23
-->
<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
    >
      <el-form-item label="业务类型" prop="dataType">
        <el-select
          v-model="queryParams.dataType"
          placeholder="业务类型"
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="dict in devops_news_data_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="标题" prop="newsTitle">
        <el-input
          v-model="queryParams.newsTitle"
          placeholder="标题"
          clearable
          style="width: 240px"
          @keyup.enter="getList"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="getList"
          >搜索</el-button
        >
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb-8px">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleUpdate">
          新增
        </el-button>
      </el-col>
      <right-toolbar
        :search="false"
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="tableData">
      <el-table-column prop="dataType" label="业务类型">
        <template #default="scope">
          <dict-tag
            :options="devops_news_data_type"
            :value="scope.row.dataType"
          /> </template
      ></el-table-column>
      <el-table-column prop="newsTitle" label="标题"></el-table-column>
      <el-table-column
        prop="catalogueName"
        label="所属目录名称"
      ></el-table-column>
      <el-table-column prop="newsContent" label="内容"></el-table-column>
      <el-table-column
        prop="publishStartTime"
        label="发布开始时间"
      ></el-table-column>
      <el-table-column
        prop="publishEndTime"
        label="发布结束时间"
      ></el-table-column>
      <el-table-column
        label="操作"
        width="200"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-button
            link
            type="primary"
            icon="Edit"
            @click="handleUpdate(scope.row)"
            >修改</el-button
          >
          <el-button
            link
            type="primary"
            icon="Delete"
            @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 弹窗, 新增 / 修改 -->
    <addEditDialog
      ref="addEditDialogRef"
      @refreshDataList="getList"
    ></addEditDialog>
  </div>
</template>

<script setup name="swiperManageList">
  import addEditDialog from "./addEditDialog.vue"
  import { listNews, delNews } from "@/api/opertaionManage/news.js"

  const { proxy } = getCurrentInstance()
  const { devops_news_data_type } = proxy.useDict("devops_news_data_type")

  const tableData = ref([])
  const loading = ref(false)
  const showSearch = ref(true)
  const total = ref(0)
  const addEditDialogRef = ref()

  const data = reactive({
    queryParams: {
      pageNum: 1,
      pageSize: 10
    }
  })

  const { queryParams } = toRefs(data)

  /** 查询目录列表 */
  function getList() {
    loading.value = true
    listNews(queryParams.value).then(response => {
      tableData.value = response.rows || []
      total.value = response.total
      loading.value = false
    })
  }
  /** 修改按钮操作 */
  function handleUpdate(row) {
    addEditDialogRef.value.openDialog(row?.newsId)
  }
  /** 删除按钮操作 */
  function handleDelete(row) {
    proxy.$modal
      .confirm('是否确认删除名称为"' + row.newsTitle + '"的数据项?')
      .then(function () {
        return delNews(row.newsId)
      })
      .then(() => {
        getList()
        proxy.$modal.msgSuccess("删除成功")
      })
      .catch(() => {})
  }

  function resetQuery() {
    proxy.resetForm("queryRef")
    getList()
  }

  getList()
</script>
<style lang="scss" scoped></style>
