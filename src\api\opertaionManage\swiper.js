/*
 * @Description: 轮播图管理相关API
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-03-14 13:29:06
 * @LastEditTime: 2023-03-14 13:46:52
 */
import request from "@/utils/request"
// 查询轮播图列表
export function listSwiper(query) {
  return request({
    url: "/devops/carousel/list",
    method: "get",
    params: query
  })
}

// 查询轮播图管理详细
export function getSwiper(carouselId) {
  return request({
    url: "/devops/carousel/" + carouselId,
    method: "get"
  })
}

// 新增轮播图管理
export function addSwiper(data) {
  return request({
    url: "/devops/carousel",
    method: "post",
    data: data
  })
}

// 修改轮播图管理
export function updateSwiper(data) {
  return request({
    url: "/devops/carousel",
    method: "put",
    data: data
  })
}

// 删除轮播图管理
export function delSwiper(carouselId) {
  return request({
    url: "/devops/carousel/" + carouselId,
    method: "delete"
  })
}
