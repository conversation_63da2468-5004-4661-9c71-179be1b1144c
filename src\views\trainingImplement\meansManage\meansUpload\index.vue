<!--
 * @Description: 新增/修改资料-入口文件
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-03-08 11:28:06
 * @LastEditTime: 2024-03-15 13:40:00
-->
<template>
  <section class="index-container">
    <component
      :is="currentView"
      :row="row"
      @updateCurrentView="updateCurrentView"
    ></component>
  </section>
</template>

<script setup name="courseAddIndex">
  import addEditMeans from "./addEditMeans"
  import userServiceAgree from "@/views/onlineCourse/courseAdd/userServiceAgree"
  import useUserStore from "@/store/modules/user"

  const { user } = storeToRefs(useUserStore())
  const row = ref({})
  const route = useRoute()
  const currentView = shallowRef(addEditMeans)

  if (route.query.flag === "add" && user.value.userId !== 1) {
    row.value.agreeFlag = "means"
    currentView.value = userServiceAgree
  }

  const updateCurrentView = (view, item) => {
    row.value = item
    if (view === "userServiceAgree") {
      currentView.value = userServiceAgree
    } else {
      currentView.value = addEditMeans
    }
  }
</script>
