/*
 * @Description: 租户管理相关API
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-06-13 15:45:02
 * @LastEditTime: 2023-08-16 15:13:12
 */
import request from "@/utils/request"

// ========================================= 租户相关 =========================================
// 查询租户列表
export function listTenant(query) {
  return request({
    url: "/system/tenant/list",
    method: "get",
    params: query
  })
}

// 查询租户详细
export function getTenant(tenantId) {
  return request({
    url: "/system/tenant/" + tenantId,
    method: "get"
  })
}

// 新增租户
export function addTenant(data) {
  return request({
    url: "/system/tenant",
    method: "post",
    data
  })
}

// 修改租户
export function updateTenant(data) {
  return request({
    url: "/system/tenant",
    method: "put",
    data
  })
}

// 删除租户
export function delTenant(tenantId) {
  return request({
    url: "/system/tenant/" + tenantId,
    method: "delete"
  })
}

//刷新域名
export function refreshUrl(data) {
  return request({
    url: "/system/tenant/refresh-tasks",
    method: "post",
    data
  })
}

// ========================================= 租户套餐相关 =========================================
// 查询租户套餐列表
export function listPackage(query) {
  return request({
    url: "/system/package/list",
    method: "get",
    params: query
  })
}

// 查询租户套餐详细
export function getPackage(packageId) {
  return request({
    url: "/system/package/" + packageId,
    method: "get"
  })
}

// 新增租户套餐
export function addPackage(data) {
  return request({
    url: "/system/package",
    method: "post",
    data
  })
}

// 修改租户套餐
export function updatePackage(data) {
  return request({
    url: "/system/package",
    method: "put",
    data
  })
}

// 删除租户套餐
export function delPackage(packageId) {
  return request({
    url: "/system/package/" + packageId,
    method: "delete"
  })
}

// ========================== 查询租户信息 ==========================
export function querySysTenantInfo() {
  return request({
    url: "system/tenant/tenantInfo",
    method: "get"
  })
}
