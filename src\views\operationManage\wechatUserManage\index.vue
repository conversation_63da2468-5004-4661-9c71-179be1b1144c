<!--
 * @Description: 微信用户管理
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-04-12 13:47:55
 * @LastEditTime: 2023-07-25 09:36:36
-->
<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
    >
      <el-form-item label="open_id" prop="openId">
        <el-input
          v-model="queryParams.openId"
          placeholder="open_id"
          clearable
          style="width: 240px"
          @keyup.enter="getList"
        />
      </el-form-item>
      <el-form-item label="用户手机号" prop="phoneNumber">
        <el-input
          v-model="queryParams.phoneNumber"
          placeholder="用户昵称"
          clearable
          style="width: 240px"
          @keyup.enter="getList"
        />
      </el-form-item>
      <el-form-item label="用户昵称" prop="nickName">
        <el-input
          v-model="queryParams.nickName"
          placeholder="用户昵称"
          clearable
          style="width: 240px"
          @keyup.enter="getList"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="getList"
          >搜索</el-button
        >
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb-8px">
      <right-toolbar
        :search="false"
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="tableData">
      <el-table-column prop="openId" label="open_id"></el-table-column>
      <el-table-column prop="unionId" label="union_id"></el-table-column>
      <el-table-column prop="phoneNumber" label="用户手机号"></el-table-column>
      <el-table-column prop="nickName" label="用户昵称"></el-table-column>
      <el-table-column prop="avatarUrl" label="用户头像">
        <template #default="scope">
          <el-popover placement="right" :width="400" trigger="hover">
            <img :src="scope.row.avatarUrl" width="375" height="375" />
            <template #reference>
              <img
                :src="scope.row.avatarUrl"
                style="max-height: 60px; max-width: 60px"
              />
            </template>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column prop="gender" label="用户性别">
        <template #default="scope">
          <dict-tag :options="sys_user_sex" :value="scope.row.phoneNumber" />
        </template>
      </el-table-column>
      <el-table-column prop="email" label="用户邮箱"></el-table-column>
      <el-table-column prop="province" label="用户所属城市"></el-table-column>
      <el-table-column prop="country" label="用户所属地区"></el-table-column>
      <el-table-column prop="status" label="帐号状态">
        <template #default="scope">
          <dict-tag :options="sys_job_status" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column prop="loginIp" label="最后登录IP"></el-table-column>
      <el-table-column prop="loginDate" label="最后登录时间"></el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script setup name="wechatUserManageList">
  import { listWxUser } from "@/api/opertaionManage/wechat.js"

  const { proxy } = getCurrentInstance()
  const { sys_user_sex, sys_job_status } = proxy.useDict(
    "sys_user_sex",
    "sys_job_status"
  )

  const tableData = ref([])
  const loading = ref(false)
  const showSearch = ref(true)
  const total = ref(0)

  const data = reactive({
    queryParams: {
      pageNum: 1,
      pageSize: 10
    }
  })

  const { queryParams } = toRefs(data)

  /** 查询目录列表 */
  function getList() {
    loading.value = true
    listWxUser(queryParams.value).then(response => {
      tableData.value = response.rows || []
      total.value = response.total
      loading.value = false
    })
  }

  function resetQuery() {
    proxy.resetForm("queryRef")
    getList()
  }

  getList()
</script>
<style lang="scss" scoped></style>
