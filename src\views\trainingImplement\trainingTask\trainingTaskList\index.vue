<!--
 * @Description: 培训任务-入口文件
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-06-12 14:24:05
 * @LastEditTime: 2023-11-02 10:49:49
-->
<template>
  <section class="index-container">
    <component
      :is="currentView"
      :row="row"
      @updateCurrentView="updateCurrentView"
    ></component>
  </section>
</template>

<script setup name="trainingTaskIndex">
  import taskList from "./taskList.vue"
  import taskDetail from "@/views/trainingProfile/reportStatistics/taskReport/taskDetail.vue"
  import taskPersonDetail from "@/views/trainingProfile/reportStatistics/taskReport/taskPersonDetail.vue"

  const row = ref({})
  const currentView = shallowRef(taskList)

  const updateCurrentView = (view, item) => {
    row.value = item
    if (view === "taskDetail") {
      currentView.value = taskDetail
    } else if (view === "taskList") {
      currentView.value = taskList
    } else if (view === "taskPersonDetail") {
      currentView.value = taskPersonDetail
    }
  }
</script>
