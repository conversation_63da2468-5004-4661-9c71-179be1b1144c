<!--
 * @Description: 公益片管理
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-11-15 09:21:07
 * @LastEditTime: 2024-11-15 14:56:32
-->

<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true">
      <el-form-item label="视频标题" prop="title">
        <el-input
          v-model="queryParams.title"
          placeholder="请输入视频标题"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="视频类型" prop="videoType">
        <el-select
          v-model="queryParams.videoType"
          placeholder="请选择视频类型"
          clearable
        >
          <el-option
            v-for="dict in public_trailer_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="上传人" prop="createBy">
        <el-input
          v-model="queryParams.createBy"
          placeholder="请输入上传人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="审核结果" prop="approveResult">
        <el-select
          v-model="queryParams.approveResult"
          placeholder="请选择审核结果"
          clearable
        >
          <el-option
            v-for="dict in review_result"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-table v-loading="loading" :data="tableData">
      <el-table-column label="视频标题" align="center" prop="title" />
      <el-table-column label="视频封面" align="center" prop="cover">
        <template #default="scope">
          <el-popover placement="right" :width="400" trigger="hover">
            <img
              :src="scope.row.cover"
              style="
                max-height: 360px;
                max-width: 360px;
                width: 100%;
                height: 100%;
              "
            />
            <template #reference>
              <img
                :src="scope.row.cover"
                style="max-height: 60px; max-width: 60px"
              />
            </template>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column
        label="视频类型"
        align="center"
        prop="videoType"
        width="150"
      >
        <template #default="scope">
          <dict-tag
            :options="public_trailer_type"
            :value="scope.row.videoType"
          />
        </template>
      </el-table-column>
      <el-table-column label="上传时间" align="center" prop="createTime" />
      <el-table-column label="上传人" align="center" prop="createBy" />
      <el-table-column label="所属部门" align="center" prop="deptName" />
      <el-table-column label="审核结果" align="center" prop="approveResult">
        <template #default="scope">
          <dict-tag :options="review_result" :value="scope.row.approveResult" />
        </template>
      </el-table-column>
      <el-table-column label="审核时间" align="center" prop="approveTime" />
      <el-table-column label="操作" align="center">
        <template #default="scope">
          <el-button
            v-if="scope.row.approveResult === null"
            link
            type="warning"
            @click="handleOperation('review', scope.row)"
            >审核</el-button
          >
          <el-button
            link
            type="primary"
            @click="handleOperation('detail', scope.row)"
            >查看</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
    <TrailerForm ref="trailerFormRef" @refresh="getList" />
  </div>
</template>

<script setup name="trailerList">
  import TrailerForm from "./TrailerForm.vue"
  import { delVideo, listVideo } from "@/api/opertaionManage/trailer.js"

  const { proxy } = getCurrentInstance()
  const { public_trailer_type, review_result } = proxy.useDict(
    "public_trailer_type",
    "review_result"
  )
  const tableData = ref([])
  const loading = ref(true)
  const trailerFormRef = ref()
  const total = ref(0)

  const queryParams = ref({
    pageNum: 1,
    pageSize: 10,
    title: "",
    videoType: "",
    createBy: "",
    approveStatus: ""
  })

  /** 查询目录列表 */
  function getList() {
    loading.value = true
    listVideo(queryParams.value).then(response => {
      tableData.value = response.rows || []
      total.value = response.total
      loading.value = false
    })
  }
  /** 搜索按钮操作 */
  function handleQuery() {
    getList()
  }
  /** 重置按钮操作 */
  function resetQuery() {
    proxy.resetForm("queryRef")
    handleQuery()
  }
  const handleOperation = (flag, row) => {
    trailerFormRef.value.openDialog(flag, row)
  }
  /** 删除按钮操作 */
  function handleDelete(row) {
    proxy.$modal
      .confirm('是否确认删除名称为"' + row.manageName + '"的数据项?')
      .then(function () {
        return delVideo(row.manageId)
      })
      .then(() => {
        getList()
        proxy.$modal.msgSuccess("删除成功")
      })
      .catch(() => {})
  }

  onMounted(() => {
    getList()
  })
</script>
<style lang="scss" scoped></style>
