<!--
 * @Description: 首页-平台本月增长
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-08-03 11:44:54
 * @LastEditTime: 2023-12-11 13:23:10
-->

<template>
  <el-card :bordered="false">
    <template #header> <div class="title">本月平台增长</div> </template>

    <el-row>
      <el-col :span="12">
        <el-statistic title="新增人数" :value="infoData.newlyAddedUser">
          <template #suffix> 人 </template>
        </el-statistic>
      </el-col>
      <el-col :span="12">
        <el-statistic title="新增课程数" :value="infoData.newlyAddedCourse">
          <template #suffix> 门 </template>
        </el-statistic>
      </el-col>
      <el-col :span="12">
        <el-statistic title="新增考试数" :value="infoData.newlyAddedExam">
          <template #suffix> 场 </template>
        </el-statistic>
      </el-col>
      <el-col :span="12">
        <el-statistic title="新增任务数" :value="infoData.newlyAddedTask">
          <template #suffix> 个 </template>
        </el-statistic>
      </el-col>
    </el-row>
  </el-card>
</template>

<script setup name="growthThisMonth">
  const props = defineProps({
    infoData: {
      type: Object,
      default: () => ({})
    }
  })
</script>

<style scoped>
  .title {
    padding: 8px;
    font-size: 19px;
    font-weight: bolder;
  }

  .el-col {
    text-align: center;
    margin-bottom: 31px;
  }
  :deep(.el-statistic__head) {
    font-size: 17px;
    margin: 10px 0 10px 0;
    font-weight: bolder;
  }
  :deep(.el-statistic__content) {
    font-size: 25px;
    font-weight: bold;
  }
  :deep(.el-statistic__suffix) {
    font-size: 18px;
  }
</style>
