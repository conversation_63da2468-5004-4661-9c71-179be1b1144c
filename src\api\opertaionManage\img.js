/*
 * @Description: 轮播图图片管理
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-03-14 11:39:46
 * @LastEditTime: 2023-03-14 13:49:44
 */
import request from "@/utils/request"

// 查询轮播图图片管理列表
export function listImg(query) {
  return request({
    url: "/devops/img/list",
    method: "get",
    params: query
  })
}

// 查询轮播图图片管理详细
export function getImg(carouselImgId) {
  return request({
    url: "/devops/img/" + carouselImgId,
    method: "get"
  })
}

// 新增轮播图图片管理
export function addImg(data) {
  return request({
    url: "/devops/img",
    method: "post",
    data: data
  })
}

// 修改轮播图图片管理
export function updateImg(data) {
  return request({
    url: "/devops/img",
    method: "put",
    data: data
  })
}

// 删除轮播图图片管理
export function delImg(carouselImgId) {
  return request({
    url: "/devops/img/" + carouselImgId,
    method: "delete"
  })
}
