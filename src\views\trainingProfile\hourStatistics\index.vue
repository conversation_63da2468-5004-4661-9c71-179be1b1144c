<!--
 * @Description: 学时统计-入口页面
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-06-20 11:13:12
 * @LastEditTime: 2023-06-27 11:52:03
-->
<template>
  <section class="index-container">
    <component
      :is="currentView"
      :row="row"
      @updateCurrentView="updateCurrentView"
    ></component>
  </section>
</template>

<script setup name="hourStatisticsIndex">
  import hourStatistics from "./hourStatistics.vue"
  import personalProfile from "./personalProfile.vue"

  const row = ref({})
  const currentView = shallowRef(hourStatistics)

  const updateCurrentView = (view, item) => {
    row.value = item
    if (view === "personalProfile") {
      currentView.value = personalProfile
    } else if (view === "hourStatistics") {
      currentView.value = hourStatistics
    }
  }
</script>
