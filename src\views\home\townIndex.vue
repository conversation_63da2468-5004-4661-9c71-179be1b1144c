<!--
 * @Description: 北蔡防灾减灾首页
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-12-18 17:02:59
 * @LastEditTime: 2024-04-02 15:17:03
-->

<template>
  <div class="home">
    <el-radio-group
      class="radio-tab"
      v-model="activeTab"
      size="large"
      @change="tabChange"
    >
      <el-radio-button label="1">居委</el-radio-button>
      <el-radio-button label="2">商铺</el-radio-button>
      <el-radio-button label="3">居民</el-radio-button>
    </el-radio-group>
    <el-row :gutter="20">
      <el-col :span="24">
        <div class="home-title">今日数据一览</div>
        <ul class="list-item">
          <li>
            <p>今日学习人数</p>
            <span class="num" style="color: #8287fc">
              {{ dashboardData.todayLearners || 0 }}
            </span>
            <span
              class="icon"
              style="
                background: linear-gradient(to bottom, #cb87ee, #947fed);
                box-shadow: 0 0 6px #8287fc;
              "
            >
              <el-icon size="20">
                <i class="iconfont icon-xuexiyuandi"></i>
              </el-icon>
            </span>
          </li>
          <li>
            <p>本月学习人数</p>
            <span class="num" style="color: #f46539">
              {{ dashboardData.thisMonthLearners || 0 }}
            </span>
            <span
              class="icon"
              style="
                background: linear-gradient(to bottom, #e2ad91, #ec7649);
                box-shadow: 0 0 6px #ec7649;
              "
            >
              <el-icon size="20">
                <i class="iconfont icon-bisai-"></i>
              </el-icon>
            </span>
          </li>
          <!-- 居委 -->
          <template v-if="activeTab === '1'">
            <li>
              <p>本月学习时长最长</p>
              <span class="num" style="color: #53d4c8">
                {{
                  Math.floor(dashboardData.monthMaxStudyingTime / 60) || 0
                }}分钟
              </span>
              <span
                class="icon"
                style="
                  background: linear-gradient(to bottom, #6de7c2, #24d3c3);
                  box-shadow: 0 0 6px #24d3c3;
                "
              >
                <el-icon size="20">
                  <i class="iconfont icon-renwu"></i>
                </el-icon>
              </span>
            </li>
            <li>
              <p>本季度分数最高</p>
              <el-tooltip effect="dark" :content="topDeptName" placement="top">
                <span class="num ctext" style="color: #fb7352">
                  {{ topDeptName }}
                </span>
              </el-tooltip>
              <span
                class="icon"
                style="
                  background: linear-gradient(to bottom, #fe7b63, #f86135);
                  box-shadow: 0 0 6px #f86135;
                "
              >
                <el-icon size="20">
                  <i class="iconfont icon-shouye"></i>
                </el-icon>
              </span>
            </li>
            <li>
              <p>本季度学习最长</p>
              <el-tooltip
                effect="dark"
                :content="dashboardData.quarterMaxStudyingDept"
                placement="top"
              >
                <span class="num ctext" style="color: #12abf3">
                  {{ dashboardData.quarterMaxStudyingDept }}
                </span>
              </el-tooltip>
              <span
                class="icon"
                style="
                  background: linear-gradient(to bottom, #11c8fc, #0384e8);
                  box-shadow: 0 0 6px #0384e8;
                "
              >
                <el-icon size="20">
                  <i class="iconfont icon-shiyongwendang"></i>
                </el-icon>
              </span>
            </li>
          </template>
          <!-- 商铺 -->
          <template v-else-if="activeTab === '2'">
            <li>
              <p>本月学习时长最长</p>
              <el-tooltip
                effect="dark"
                :content="dashboardData.monthMaxStudyingTime"
                placement="top"
              >
                <span class="num ctext" style="color: #53d4c8">
                  {{ dashboardData.monthMaxStudyingTime }}
                </span>
              </el-tooltip>
              <span
                class="icon"
                style="
                  background: linear-gradient(to bottom, #6de7c2, #24d3c3);
                  box-shadow: 0 0 6px #24d3c3;
                "
              >
                <el-icon size="20">
                  <i class="iconfont icon-renwu"></i>
                </el-icon>
              </span>
            </li>
            <li>
              <p>本月分数最高</p>
              <el-tooltip effect="dark" :content="topDeptName" placement="top">
                <span class="num ctext" style="color: #fb7352">
                  {{ topDeptName }}
                </span>
              </el-tooltip>
              <span
                class="icon"
                style="
                  background: linear-gradient(to bottom, #fe7b63, #f86135);
                  box-shadow: 0 0 6px #f86135;
                "
              >
                <el-icon size="20">
                  <i class="iconfont icon-shouye"></i>
                </el-icon>
              </span>
            </li>
          </template>
          <!-- 居民 -->
          <template v-else-if="activeTab === '3'">
            <li>
              <p>本月学习人数最多</p>
              <el-tooltip
                effect="dark"
                :content="dashboardData.maxMonthlyLearners"
                placement="top"
              >
                <span class="num ctext" style="color: #53d4c8">
                  {{ dashboardData.maxMonthlyLearners }}
                </span>
              </el-tooltip>
              <span
                class="icon"
                style="
                  background: linear-gradient(to bottom, #6de7c2, #24d3c3);
                  box-shadow: 0 0 6px #24d3c3;
                "
              >
                <el-icon size="20">
                  <i class="iconfont icon-renwu"></i>
                </el-icon>
              </span>
            </li>
            <li>
              <p>本月学习视频最多</p>
              <el-tooltip
                effect="dark"
                :content="dashboardData.maxMonthlyCourseWatched"
                placement="top"
              >
                <span class="num ctext" style="color: #fb7352">
                  {{ dashboardData.maxMonthlyCourseWatched }}
                </span>
              </el-tooltip>
              <span
                class="icon"
                style="
                  background: linear-gradient(to bottom, #fe7b63, #f86135);
                  box-shadow: 0 0 6px #f86135;
                "
              >
                <el-icon size="20">
                  <i class="iconfont icon-shouye"></i>
                </el-icon>
              </span>
            </li>
          </template>
        </ul>
      </el-col>
    </el-row>
    <el-row :gutter="20" style="margin-top: 20px">
      <el-col :span="13">
        <communityLearnSituation :activeTab="activeTab" />
      </el-col>
      <el-col :span="11"> <townLearnRank :activeTab="activeTab" /></el-col>
    </el-row>
    <el-row>
      <el-col :span="24">
        <committeeLearnSituation :activeTab="activeTab" />
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
  import communityLearnSituation from "./components/town/communityLearnSituation"
  import committeeLearnSituation from "./components/town/committeeLearnSituation"
  import townLearnRank from "./components/town/townLearnRank"
  import {
    townDashboard,
    storeDashboard,
    residentDashboard
  } from "@/api/home/<USER>/index"

  const { proxy } = getCurrentInstance()
  const dashboardData = ref({
    todayLearners: "",
    thisMonthLearners: "",
    monthMaxStudyingTime: "",
    quarterMaxScoreDept: "",
    quarterMaxStudyingDept: "",
    maxMonthlyLearners: "",
    maxMonthlyCourseWatched: ""
  })

  const getData = async () => {
    if (activeTab.value === "1") {
      const res = await townDashboard()
      dashboardData.value = res.data
    } else if (activeTab.value === "2") {
      const res = await storeDashboard({ userType: "02" })
      dashboardData.value = res.data
    } else {
      const res = await residentDashboard({ userType: "03" })
      dashboardData.value = res.data
    }
  }

  const topDeptName = ref("")
  onBeforeMount(() => {
    proxy.$mitt.on("getTopDeptName", val => {
      if (val) topDeptName.value = val
    })
  })

  const activeTab = ref("1")
  const tabChange = val => {
    getData()
  }

  getData()
</script>

<style lang="scss" scoped>
  :deep(.el-radio-button__inner) {
    width: 120px;
  }
  .iconfont {
    font-size: 20px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-2px, -2px);
    color: #fff;
  }
  .home {
    background: #f3f5ff;
    padding: 20px;
    color: #222d61;
    .radio-tab {
      margin: 0 0 10px 10px;
    }
  }
  .home-title {
    line-height: 40px;
    font-weight: bold;
    font-size: 20px;
    text-indent: 10px;
    margin-bottom: 10px;
  }
  .list-item {
    white-space: nowrap;
    display: flex;

    > li {
      width: 300px;
      padding: 16px 20px;
      margin: 0 10px;
      position: relative;
      display: inline-block;
      background: #fff;
      line-height: 30px;
      font-weight: bold;
      border-radius: 10px;
    }
    .icon {
      position: absolute;
      bottom: 20px;
      right: 20px;
      width: 36px;
      height: 36px;
      border-radius: 100%;
    }
    .num {
      margin-top: 10px;
      font-weight: bold;
      display: inline-block;
      font-size: 24px;
    }
    .ctext {
      font-size: 16px;
      max-width: 220px;
      /*强制文字在一行文本框内*/
      white-space: nowrap;
      /*溢出部分文字隐藏*/
      overflow: hidden;
      /*溢出部分省略号处理*/
      text-overflow: ellipsis;
    }
  }
</style>
