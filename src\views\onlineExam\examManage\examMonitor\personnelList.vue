<!--
 * @Description: 人员列表
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-03-30 09:28:54
 * @LastEditTime: 2023-10-27 11:51:04
-->

<template>
  <div class="app-container">
    <el-row :gutter="10" class="mb-8px">
      <div class="home_process_header">
        <el-icon class="backIcon" size="20" @click="backMonitor">
          <ArrowLeftBold />
        </el-icon>
        <el-divider direction="vertical"></el-divider>
        {{ row.baseName }}
        <el-divider direction="vertical"></el-divider>
        {{ row.startTime }} - {{ row.endTime }}
      </div>
    </el-row>
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
    >
      <el-form-item label="姓名" prop="userName">
        <el-input
          v-model="queryParams.userName"
          placeholder="姓名"
          clearable
          style="width: 240px"
          @keyup.enter="getList"
        />
      </el-form-item>
      <el-form-item label="部门" prop="deptId">
        <el-tree-select
          v-model="queryParams.deptId"
          :data="deptOptions"
          :props="{ value: 'id', label: 'label', children: 'children' }"
          value-key="id"
          placeholder="请选择部门"
          check-strictly
          clearable
        />
      </el-form-item>

      <el-form-item label="是否通过" prop="isPass">
        <el-select
          v-model="queryParams.isPass"
          placeholder="是否通过"
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="dict in exam_pass_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="getList">
          搜索
        </el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb-8px">
      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table :data="tableData" :border="false" v-loading="loading">
      <el-table-column label="姓名" prop="userName" />
      <el-table-column label="所属部门" prop="deptName" />
      <el-table-column label="开始时间" prop="userStartTime" />
      <el-table-column label="提交时间" prop="submitTime" />
      <el-table-column label="用时" prop="costTime">
        <template #default="scope">
          {{ formatSecondStr(scope.row.costTime) }}
        </template>
      </el-table-column>
      <el-table-column label="考试成绩" prop="userPaperScore" />
      <el-table-column label="是否通过" prop="isPassDesc" width="80" />
      <el-table-column label="考试次数" prop="examCount" width="80" />
      <el-table-column label="名次" prop="ranking" width="80" />
      <el-table-column label="切屏次数" prop="frequency" width="80" />
      <el-table-column label="答题数" prop="answerCount" width="80" />
      <el-table-column label="考试安排类型" prop="arrangeType">
        <template #default="scope">
          <dict-tag
            :options="exam_arrange_type"
            :value="scope.row.arrangeType"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        width="200"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-button
            v-if="scope.row.paperAnswerId"
            link
            type="primary"
            icon="Edit"
            @click="goExamList(scope.row)"
          >
            修改成绩
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script setup name="personnelList">
  import { formatSecondStr } from "@/utils/common"
  import { userListByArrange } from "@/api/onlineExam/exam"
  import { deptTreeSelect } from "@/api/system/user"

  const { proxy } = getCurrentInstance()
  const { exam_arrange_type, exam_pass_status } = proxy.useDict(
    "exam_arrange_type",
    "exam_pass_status"
  )
  const emit = defineEmits(["updateCurrentView"])
  const props = defineProps({
    row: {
      type: Object,
      default: () => ({})
    }
  })

  const tableData = ref([])
  const loading = ref(true)
  const showSearch = ref(true)
  const total = ref(0)
  const deptOptions = ref([])

  const data = reactive({
    queryParams: {
      pageNum: 1,
      pageSize: 10
    }
  })

  const { queryParams } = toRefs(data)

  /** 查询目录列表 */
  function getList() {
    loading.value = true
    let queryData = {
      ...queryParams.value,
      arrangeId: props.row.arrangeId
    }
    userListByArrange(queryData).then(response => {
      tableData.value = response.rows || []
      total.value = response.total
      loading.value = false
    })
  }

  const backMonitor = () => {
    emit("updateCurrentView", "monitorList")
  }

  /** 重置按钮操作 */
  function resetQuery() {
    proxy.resetForm("queryRef")
    getList()
  }

  // 修改成绩
  const goExamList = row => {
    emit("updateCurrentView", "examPaperDetails", {
      ...row,
      arrangeId: props.row.arrangeId,
      baseName: props.row.baseName,
      startTime: props.row.startTime,
      endTime: props.row.endTime
    })
  }
  /** 查询部门下拉树结构 */
  function getDeptTree() {
    deptTreeSelect().then(response => {
      deptOptions.value = response.data
    })
  }

  getList()
  getDeptTree()
</script>

<style lang="scss" scoped>
  .arrangeTable {
    margin: 0 20px;
  }

  .backIcon {
    svg {
      height: 0.7em;
    }
  }
</style>
