<!--
 * @Description: 
 * @Author: <PERSON><PERSON><PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-09-08 15:40:09
 * @LastEditTime: 2024-05-11 17:11:12
-->
<template>
  <div class="target-view" style="height: 100%">
    <div>
      <span class="title">学习排名</span>
      <span class="export" @click="handleExport">导出</span>
    </div>
    <div class="view-con">
      <el-table
        :data="tableData"
        v-el-table-infinite-scroll="load"
        stripe
        height="470"
        style="width: 100%"
        :border="false"
      >
        <el-table-column prop="ranking" label="排名" min-width="70" />
        <el-table-column prop="mobile" label="手机号" min-width="120" />
        <template v-if="domainName === 'eduxd'">
          <el-table-column prop="userName" label="用户名" min-width="120" />
          <el-table-column prop="userCode" label="用户编号" min-width="120" />
          <el-table-column prop="enterprise" label="企业名称" min-width="250" />
          <el-table-column prop="deptName" label="部门" min-width="200" />
        </template>
        <el-table-column prop="score" label="总分" min-width="80" />
        <el-table-column
          prop="courseIntegral"
          label="视频得分"
          min-width="80"
        />
        <el-table-column
          prop="userPaperScore"
          label="测试得分"
          min-width="80"
        />
        <template v-if="domainName === 'eduxd'">
          <el-table-column prop="videoCount" label="视频个数" min-width="80" />
          <el-table-column
            prop="avgExamScore"
            label="测试平均分"
            min-width="90"
          />
        </template>
        <template v-else>
          <el-table-column prop="examTime" label="测试时长" min-width="120">
            <template #default="scope">
              {{ formatSecondStr(scope.row.examTime) }}
            </template>
          </el-table-column>
          <el-table-column
            prop="totalStudyHour"
            label="学习时长"
            min-width="120"
          >
            <template #default="scope">
              {{ formatSecondStr(scope.row.totalStudyHour) }}
            </template>
          </el-table-column>
          <el-table-column
            prop="loginTime"
            label="最后登录时间"
            min-width="160"
          />
        </template>
      </el-table>
    </div>
  </div>
</template>

<script setup>
  import { getUnepStudyRank, getXdStudyRank } from "@/api/home/<USER>/index"
  import dayjs from "dayjs"
  import { formatSecondStr } from "@/utils/common"
  import useTenantStore from "@/store/modules/tenant"

  const { domainName } = storeToRefs(useTenantStore())
  const { proxy } = getCurrentInstance()
  const tableData = ref([])
  const queryParam = reactive({
    pageSize: 20,
    pageNum: 1
  })

  const total = ref(0)
  const getData = async () => {
    if (domainName.value === "eduxd") {
      queryParam.examId = "2057"
    }
    let func = domainName.value === "eduxd" ? getXdStudyRank : getUnepStudyRank
    const res = await func(queryParam)
    tableData.value = tableData.value.concat(res.rows)
    total.value = res.total
  }
  getData()
  const handelMore = () => {
    queryParam.pageNum += 1
    getData()
  }

  const handleExport = () => {
    let realApiAddress =
      domainName.value === "eduxd"
        ? "system/user/xdExport"
        : "system/user/unepExport"
    proxy.download(
      realApiAddress,
      {
        examId: domainName.value === "eduxd" ? "2057" : undefined
      },
      `排行榜_${dayjs().format("YYYY-MM-DD")}.xlsx`
    )
  }

  const load = () => {
    if (queryParam.pageNum * queryParam.pageSize < total.value) {
      handelMore()
    }
  }
</script>

<style scoped lang="scss">
  .more {
    text-align: center;
    font-size: 14px;
    color: #606266;
    line-height: 30px;
    cursor: pointer;
  }
  .export {
    float: right;
    margin-right: 10px;
    margin-top: 8px;
    font-size: 14px;
    color: #589cfd;
    cursor: pointer;
  }
  .title {
    line-height: 40px;
    font-weight: bold;
    margin-left: 10px;
  }
  .view-con {
    background: #fff;
    padding: 14px 20px;
    border-radius: 10px;
    height: 500px;
  }
</style>
