<!--
 * @Description: 首页-轮播图
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-04-11 16:39:24
 * @LastEditTime: 2023-06-02 08:45:00
-->
<template>
  <el-carousel height="150px">
    <el-carousel-item v-for="item in carouselList" :key="item">
      <el-image :src="item.img" fit="fill"></el-image>
    </el-carousel-item>
  </el-carousel>
</template>

<script setup name="carousel">
  import { listImg } from "@/api/opertaionManage/img.js"
  const carouselList = ref([
    // "https://img.traingo.cn/data/home/<USER>",
    // "https://img.traingo.cn/data/home/<USER>",
    // "https://img.traingo.cn/data/home/<USER>"
  ])

  // 获取轮播图数据
  const getBannerList = async () => {
    let queryData = {
      pageNum: 1,
      pageSize: 10,
      carouselId: 2030
    }

    const { rows } = await listImg(queryData)
    carouselList.value = rows
  }

  // 打开一个新窗口
  const leaveForOpen = url => {
    if (url) {
      window.open(url)
    }
  }

  getBannerList()
</script>

<style scoped>
  .carousel-images {
    height: 160px;
    width: 100%;
    cursor: pointer;
  }
  .snowy-right-card-one {
    height: 160px;
  }
</style>
