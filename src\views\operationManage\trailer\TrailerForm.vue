<template>
  <el-dialog
    :title="dialogType === 'review' ? '视频审核' : '查看详情'"
    v-model="visible"
    width="800px"
    append-to-body
    destroy-on-close
  >
    <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
      <el-form-item label="视频名称">
        <span>{{ form.title }}</span>
      </el-form-item>
      <el-form-item label="视频文件">
        <div class="video-container">
          <div v-if="!isPlaying" class="image-wrapper" @click="handlePlayVideo">
            <el-image
              style="width: 320px; height: 180px"
              :src="form.cover"
              fit="contain"
            />
            <div class="play-icon">
              <el-icon><video-play /></el-icon>
            </div>
          </div>
          <video
            v-show="isPlaying"
            ref="videoRef"
            style="width: 320px; height: 180px"
            :src="form.url"
            controlsList="nodownload"
            controls
            @ended="handleVideoEnd"
            @error="handleVideoError"
            @fullscreenchange="handleFullscreenChange"
          ></video>
        </div>
      </el-form-item>
      <el-form-item label="视频类型">
        <dict-tag :options="public_trailer_type" :value="form.videoType" />
      </el-form-item>
      <el-form-item label="上传人">
        <span>{{ form.createBy }}</span>
      </el-form-item>
      <el-form-item label="上传时间">
        <span>{{ form.createTime }}</span>
      </el-form-item>
      <el-form-item
        label="审核结果"
        prop="approveResult"
        v-if="dialogType === 'review'"
      >
        <el-radio-group v-model="form.approveResult">
          <el-radio
            v-for="dict in review_result"
            :key="dict.value"
            :label="dict.value"
            >{{ dict.label }}</el-radio
          >
        </el-radio-group>
      </el-form-item>
      <el-form-item label="审核结果" v-else>
        <dict-tag :options="review_result" :value="form.approveResult" />
      </el-form-item>
      <el-form-item
        label="审核意见"
        prop="approveOpinion"
        v-if="dialogType === 'review'"
        :rules="[
          {
            required: form.approveResult === '0',
            message: '审核不通过时意见必填',
            trigger: ['blur']
          }
        ]"
      >
        <el-input
          v-model="form.approveOpinion"
          type="textarea"
          :rows="3"
          placeholder="请输入审核意见"
        />
      </el-form-item>
      <el-form-item label="审核意见" v-else>
        <span>{{ form.approveOpinion }}</span>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button
          type="primary"
          @click="submitForm"
          v-if="dialogType === 'review'"
          >确 定</el-button
        >
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
  import { addApprove } from "@/api/opertaionManage/trailer.js"
  import { cloneDeep } from "lodash-es"
  import { VideoPlay } from "@element-plus/icons-vue"

  const { proxy } = getCurrentInstance()
  const { public_trailer_type, review_result } = proxy.useDict(
    "public_trailer_type",
    "review_result"
  )

  const visible = ref(false)
  const dialogType = ref("review") // review or detail
  const isPlaying = ref(false)
  const videoRef = ref(null)

  const form = ref({
    id: undefined,
    title: undefined,
    cover: undefined,
    url: undefined,
    videoType: undefined,
    createBy: undefined,
    createTime: undefined,
    approveResult: undefined,
    approveOpinion: undefined
  })

  const rules = {
    approveResult: [
      { required: true, message: "请选择审核结果", trigger: "change" }
    ]
  }

  const openDialog = (type, row) => {
    dialogType.value = type
    visible.value = true
    form.value = cloneDeep(row)
    console.log("接收到的行数据:", form.value)
  }

  const handlePlayVideo = () => {
    if (!form.value.url) {
      proxy.$modal.msgError("视频地址不存在")
      return
    }
    isPlaying.value = true
    nextTick(() => {
      if (videoRef.value) {
        videoRef.value.style.zIndex = "auto"
        const playPromise = videoRef.value.play()
        if (playPromise !== undefined) {
          playPromise.catch(error => {
            console.error("视频播放失败:", error)
            isPlaying.value = false
          })
        }
      }
    })
  }

  const handleVideoError = e => {
    if (isPlaying.value) {
      console.error("视频加载错误:", e)
      proxy.$modal.msgError("视频加载失败")
      isPlaying.value = false
    }
  }

  const handleVideoEnd = () => {
    isPlaying.value = false
    if (videoRef.value) {
      videoRef.value.style.zIndex = "auto"
    }
  }

  const handleFullscreenChange = () => {
    if (!document.fullscreenElement && !document.webkitFullscreenElement) {
      isPlaying.value = true
      const videoElement = videoRef.value
      if (videoElement) {
        videoElement.style.zIndex = "auto"
      }
    }
  }

  const submitForm = () => {
    proxy.$refs["formRef"].validate(valid => {
      if (valid) {
        if (form.value.approveResult === "0" && !form.value.approveOpinion) {
          proxy.$modal.msgError("审核不通过时必须填写审核意见")
          return
        }

        const submitData = {
          psaId: form.value.psaId,
          approveResult: form.value.approveResult,
          approveOpinion: form.value.approveOpinion
        }
        addApprove(submitData).then(response => {
          proxy.$modal.msgSuccess("审核成功")
          visible.value = false
          proxy.$emit("refresh")
        })
      }
    })
  }

  const cancel = () => {
    if (videoRef.value) {
      videoRef.value.pause()
    }
    isPlaying.value = false
    nextTick(() => {
      visible.value = false
      reset()
    })
  }

  const reset = () => {
    form.value = {
      id: undefined,
      title: undefined,
      cover: undefined,
      url: undefined,
      videoType: undefined,
      createBy: undefined,
      createTime: undefined,
      approveResult: undefined,
      approveOpinion: undefined
    }
    proxy.$refs["formRef"]?.resetFields()
  }

  onBeforeUnmount(() => {
    if (document.fullscreenElement || document.webkitFullscreenElement) {
      document.exitFullscreen?.() || document.webkitExitFullscreen?.()
    }
  })

  defineExpose({
    openDialog
  })
</script>

<style lang="scss" scoped>
  .video-container {
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #f5f7fa;
    border-radius: 4px;
    overflow: hidden;
    min-height: 180px;
    position: relative;
  }

  .image-wrapper {
    position: relative;
    cursor: pointer;
  }

  .play-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .play-icon .el-icon {
    font-size: 24px;
    color: white;
  }

  video {
    max-width: 100%;
    display: block;
    position: relative;
    z-index: auto;
  }

  video:fullscreen {
    z-index: auto !important;
  }
</style>
