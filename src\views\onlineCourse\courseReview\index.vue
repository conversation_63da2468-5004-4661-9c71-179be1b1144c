<!--
 * @Description: 课程审批-入口文件
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-09-06 14:23:19
 * @LastEditTime: 2023-09-06 14:25:21
-->
<template>
  <section class="index-container">
    <component
      :is="currentView"
      :row="row"
      @updateCurrentView="updateCurrentView"
    ></component>
  </section>
</template>

<script setup name="courseAddIndex">
  import courseReviewList from "./courseReviewList.vue"
  import courseReview from "./courseReview.vue"

  const row = ref({})
  const currentView = shallowRef(courseReviewList)

  const updateCurrentView = (view, item) => {
    row.value = item
    if (view === "courseReview") {
      currentView.value = courseReview
    } else {
      currentView.value = courseReviewList
    }
  }
</script>
