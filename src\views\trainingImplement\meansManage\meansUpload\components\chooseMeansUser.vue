<!--
 * @Description: 选择授权学院弹窗
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-03-13 15:37:36
 * @LastEditTime: 2023-09-05 16:18:37
-->
<template>
  <div class="app-container">
    <el-dialog
      v-model="dialogVisible"
      title="选择授权学员"
      width="50%"
      top="110px"
      @close="close"
      :key="new Date().getTime()"
      center
    >
      <el-form
        :model="queryParams"
        ref="queryRef"
        :inline="true"
        v-show="showSearch"
        label-width="68px"
      >
        <el-form-item label="用户名称" prop="userName">
          <el-input
            v-model="queryParams.userName"
            placeholder="请输入用户名称"
            clearable
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="手机号码" prop="phonenumber">
          <el-input
            v-model="queryParams.phonenumber"
            placeholder="请输入手机号码"
            clearable
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="创建时间">
          <el-date-picker
            v-model="dateRange"
            value-format="YYYY-MM-DD"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          ></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-table
        ref="choosePersonnelTableRef"
        v-loading="loading"
        :data="tableData"
        row-key="userId"
      >
        <el-table-column type="selection" width="55" reserve-selection />
        <el-table-column label="用户编号" key="userId" prop="userId" />
        <el-table-column label="用户名称" key="userName" prop="userName" />
        <el-table-column label="用户昵称" key="nickName" prop="nickName" />
        <el-table-column label="部门" key="deptName" prop="dept.deptName" />
        <el-table-column
          label="手机号码"
          key="phonenumber"
          prop="phonenumber"
          width="120"
        />
        <el-table-column label="创建时间" prop="createTime" width="160">
          <template #default="scope">
            <span>{{ parseTime(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        class="dialog-pagination"
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
        :autoScroll="false"
      />
      <template #footer>
        <el-button @click="close">取消</el-button>
        <el-button type="primary" @click="save">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="choosePersonnelDialog">
  import { listUser } from "@/api/system/user"

  const emit = defineEmits(["fetch-data"])
  const { proxy } = getCurrentInstance()

  const tableData = ref([])
  const loading = ref(true)
  const showSearch = ref(true)
  const dialogVisible = ref(false)
  const total = ref(0)
  const dateRange = ref([])

  const data = reactive({
    queryParams: {
      pageNum: 1,
      pageSize: 10
    },
    selectedRows: []
  })

  const { queryParams, selectedRows } = toRefs(data)

  //** 弹框打开事件 */
  const openDialog = async ids => {
    if (ids) {
      selectedRows.value = ids.split(",")
    }
    dialogVisible.value = true
    await getList()
    if (selectedRows.value) {
      nextTick(() => {
        tableData.value.forEach((item, index) => {
          if (selectedRows.value.find(v => v == item.userId)) {
            proxy.$refs["choosePersonnelTableRef"].toggleRowSelection(
              proxy.$refs["choosePersonnelTableRef"].data[index],
              true
            )
          }
        })
      })
    }
  }

  /** 查询目录列表 */
  const getList = async () => {
    loading.value = true
    const res = await listUser(
      proxy.addDateRange(queryParams.value, dateRange.value)
    )
    if (res.code === 200) {
      tableData.value = res.rows
      total.value = res.total
      loading.value = false
    }
  }
  /** 搜索按钮操作 */
  function handleQuery() {
    getList()
  }
  /** 重置按钮操作 */
  function resetQuery() {
    dateRange.value = []
    proxy.resetForm("queryRef")
    handleQuery()
  }
  // 关闭弹框并重置操作
  const close = () => {
    proxy.resetForm("queryRef")
    dialogVisible.value = false
  }
  const save = () => {
    const selectionRows =
      proxy.$refs["choosePersonnelTableRef"].getSelectionRows()
    emit("fetch-data", selectionRows)
    proxy.$modal.msgSuccess("操作成功")
    close()
  }

  defineExpose({
    openDialog
  })
</script>

<style lang="scss" scoped>
  :deep(.pagination-container .el-pagination) {
    position: static;
  }

  :deep(.el-input__wrapper) {
    width: auto !important;
  }
</style>
