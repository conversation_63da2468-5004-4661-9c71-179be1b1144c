/*
 * @Description: 游戏管理相关API
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2025-06-06 14:01:53
 * @LastEditTime: 2025-07-08 10:17:00
 */

import request from "@/utils/request"

// 查询游戏列表
export function listGame(query) {
  return request({
    url: "/system/game/list",
    method: "get",
    params: query
  })
}

// 查询游戏详细
export function getGame(id) {
  return request({
    url: "/system/game/" + id,
    method: "get"
  })
}

// 新增游戏
export function addGame(data) {
  return request({
    url: "/system/game",
    method: "post",
    data: data
  })
}

// 修改游戏
export function updateGame(data) {
  return request({
    url: "/system/game",
    method: "put",
    data: data
  })
}

// 删除游戏
export function delGame(id) {
  return request({
    url: "/system/game/" + id,
    method: "delete"
  })
}

// 批量设置人员
export function batchAssignPersonnelToGames(data) {
  return request({
    url: "/system/game/batchLink",
    method: "post",
    data: data
  })
}