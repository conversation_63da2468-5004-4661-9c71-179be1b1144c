<!--
 * @Description: 新增/修改游戏
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2025-06-06 13:59:33
 * @LastEditTime: 2025-07-16 14:02:01
-->
<template>
  <el-dialog
    v-model="visible"
    :gameName="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    center
  >
    <el-form
      ref="dataFormRef"
      :model="dataForm"
      :rules="dataFormRules"
      label-width="130px"
      @keyup.enter="submitHandle()"
    >
      <el-row :gutter="10">
        <el-col :span="24">
          <el-form-item label="游戏名称" prop="gameName">
            <el-input v-model="dataForm.gameName" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="游戏编号" prop="gameCode">
            <el-input v-model="dataForm.gameCode" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="游戏封面" prop="gameCover">
            <ImageUpload v-model="dataForm.gameCover" :limit="1" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="游戏类型" prop="gameType">
            <el-input v-model="dataForm.gameType" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="游戏链接URL" prop="gameLink">
            <el-input v-model="dataForm.gameLink" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="游戏介绍" prop="description">
            <el-input
              type="textarea"
              v-model="dataForm.description"
              :autosize="{ minRows: 3, maxRows: 6 }"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="人员分配" prop="linkUserNames" class="mb-5">
            <div class="mt-2 flex flex-wrap gap-2">
              <div
                v-for="(person, index) in displayPersonnelList"
                :key="person.userId"
                class="bg-blue-50 h-9 rounded-3xl flex items-center pr-2 relative border border-blue-200"
              >
                <el-avatar
                  class="!m-1"
                  :size="28"
                  :style="{ backgroundColor: '#409eff' }"
                >
                  {{ person.userName.substring(0, 1) }}
                </el-avatar>
                <span class="mx-2 text-sm">{{ person.userName }}</span>
                <el-icon
                  class="ml-2 cursor-pointer hover:text-red-500"
                  @click="handleRemovePerson(index)"
                >
                  <Close />
                </el-icon>
              </div>
              
              <!-- 展开/收起按钮 -->
              <div
                v-if="selectedPersonnelList.length > 20"
                class="bg-gray-100 h-9 rounded-3xl flex items-center px-3 border border-gray-200 cursor-pointer hover:bg-gray-200"
                @click="toggleExpanded"
              >
                <span class="text-sm text-gray-600">
                  {{ isExpanded ? '收起' : `还有${selectedPersonnelList.length - 20}人...` }}
                </span>
                <el-icon class="ml-1 text-gray-600">
                  <component :is="isExpanded ? 'ArrowUp' : 'ArrowDown'" />
                </el-icon>
              </div>
              
              <el-button type="primary" link @click="choosePersonnel">
                <el-icon><Plus /></el-icon> 选择人员
              </el-button>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="状态" prop="status">
            <el-radio-group v-model="dataForm.status">
              <el-radio :label="0">下架</el-radio>
              <el-radio :label="1">上架</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="submitHandle()">确定</el-button>
    </template>
  </el-dialog>

  <!-- 人员选择弹窗 -->
  <ChoosePersonnelOrDeptDialog
    ref="choosePersonnelOrDeptDialogRef"
    @fetch-data="choosePersonnelDone"
  />
</template>

<script setup>
  import { getGame, addGame, updateGame } from "@/api/game/index.js"
  import { isUrl } from "@/utils/is"
  import { Close, Plus, ArrowUp, ArrowDown } from "@element-plus/icons-vue"

  const emit = defineEmits(["refreshDataList"])

  const { proxy } = getCurrentInstance()

  const visible = ref(false)
  const dataFormRef = ref()
  const dataForm = ref({})
  const choosePersonnelOrDeptDialogRef = ref()

  // 人员分配相关
  const selectedPersonnelList = ref([])
  const isExpanded = ref(false)

  // 显示的人员列表（支持展开/收起）
  const displayPersonnelList = computed(() => {
    if (selectedPersonnelList.value.length <= 20 || isExpanded.value) {
      return selectedPersonnelList.value
    }
    return selectedPersonnelList.value.slice(0, 20)
  })

  // 切换展开/收起状态
  const toggleExpanded = () => {
    isExpanded.value = !isExpanded.value
  }

  // 选择人员
  const choosePersonnel = () => {
    const currentIds = selectedPersonnelList.value
      .map(person => person.userId)
      .join(",")
    const currentNames = selectedPersonnelList.value
      .map(person => person.userName)
      .join(",")
    choosePersonnelOrDeptDialogRef.value.openDialog(
      currentIds,
      currentNames,
      null,
      "personnel"
    )
  }

  // 人员选择完成
  const choosePersonnelDone = (userList, fieldType) => {
    if (fieldType === "personnel") {
      selectedPersonnelList.value = userList.map(user => ({
        userId: user.userId,
        userName: user.userName
      }))
      dataForm.value.linkUserIds = userList.map(user => user.userId).join()
      dataForm.value.linkUserNames = userList.map(user => user.userName).join()
    }
  }

  // 删除选中的人员
  const handleRemovePerson = (index) => {
    // 计算实际索引（考虑展开/收起状态）
    const actualIndex = isExpanded.value || selectedPersonnelList.value.length <= 20 
      ? index 
      : index < 20 ? index : -1
    
    if (actualIndex >= 0 && actualIndex < selectedPersonnelList.value.length) {
      selectedPersonnelList.value.splice(actualIndex, 1)
      // 更新dataForm中的数据
      dataForm.value.linkUserIds = selectedPersonnelList.value
        .map(user => user.userId)
        .join()
      dataForm.value.linkUserNames = selectedPersonnelList.value
        .map(user => user.userName)
        .join()
    }
  }

  // 打开弹窗事件
  const openDialog = async id => {
    // 重置表单数据
    dataForm.value = {}
    selectedPersonnelList.value = []
    isExpanded.value = false

    if (id) {
      const { data } = await getGame(id)
      Object.assign(dataForm.value, data)
      
      // 处理人员分配的回显
      if (data.linkUserIds && data.linkUserNames) {
        // 兼容字符串和数组两种格式
        const userIds = Array.isArray(data.linkUserIds) 
          ? data.linkUserIds 
          : data.linkUserIds.split(",")
        const userNames = Array.isArray(data.linkUserNames) 
          ? data.linkUserNames 
          : data.linkUserNames.split(",")
        
        if (userIds.length === userNames.length && userIds.length > 0) {
          selectedPersonnelList.value = userIds.map((id, index) => ({
            userId: String(id).trim(), // 确保是字符串并去除空格
            userName: String(userNames[index]).trim()
          })).filter(item => item.userId && item.userName) // 过滤无效数据
        }
      }
    }
    visible.value = true
  }

  const validateUrl = (rule, value, callback) => {
    if (!isUrl(value)) {
      callback(new Error("请输入正确的URL"))
    } else {
      callback()
    }
  }

  const dataFormRules = ref({
    gameCover: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
    gameName: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
    gameCode: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
    gameLink: [
      { required: true, message: "必填项不能为空", trigger: "blur" },
      { validator: validateUrl, trigger: "blur" }
    ],
    status: [{ required: true, message: "必填项不能为空", trigger: "blur" }]
  })
  
  // 表单提交
  const submitHandle = async () => {
    dataFormRef.value.validate(async valid => {
      if (!valid) {
        return false
      }
      if (!dataForm.value.id) {
        await addGame(dataForm.value)
        proxy.$modal.msgSuccess("新增成功")
        visible.value = false
        emit("refreshDataList")
      } else {
        await updateGame(dataForm.value)
        proxy.$modal.msgSuccess("修改成功")
        visible.value = false
        emit("refreshDataList")
      }
    })
  }

  defineExpose({
    openDialog
  })
</script>
