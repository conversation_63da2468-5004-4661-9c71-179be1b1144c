<!--
 * @Description: 
 * @Author: sunyun<PERSON>
 * @LastEditors: sunyunwu
 * @Date: 2023-09-08 15:40:09
 * @LastEditTime: 2023-09-27 10:18:33
-->
<template>
  <div class="target-view">
    <div>
      <span class="title">学习情况分布</span>

      <el-date-picker
        style="width: 260px; float: right; margin-right: 10px"
        v-model="dateRange"
        value-format="YYYY-MM-DD"
        type="daterange"
        range-separator="-"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        @change="selectDate"
      ></el-date-picker>
    </div>
    <div class="view-con">
      <Echarts :option="option" />
    </div>
  </div>
</template>

<script setup>
  import { ref } from "vue"
  import { graphic } from "echarts"
  import { unepLoginData } from "@/api/home/<USER>/index"

  let date = new Date()
  let year = date.getFullYear()
  let month =
    date.getMonth() + 1 < 10 ? "0" + (date.getMonth() + 1) : date.getMonth() + 1
  let day = date.getDate() < 10 ? "0" + date.getDate() : date.getDate()
  let startDate = year + "-" + month + "-" + "01"
  let endDate = year + "-" + month + "-" + day
  let dateRange = ref([startDate, endDate])

  const selectDate = () => {
    getData()
  }

  // 查询学习情况
  const getData = () => {
    unepLoginData({
      queryTimeFrom: dateRange.value[0],
      queryTimeTo: dateRange.value[1]
    }).then(res => {
      setOption(res.data)
    })
  }

  getData()

  const option = ref(null)

  const setOption = data => {
    let xarr = []
    let yarr = []
    for (let item of data) {
      yarr.push(item.loginCount)
      xarr.push(item.study_time.slice(5,10))
    }

    let rotate = 60

    option.value = {
      xAxis: {
        type: "category",
        data: xarr,
        axisLabel: {
          interval: 0,
          rotate
        },
        boundaryGap: false
      },
      tooltip: {
        trigger: "axis",
        axisPointer: {
          type: "cross",
          label: {
            backgroundColor: "#6a7985"
          }
        }
      },
      yAxis: {
        type: "value",
        name:"人数(个)"
      },
      grid: {
        left: "20",
        top: "30",
        bottom: "0",
        right: "20",
        containLabel: true
      },
      color: ["rgb(1, 191, 236)"],
      series: [
        {
          data: yarr,
          type: "line",
          smooth: true,
          showSymbol: false,
          areaStyle: {
            opacity: 0.8,
            color: new graphic.LinearGradient(0, 0, 1, 0, [
              {
                offset: 0,
                color: "rgb(128, 255, 165)"
              },
              {
                offset: 1,
                color: "rgb(1, 191, 236)"
              }
            ])
          }
        }
      ]
    }
  }
</script>

<style scoped lang="scss">
  .title {
    line-height: 40px;
    font-weight: bold;
    margin-left: 10px;
  }
  .view-con {
    background: #fff;
    padding: 14px 20px;
    border-radius: 10px;
    height: 500px;
  }
</style>
