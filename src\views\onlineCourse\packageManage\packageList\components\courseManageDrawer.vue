<!--
 * @Description: 课程套餐-课程管理
 * @Author: <PERSON><PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-06-28 16:12:57
 * @LastEditTime: 2024-02-07 10:07:12
-->
<template>
  <div class="app-container">
    <el-drawer
      v-model="itemsDataVisible"
      :title="itemsDataTitle"
      :size="1100"
      :close-on-press-escape="false"
      :close-on-click-modal="true"
    >
      <el-form :model="queryParams" ref="queryRef" :inline="true">
        <el-form-item label="课程名称" prop="courseName">
          <el-input
            v-model="queryParams.courseName"
            placeholder="请输入课程名称"
            clearable
            style="width: 240px"
            @keyup.enter="getDetailList"
          />
        </el-form-item>
        <el-form-item label="课程编号" prop="courseCode">
          <el-input
            v-model="queryParams.courseCode"
            placeholder="请输入课程编号"
            clearable
            style="width: 240px"
            @keyup.enter="getDetailList"
          />
        </el-form-item>
        <el-form-item label="课程目录" prop="catalogueId">
          <el-tree-select
            v-model="queryParams.catalogueId"
            :data="catalogueOptions"
            :props="{
              value: 'catalogueId',
              label: 'catalogueName',
              children: 'children',
              disabled: 'disabled'
            }"
            clearable
            value-key="catalogueId"
            placeholder="选择课程目录"
            check-strictly
            default-expand-all
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="getDetailList"
            >搜索</el-button
          >
          <el-button icon="Refresh" @click="reset">重置</el-button>
        </el-form-item>
      </el-form>
      <el-row :gutter="10" class="mb-8px">
        <el-col :span="1.5">
          <el-button type="primary" plain icon="Plus" @click="chooseCourse"
            >添加课程</el-button
          >
        </el-col>
      </el-row>
      <el-table v-loading="loading" :data="tableData">
        <el-table-column label="课程封面" prop="courseImage">
          <template #default="scope">
            <el-popover placement="right" :width="400" trigger="hover">
              <img :src="scope.row.courseImage" width="375" height="375" />
              <template #reference>
                <img
                  :src="scope.row.courseImage"
                  style="max-height: 60px; max-width: 60px"
                />
              </template>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column label="课程名称" prop="courseName" />
        <el-table-column label="所属目录" prop="catalogueName" />
        <el-table-column label="课程编号" prop="courseCode" />
        <el-table-column label="课程类型" prop="courseType">
          <template #default="scope">
            <dict-tag
              :options="pedu_course_type"
              :value="scope.row.courseType"
            />
          </template>
        </el-table-column>

        <el-table-column
          label="操作"
          width="200"
          class-name="small-padding fixed-width"
        >
          <template #default="scope">
            <el-button
              link
              type="primary"
              icon="Delete"
              @click="handleDelete(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getDetailList"
      />
    </el-drawer>

    <!-- 课程选择弹窗 -->
    <ChooseCourseDialog
      selectionType="multiple"
      ref="chooseCourseDialogRef"
      @fetch-data="chooseCourseDone"
    />
  </div>
</template>

<script setup name="courseManageDrawer">
  import { listDetail, batchInsert, delDetail } from "@/api/system/package"
  import { catalogueList } from "@/api/system/catalogue.js"
  import catalogue from "@/utils/catalogue.js"
  const { proxy } = getCurrentInstance()
  const { pedu_course_type } = proxy.useDict("pedu_course_type")

  const loading = ref(false)
  const itemsDataVisible = ref(false)
  const itemsDataTitle = ref()

  let total = ref(0)
  const tableData = ref([])
  const catalogueOptions = ref([])
  const passageInfo = ref({})
  const data = reactive({
    queryParams: {
      pageNum: 1,
      pageSize: 10
    }
  })
  const { queryParams } = toRefs(data)

  const openDialog = async row => {
    passageInfo.value = row
    itemsDataTitle.value = "课程管理 【 " + row.name + " 】"
    await getTreeselect()
    await getDetailList()
    fetchAllCourseData()
    itemsDataVisible.value = true
  }

  const getDetailList = async () => {
    const res = await listDetail({
      ...queryParams.value,
      pakageId: passageInfo.value.id
    })
    tableData.value = res.rows
    total.value = res.total
  }

  /** 表单重置 */
  function reset() {
    proxy.resetForm("queryRef")
    getDetailList()
  }

  let chooseCourseDialogRef = ref()
  // 选择课程
  const chooseCourse = () => {
    const ids = allCourseData.value.map(item => item.courseId).join()
    chooseCourseDialogRef.value.openDialog(ids)
  }

  const chooseCourseDone = async row => {
    tableData.value = row
    let requestData = {
      coursePackageDetailList: tableData.value,
      id: passageInfo.value.id
    }
    await batchInsert(requestData)
    await getDetailList()
    await fetchAllCourseData()
  }
  /** 查询目录下拉树结构 */
  function getTreeselect() {
    catalogueOptions.value = []
    catalogueList({ catalogueType: catalogue.COURSE_CATALOGUE }).then(
      response => {
        const courseCatalogue = {
          catalogueId: 0,
          catalogueName: "主类目",
          children: [],
          disabled: true
        }
        courseCatalogue.children = proxy.handleTree(
          response.rows,
          "catalogueId"
        )
        catalogueOptions.value.push(courseCatalogue)
      }
    )
  }

  const allCourseData = ref([])
  const fetchAllCourseData = async () => {
    const res = await listDetail({
      pageNum: 1,
      pageSize: 999,
      pakageId: passageInfo.value.id
    })
    allCourseData.value = res.rows
  }

  /** 删除按钮操作 */
  function handleDelete(row) {
    proxy.$modal
      .confirm('是否确认删除课程名称为"' + row.courseName + '"的课程?')
      .then(function () {
        return delDetail(row.id)
      })
      .then(() => {
        getDetailList()
        fetchAllCourseData()
        proxy.$modal.msgSuccess("删除成功")
      })
  }

  defineExpose({
    openDialog
  })
</script>
