<!--
 * @Description: 资料列表
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-03-08 11:28:06
 * @LastEditTime: 2024-11-15 09:04:58
-->
<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
    >
      <el-form-item label="资料名称" prop="manageName">
        <el-input
          v-model="queryParams.manageName"
          placeholder="资料名称"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="资料目录" prop="catalogueId">
        <el-tree-select
          v-model="queryParams.catalogueId"
          :data="catalogueOptions"
          :props="{
            value: 'catalogueId',
            label: 'catalogueName',
            children: 'children',
            disabled: 'disabled'
          }"
          clearable
          value-key="catalogueId"
          placeholder="选择资料目录"
          check-strictly
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb-8px">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd">
          新增
        </el-button>
      </el-col>
      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="tableData">
      <el-table-column label="资料封面" prop="cover">
        <template #default="scope">
          <el-popover
            v-if="scope.row.cover"
            placement="right"
            width="400"
            trigger="hover"
          >
            <template #reference>
              <el-image
                style="width: 80px; height: 80px"
                :src="scope.row.cover"
              ></el-image>
            </template>
            <el-image :src="scope.row.cover"></el-image>
          </el-popover>
          <div v-else>--</div>
        </template>
      </el-table-column>
      <el-table-column label="资料名称" prop="manageName" />
      <el-table-column label="资料类型" prop="manageType">
        <template #default="scope">
          <dict-tag :options="sys_flie_type" :value="scope.row.manageType" />
        </template>
      </el-table-column>
      <el-table-column label="所属目录" prop="catalogueName" />
      <el-table-column label="创建人" prop="createBy" />
      <el-table-column label="创建时间" prop="createTime" />
      <el-table-column
        label="操作"
        width="200"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-button
            link
            type="primary"
            icon="Edit"
            @click="handleUpdate(scope.row)"
            >修改</el-button
          >
          <el-button
            link
            type="primary"
            icon="Delete"
            @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script setup name="meansList">
  import { delMeans, listMeans } from "@/api/trainingImplement/means.js"
  import { catalogueList } from "@/api/system/catalogue.js"
  import catalogue from "@/utils/catalogue.js"

  const { proxy } = getCurrentInstance()
  const { sys_flie_type } = proxy.useDict("sys_flie_type")

  const tableData = ref([])
  const loading = ref(true)
  const showSearch = ref(true)
  const total = ref(0)
  const catalogueOptions = ref([])

  const data = reactive({
    queryParams: {
      pageNum: 1,
      pageSize: 10
    }
  })

  const { queryParams } = toRefs(data)

  /** 查询目录列表 */
  function getList() {
    loading.value = true
    listMeans(queryParams.value).then(response => {
      tableData.value = response.rows || []
      total.value = response.total
      loading.value = false
    })
  }
  /** 查询目录下拉树结构 */
  function getTreeselect() {
    catalogueOptions.value = []
    catalogueList({ catalogueType: catalogue.MEANS_CATALOGUE }).then(
      response => {
        const meansCatalogue = {
          catalogueId: 0,
          catalogueName: "主类目",
          children: [],
          disabled: true
        }
        meansCatalogue.children = proxy.handleTree(response.rows, "catalogueId")
        catalogueOptions.value.push(meansCatalogue)
      }
    )
  }
  /** 搜索按钮操作 */
  function handleQuery() {
    getList()
  }
  /** 重置按钮操作 */
  function resetQuery() {
    proxy.resetForm("queryRef")
    handleQuery()
  }
  const handleAdd = () => {
    proxy.$tab.closeOpenPage({
      path: "/trainingImplement/meansUpload",
      query: {
        flag: "add"
      }
    })
  }
  /** 修改按钮操作 */
  function handleUpdate(row) {
    proxy.$tab.closeOpenPage({
      path: "/trainingImplement/meansUpload",
      query: {
        ...row
      }
    })
  }
  /** 删除按钮操作 */
  function handleDelete(row) {
    proxy.$modal
      .confirm('是否确认删除名称为"' + row.manageName + '"的数据项?')
      .then(function () {
        return delMeans(row.manageId)
      })
      .then(() => {
        getList()
        proxy.$modal.msgSuccess("删除成功")
      })
      .catch(() => {})
  }

  getList()
  getTreeselect()
</script>
<style lang="scss" scoped></style>
