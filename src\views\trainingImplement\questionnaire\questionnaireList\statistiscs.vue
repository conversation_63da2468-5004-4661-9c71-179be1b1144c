<!--
 * @Description: 
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-12-06 11:56:10
 * @LastEditTime: 2024-12-06 13:27:23
-->
<template>
  <div class="app-container">
    <div style="margin-bottom: 10px">
      <el-button type="primary" plain icon="ArrowLeftBold" @click="backMonitor">
        返回
      </el-button>
      <el-divider direction="vertical"></el-divider>
      问卷：{{ row.surveyTitle }}
    </div>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport">
          导出
        </el-button>
      </el-col>
    </el-row>

    <el-table :data="tableData" :border="false">
      <el-table-column label="用户名称" prop="userName" />
      <el-table-column label="部门名称" prop="deptName" />
      <el-table-column label="总分数" prop="totalScore" />
      <el-table-column label="提交时间" prop="submitTime" />
      <el-table-column label="问题1得分" prop="question1Score" />
      <el-table-column label="问题2得分" prop="question2Score" />
      <el-table-column label="问题3得分" prop="question3Score" />
      <el-table-column label="问题4得分" prop="question4Score" />
      <el-table-column label="问题5得分" prop="question5Score" />
      <el-table-column label="问题6得分" prop="question6Score" />
      <el-table-column label="问题7得分" prop="question7Score" />
      <el-table-column label="问题8得分" prop="question8Score" />
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script setup name="statistiscs">
  import dayjs from "dayjs"
  import { questionnaireStatistics } from "@/api/trainingImplement/questionnaire.js"

  const { proxy } = getCurrentInstance()
  const emit = defineEmits(["updateCurrentView"])
  const props = defineProps({
    row: {
      type: Object,
      default: () => ({})
    }
  })

  const tableData = ref([])
  const loading = ref(true)
  const total = ref(0)
  const queryParams = ref({
    pageNum: 1,
    pageSize: 10
  })

  /** 查询目录列表 */
  async function getList() {
    loading.value = true
    const response = await questionnaireStatistics(queryParams.value)
    tableData.value = response.rows || []
    total.value = response.total
    loading.value = false
  }

  const backMonitor = () => {
    emit("updateCurrentView", "questionnaireList")
  }
  const handleExport = () => {
    proxy.download(
      "course/questionnaire/user-export",
      {},
      `问卷统计_${dayjs().format("YYYY-MM-DD")}.xlsx`
    )
  }

  onMounted(() => {
    getList()
  })
</script>
