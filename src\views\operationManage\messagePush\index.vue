<!--
 * @Description: 消息推送
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-07-04 16:02:34
 * @LastEditTime: 2023-10-19 09:34:40
-->
<template>
  <div class="app-container">
    <el-tabs v-model="activeTab" class="demo-tabs" @tab-change="changeTab">
      <el-tab-pane label="邮箱" name="1"></el-tab-pane>
      <el-tab-pane label="短信" name="2"></el-tab-pane>
    </el-tabs>

    <el-row class="data-lists" :gutter="50">
      <el-col :span="12">
        <div class="setting-drawer-title">
          <h3 class="drawer-title">通知内容</h3>
        </div>
        <!-- 邮件通知内容 -->
        <el-form
          v-if="activeTab === '1'"
          :model="emailForm"
          ref="emailFormRef"
          :rules="emailRules"
          label-width="100px"
        >
          <el-row>
            <el-col :span="24">
              <el-form-item label="主题" prop="taskName">
                <el-input
                  v-model="emailForm.taskName"
                  placeholder="请输入主题"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="正文" prop="trainingContent">
                <el-input
                  v-model="emailForm.trainingContent"
                  placeholder="请输入正文"
                  clearable
                  :rows="4"
                  type="textarea"
                />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="发件人">
                <el-input v-model="defaultEmail" clearable disabled />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <!-- 短信通知内容 -->
        <el-form
          v-if="activeTab === '2'"
          :model="smsForm"
          ref="smsFormRef"
          :rules="smsRules"
          label-width="100px"
        >
          <el-row>
            <el-col :span="24">
              <el-form-item label="短信内容" prop="message">
                <el-input
                  v-model="smsForm.message"
                  placeholder="请输入正文"
                  clearable
                  :rows="4"
                  type="textarea"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-col>
      <el-col :span="12">
        <div class="setting-drawer-title">
          <h3 class="drawer-title">人员名单</h3>
        </div>

        <el-row :gutter="10" class="mb-8px">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="choosePersonnel"
              >添加人员</el-button
            >
          </el-col>
        </el-row>

        <el-table :data="userList">
          <el-table-column label="用户名称" prop="userName" />
          <el-table-column label="用户昵称" prop="nickName" />
          <el-table-column label="部门" prop="dept.deptName" />
          <el-table-column v-if="activeTab === '1'" label="邮箱" prop="email" />
          <el-table-column
            v-if="activeTab === '2'"
            label="手机号码"
            prop="phonenumber"
          />
        </el-table>
      </el-col>
    </el-row>

    <div class="msgpush-footer">
      <el-button type="primary" @click="submitForm">确认推送</el-button>
    </div>
    <!-- 人员选择弹窗 -->
    <ChoosePersonnelDialog
      ref="choosePersonnelDialogRef"
      @fetch-data="choosePersonnelDone"
      :emailRequired="activeTab === '1'"
      :phoneRequired="activeTab === '2'"
    />
  </div>
</template>

<script setup name="messagePush">
  import { sendEmail, sendSms } from "@/api/opertaionManage/msgPush.js"

  const { proxy } = getCurrentInstance()

  const defaultEmail = ref("上海柏科咨询")
  const activeTab = ref("1")
  const emailForm = ref({})
  const emailRules = ref({
    taskName: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
    trainingContent: [
      { required: true, message: "必填项不能为空", trigger: "blur" }
    ]
  })
  const smsForm = ref({})
  const smsRules = ref({
    message: [{ required: true, message: "必填项不能为空", trigger: "blur" }]
  })
  const userList = ref([])

  const submitForm = () => {
    let submitData = {}
    if (activeTab.value === "1") {
      proxy.$refs["emailFormRef"].validate(async valid => {
        if (valid) {
          let userEmailArr = userList.value.map(item => item.email)
          submitData = {
            ...emailForm.value,
            toAccount: userEmailArr,
            ccAccount: userEmailArr[0]
          }
          const res = await sendEmail(submitData)
          if (res.code === 200) {
            proxy.$modal.msgSuccess("操作成功")
            reset()
          }
        }
      })
    } else if (activeTab.value === "2") {
      proxy.$refs["smsFormRef"].validate(async valid => {
        if (valid) {
          let phoneNum = userList.value
            .map(item => {
              return "+86" + item.phonenumber
            })
            .join()
          submitData = {
            ...smsForm.value,
            phoneNum
          }
          const res = await sendSms(submitData)
          if (res.code === 200) {
            proxy.$modal.msgSuccess("操作成功")
            reset()
          }
        }
      })
    }
  }

  const changeTab = () => {
    reset()
  }

  const reset = () => {
    proxy.resetForm("emailFormRef")
    proxy.resetForm("smsFormRef")
    userList.value = []
  }

  const choosePersonnelDialogRef = ref()
  const choosePersonnel = () => {
    choosePersonnelDialogRef.value.openDialog(
      userList.value.map(item => item.userId)
    )
  }
  const choosePersonnelDone = item => {
    userList.value = item
  }
</script>
<style lang="scss" scoped>
  .setting-drawer-title {
    margin-bottom: 12px;
    color: rgba(0, 0, 0, 0.85);
    line-height: 20px;
    .drawer-title {
      font-weight: bolder;
      font-size: 17px;
    }
  }
  .msgpush-footer {
    position: fixed;
    bottom: 30px;
    left: 50%;
  }
</style>
