<!--
 * @Description: 按部门选择人员
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-11-15 15:59:06
 * @LastEditTime: 2025-06-26 10:13:46
-->

<template>
  <div class="dept-tab-container">
    <div class="action-bar">
      <el-button
        type="primary"
        plain
        icon="Plus"
        @click="handleAdd"
        :disabled="selectionList.length === 0"
        size="small"
      >
        添加选中部门人员
      </el-button>
    </div>

    <div class="content-area">
      <!--左侧部门树-->
      <div class="tree-section">
        <div class="tree-header">
          <el-input
            v-model="deptName"
            placeholder="请输入部门名称"
            clearable
            prefix-icon="Search"
            size="small"
          />
        </div>
        <div class="tree-content">
          <el-scrollbar height="100%">
            <el-tree
              :data="deptOptions"
              :props="{
                label: 'label',
                children: 'children'
              }"
              :expand-on-click-node="false"
              :filter-node-method="filterNode"
              ref="deptTreeRef"
              node-key="id"
              default-expand-all
              highlight-current
              @node-click="handleNodeClick"
            />
          </el-scrollbar>
        </div>
      </div>

      <!--右侧用户数据-->
      <div class="table-section">
        <div class="search-form">
          <el-form
            :model="queryParams"
            ref="queryRef"
            :inline="true"
            label-width="68px"
          >
            <el-form-item label="账号" prop="userName"
            label-width="48px">
              <el-input
                v-model="queryParams.userName"
                placeholder="请输入用户名称"
                clearable
                size="small"
                @keyup.enter="getList"
              />
            </el-form-item>
            <el-form-item label="手机号码" prop="phonenumber">
              <el-input
                v-model="queryParams.phonenumber"
                placeholder="请输入手机号码"
                clearable
                size="small"
                @keyup.enter="getList"
              />
            </el-form-item>
            <el-form-item>
              <el-button
                type="primary"
                icon="Search"
                @click="getList"
                size="small"
                >搜索</el-button
              >
              <el-button icon="Refresh" @click="resetQuery" size="small"
                >重置</el-button
              >
            </el-form-item>
          </el-form>
        </div>

        <div class="table-container">
          <el-table
            ref="userListTableRef"
            :data="userList"
            @selection-change="handleSelectionChange"
            height="100%"
            size="small"
          >
            <el-table-column
              type="selection"
              width="50"
              :selectable="selectable"
            />
            <el-table-column
              label="账号"
              key="userName"
              prop="userName"
              width="80"
            />
            <el-table-column
              label="姓名"
              key="nickName"
              prop="nickName"
              width="80"
            />
            <el-table-column label="部门" key="deptName" prop="deptName" />
            <el-table-column
              label="手机号码"
              key="phonenumber"
              prop="phonenumber"
              width="120"
            />
            <!-- <el-table-column label="创建时间" prop="createTime" width="160">
              <template #default="scope">
                <span>{{ parseTime(scope.row.createTime) }}</span>
              </template>
            </el-table-column> -->
          </el-table>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup name="chooseDeptTab">
  import { listUser } from "@/api/system/user"

  const { proxy } = getCurrentInstance()
  const userList = ref([])
  const total = ref(0)
  const dateRange = ref([])
  const deptName = ref("")

  const emit = defineEmits(["selection-change"])

  const data = reactive({
    queryParams: {
      pageNum: 1,
      pageSize: 999,
      userName: undefined,
      phonenumber: undefined,
      status: 0,
      deptId: undefined,
      onlyCurrentDept: true
    }
  })

  const { queryParams } = toRefs(data)

  const props = defineProps({
    deptOptions: {
      type: Array,
      default: () => []
    },
    selectedIds: {
      type: Array,
      default: () => []
    },
    selectedLabels: {
      type: Array,
      default: () => []
    },
    selectedDepts: {
      type: Array,
      default: () => []
    },
    needDeptId: {
      type: Boolean,
      default: false
    },
    selectedPersonnelList: {
      type: Array,
      default: () => []
    }
  })

  /** 通过条件过滤节点  */
  const filterNode = (value, data) => {
    if (!value) return true
    return data.label.indexOf(value) !== -1
  }
  /** 根据名称筛选部门树 */
  watch(deptName, val => {
    proxy.$refs["deptTreeRef"].filter(val)
  })
  /** 查询用户列表 */
  function getList() {
    listUser(proxy.addDateRange(queryParams.value, dateRange.value)).then(
      res => {
        userList.value = res.rows
        total.value = res.total
      }
    )
  }
  /** 节点单击事件 */
  function handleNodeClick(data) {
    if (data.id === 100 || data.parentId === 100) return
    queryParams.value.deptId = data.id
    getList()
  }
  /** 重置按钮操作 */
  function resetQuery() {
    dateRange.value = []
    proxy.resetForm("queryRef")
    getList()
  }

  const selectionList = ref([])
  /** 选择条数  */
  function handleSelectionChange(selection) {
    selectionList.value = selection
  }

  const handleAdd = () => {
    emit("selection-change", selectionList.value)
    proxy.$refs["userListTableRef"].clearSelection()
  }
  const selectable = row => {
    if (
      !props.selectedPersonnelList ||
      props.selectedPersonnelList.length === 0
    )
      return true
    if (props.selectedPersonnelList.includes(row.userId)) {
      return false // 禁用
    } else {
      return true // 可选
    }
  }
</script>

<style lang="scss" scoped>
  .dept-tab-container {
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .action-bar {
    flex-shrink: 0;
    padding: 12px 0;
  }

  .content-area {
    flex: 1;
    display: flex;
    gap: 16px;
    overflow: hidden;
  }

  .tree-section {
    width: 280px;
    flex-shrink: 0;
    border: 1px solid #e4e7ed;
    border-radius: 6px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  .tree-header {
    padding: 12px;
    border-bottom: 1px solid #e4e7ed;
    background: #fafbfc;
  }

  .tree-content {
    flex: 1;
    overflow: hidden;
    padding: 8px;
  }

  .table-section {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  .search-form {
    flex-shrink: 0;
    padding: 12px;
    background: #fafbfc;

    :deep(.el-form-item) {
      margin-bottom: 8px;
    }

    :deep(.el-input) {
      width: 160px;
    }

    :deep(.el-date-editor) {
      width: 240px;
    }
  }

  .table-container {
    flex: 1;
    overflow: hidden;
  }
</style>
