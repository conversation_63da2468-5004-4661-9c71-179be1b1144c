<!--
 * @Description: 新增/修改短视频
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-04-10 17:21:57
 * @LastEditTime: 2023-05-31 10:17:22
-->
<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    :close-on-click-modal="false"
    center
  >
    <el-form
      ref="dataFormRef"
      :model="dataForm"
      :rules="dataFormRules"
      label-width="130px"
      @keyup.enter="submitHandle()"
    >
      <el-form-item label="企业名称" prop="enterpriseName">
        <el-input
          placeholder="请输入企业名称"
          v-model="dataForm.enterpriseName"
        />
      </el-form-item>
      <el-form-item label="联系人" prop="contactName">
        <el-input placeholder="请输入联系人" v-model="dataForm.contactName" />
      </el-form-item>
      <el-form-item label="职务" prop="position">
        <el-input placeholder="请输入职务" v-model="dataForm.position" />
      </el-form-item>
      <el-form-item label="电话" prop="phone">
        <el-input placeholder="请输入电话" v-model="dataForm.phone" />
      </el-form-item>
      <el-form-item label="地址" prop="address">
        <el-input placeholder="请输入地址" v-model="dataForm.address" />
      </el-form-item>
      <el-form-item label="企业简介" prop="enterpriseIntro">
        <el-input
          type="textarea"
          :rows="4"
          placeholder="请输入企业简介"
          v-model="dataForm.enterpriseIntro"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status" v-if="formType === 'update'">
        <el-radio-group v-model="dataForm.status">
          <el-radio label="0">待审核</el-radio>
          <el-radio label="1">通过</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="submitHandle()">确定</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
  import {
    getEnterprise,
    addEnterprise,
    updateEnterprise
  } from "@/api/enterpriseInfo/index.js"

  const emit = defineEmits(["refreshDataList"])

  const { proxy } = getCurrentInstance()

  const visible = ref(false)
  const dataFormRef = ref()
  let dataForm = ref({})
  const dialogTitle = ref("") // 弹窗的标题
  const formType = ref("") // 表单的类型：create - 新增；update - 修改

  // 打开弹窗事件
  const openDialog = async (type, id) => {
    // 重置表单数据
    dataForm.value = {}
    dialogTitle.value = type === "create" ? "新增" : "修改"
    formType.value = type

    if (id) {
      const { data } = await getEnterprise(id)
      Object.assign(dataForm.value, data)
    }
    visible.value = true
  }

  const dataFormRules = ref({
    enterpriseName: [
      { required: true, message: "必填项不能为空", trigger: "blur" }
    ],
    contactName: [
      { required: true, message: "必填项不能为空", trigger: "blur" }
    ],
    // position: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
    phone: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
    address: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
    enterpriseIntro: [
      { required: true, message: "必填项不能为空", trigger: "blur" }
    ]
  })

  // 表单提交
  const submitHandle = () => {
    dataFormRef.value.validate(valid => {
      if (!valid) {
        return false
      }
      if (formType.value === "create") {
        addEnterprise(dataForm.value).then(res => {
          if (res.code === 200) {
            proxy.$modal.msgSuccess("新增成功")
            visible.value = false
            emit("refreshDataList")
          }
        })
      } else {
        updateEnterprise(dataForm.value).then(res => {
          if (res.code === 200) {
            proxy.$modal.msgSuccess("修改成功")
            visible.value = false
            emit("refreshDataList")
          }
        })
      }
    })
  }

  defineExpose({
    openDialog
  })
</script>
