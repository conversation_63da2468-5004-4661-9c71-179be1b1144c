<!--
 * @Description: 设置问答弹窗
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-07-08 14:09:51
 * @LastEditTime: 2024-11-19 16:47:41
-->

<template>
  <el-dialog
    v-model="visible"
    title="设置问答"
    width="1500"
    center
    @close="dialogClose"
  >
    <div class="container">
      <video
        v-if="!isWordCourse"
        ref="videoRef"
        controlslist="nodownload"
        width="700"
        height="500"
        controls
        :src="dataForm.fileUrl"
      ></video>
      <iframe
        v-else
        ref="dynamicIframe"
        frameborder="0"
        :src="wordFileUrl"
        style="width: 700px; height: 500px"
      />
      <div class="table-container">
        <el-button
          type="primary"
          style="margin-bottom: 10px"
          @click="questionAdd"
        >
          选择试题
        </el-button>
        <el-table :data="tableData">
          <el-table-column type="index" label="序号" width="60" />
          <el-table-column prop="questionName" label="题干" min-width="200" />
          <el-table-column prop="embeddingPoint" label="时间点" width="120">
            <template #default="scope">
              <div style="display: flex; flex-direction: column; gap: 5px">
                <el-button
                  v-if="!isWordCourse"
                  @click="getPoint(scope.row)"
                  color="#626aef"
                  >获取时间点</el-button
                >
                <div class="point-container">
                  第<el-input-number
                    min="0"
                    :max="videoRef?.duration"
                    :precision="0"
                    v-model="scope.row.embeddingPoint"
                    controls-position="right"
                  />秒
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="countdownDuration"
            label="倒计时时长"
            width="100"
          >
            <template #default="scope">
              <div class="point-container">
                <el-input-number
                  min="0"
                  :precision="0"
                  v-model="scope.row.countdownDuration"
                  controls-position="right"
                />秒
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="duration" label="总时长" width="120">
            <template #default="scope">
              {{ formatSeconds(scope.row.duration) }}
            </template>
          </el-table-column>
          <el-table-column width="100" label="操作">
            <template #default="scope">
              <el-button
                type="danger"
                @click="questionDelete(scope.$index)"
                text
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="submitHandle()">确定</el-button>
    </template>
    <!-- 选择试题弹窗 -->
    <chooseExamQuestionDialog
      ref="chooseQuestionDialogRef"
      @fetch-data="chooseQuestionDone"
    />
  </el-dialog>
</template>

<script setup name="setQADialog">
  import { formatSeconds } from "@/utils/common"
  import chooseExamQuestionDialog from "./chooseExamQuestionDialog"
  import { cloneDeep, uniqBy } from "lodash-es"
  import { Base64 } from "js-base64"

  const kkFileURL = import.meta.env.VITE_APP_KK_URL
  const props = defineProps({
    isWordCourse: {
      type: Boolean,
      default: false,
      required: true
    }
  })

  const { proxy } = getCurrentInstance()
  const emit = defineEmits(["refreshDataList"])

  // 使用 ref 定义响应式变量
  const visible = ref(false)
  const dataForm = ref({})
  const tableData = ref([])
  const videoRef = ref()
  const passageIndex = ref(null)
  const videoIndex = ref(null)

  // 重置数据方法
  const resetData = () => {
    dataForm.value = {}
    tableData.value = []
    passageIndex.value = null
    videoIndex.value = null
    if (videoRef.value) {
      videoRef.value.currentTime = 0
      videoRef.value.pause()
    }
  }

  // 打开弹窗方法
  const openDialog = async (row, index, index2) => {
    // 先重置数据
    resetData()

    passageIndex.value = index
    videoIndex.value = index2

    if (row) {
      dataForm.value = cloneDeep(row)
      tableData.value = cloneDeep(dataForm.value.courseQuestionLinkList || [])
    }
    visible.value = true
  }

  // 文档预览URL
  const wordFileUrl = computed(() => {
    if (!props.isWordCourse) return ""
    return (
      `${kkFileURL}/onlinePreview?url=` +
      encodeURIComponent(Base64.encode(dataForm.value.fileUrl))
    )
  })

  // 选择题目
  const chooseQuestionDialogRef = ref()
  const questionAdd = () => {
    chooseQuestionDialogRef.value.openDialog()
  }

  // 选择题目完成
  const chooseQuestionDone = (ids, questionList) => {
    tableData.value = uniqBy(
      tableData.value.concat(questionList),
      "questionId"
    ).map(item => {
      if (item.embeddingPoint) {
        return {
          ...item,
          countdownDuration: item.countdownDuration
        }
      } else {
        return {
          ...item,
          embeddingPoint: 0,
          duration: videoRef.value ? Math.floor(videoRef.value.duration) : 0
        }
      }
    })
  }

  // 获取时间点
  const getPoint = row => {
    if (!videoRef.value) return
    row.embeddingPoint = Math.floor(videoRef.value.currentTime)
    proxy.$modal.msgSuccess("操作成功")
  }

  // 删除题目
  const questionDelete = index => {
    tableData.value.splice(index, 1)
    proxy.$modal.msgSuccess("操作成功")
  }

  // 关闭弹窗
  const dialogClose = () => {
    videoRef.value?.pause()
    resetData()
  }

  // 表单提交
  const submitHandle = () => {
    // 验证必填项
    for (const item of tableData.value) {
      if (!item.embeddingPoint && item.embeddingPoint !== 0) {
        proxy.$modal.msgWarning("请设置时间点")
        return
      }
    }

    proxy.$modal.msgSuccess("设置成功")
    visible.value = false
    emit(
      "refreshDataList",
      tableData.value.map(item => ({
        embeddingPoint: item.embeddingPoint,
        duration: item.duration,
        countdownDuration: item.countdownDuration,
        questionName: item.questionName,
        questionId: item.questionId
      })),
      passageIndex.value,
      videoIndex.value
    )
  }

  defineExpose({
    openDialog
  })
</script>

<style lang="scss" scoped>
  .container {
    display: flex;

    video {
      margin-top: 50px;
      margin-right: 20px;
    }
    .table-container {
      flex: 1;
    }

    .point-container {
      margin-right: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      :deep(.el-input-number) {
        width: 70px;
        .el-input-number__decrease,
        .el-input-number__increase {
          display: none;
        }
        .el-input__wrapper {
          padding: 1px 10px !important;
        }
      }
    }
  }
</style>
