<!--
 * @Description: 个人档案
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-06-08 16:50:00
 * @LastEditTime: 2024-12-30 13:43:56
-->

<template>
  <div class="app-container">
    <el-row :gutter="10" class="mb-8px">
      <div class="home_process_header">
        <el-icon class="backIcon" size="20" @click="handleBack">
          <ArrowLeftBold />
        </el-icon>
        <el-divider direction="vertical"></el-divider>
      </div>
    </el-row>
    <div class="profile-container" v-loading="loading">
      <div class="title">学员培训档案</div>
      <div class="profile">
        <img :src="getAssetURL(row.avatar)" alt="" />
        <div class="profile-info">
          <div>{{ row.userName }}</div>
          <div>{{ row.deptName }}</div>
        </div>
      </div>
      <div class="content">
        <!-- 头部信息展示 -->
        <div class="content-datePicker">
          <el-date-picker
            v-model="dateRange"
            value-format="YYYY-MM-DD"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          ></el-date-picker>
          <el-button type="primary" icon="Search" @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          <el-button @click="handleExport">导出Excel</el-button>
        </div>
        <!-- Echarts部分 -->
        <div class="indexpart">
          <Echarts :option="option" ref="echartsRef" />
        </div>
        <!-- 表格部分 -->
        <div class="course">
          <div class="course-header">
            <div class="left">
              <div>1</div>
              <div>课程</div>
            </div>
            <el-pagination
              class="pagination"
              v-model:current-page="coursePageNum"
              v-model:page-size="coursePageSize"
              layout="total, prev, pager, next,jumper"
              :total="courseTotal"
              @current-change="fetchCourseList"
            />
          </div>
          <el-table :data="courseData" style="width: 100%" :border="false">
            <el-table-column prop="courseName" label="课程名称" width="180" />
            <el-table-column
              prop="learningProcess"
              label="完成进度"
              width="180"
            >
              <template #default="scope">
                {{ scope.row.learningProcess * 100 }}%
              </template>
            </el-table-column>
            <el-table-column prop="learnStatus" label="学习状态" width="180" />
            <el-table-column prop="deltaDuration" label="学习时长" width="180">
              <template #default="scope">
                {{ formatSeconds(scope.row.deltaDuration) }}
              </template>
            </el-table-column>
            <el-table-column
              prop="startTime"
              label="开始学习时间"
              width="180"
            />
            <el-table-column
              prop="actualCompletionTime"
              label="实际完成时间"
              width="180"
            />
            <el-table-column
              prop="associatedExamGrade"
              label="关联的考试得分"
            />
          </el-table>
        </div>
        <div class="course">
          <div class="course-header">
            <div class="left">
              <div>2</div>
              <div>考试</div>
            </div>
            <el-pagination
              class="pagination"
              v-model:current-page="examPageNum"
              v-model:page-size="examPageSize"
              layout="total, prev, pager, next,jumper"
              :total="examTotal"
              @current-change="fetchExamList"
            />
          </div>
          <el-table :data="examData" style="width: 100%" :border="false">
            <el-table-column prop="examName" label="考试名称" width="180" />
            <el-table-column prop="lowestScore" label="及格分" width="180" />
            <el-table-column
              prop="userPaperScore"
              label="用户得分"
              width="180"
            />
            <el-table-column prop="isPass" label="是否通过" width="180">
              <template #default="scope">
                <dict-tag :options="not_or_is" :value="scope.row.isPass" />
              </template>
            </el-table-column>
            <el-table-column prop="examTime" label="考试用时" width="180">
              <template #default="{ row }">
                {{ formatSeconds(row.examTime) }}
              </template>
            </el-table-column>
            <el-table-column
              prop="submitTime"
              label="参加考试时间"
              width="180"
            />
            <el-table-column prop="examStatus" label="考试状态">
              <template #default="scope">
                <dict-tag
                  :options="exam_participate_status"
                  :value="scope.row.examStatus"
                />
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="course">
          <div class="course-header">
            <div class="left">
              <div>3</div>
              <div>问卷</div>
            </div>
            <el-pagination
              class="pagination"
              v-model:current-page="questionnairePageNum"
              v-model:page-size="questionnairePageSize"
              layout="total, prev, pager, next,jumper"
              :total="questionnaireTotal"
              @current-change="fetchQuestionnaireList"
            />
          </div>
          <el-table
            :data="questionnaireData"
            style="width: 100%"
            :border="false"
          >
            <el-table-column prop="surveyTitle" label="调查标题" width="300" />
            <el-table-column prop="startTime" label="开始时间" width="360" />
            <el-table-column prop="endTime" label="结束时间" width="360" />
            <el-table-column prop="participateStatus" label="参与状态">
              <template #default="scope">
                <dict-tag
                  :options="exam_participate_status"
                  :value="scope.row.participateStatus"
                />
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup name="personalProfile">
  import dayjs from "dayjs"
  import {
    getTrainingStatisticsInfo,
    getTrainingCourseList,
    getTrainingExamList,
    getTrainingQuestionnaireList
  } from "@/api/trainingProfile/statistics.js"
  import { getAssetURL, formatSeconds } from "@/utils/common.js"
  let dateRange = ref([])
  const { proxy } = getCurrentInstance()
  const { learn_status, exam_participate_status, not_or_is } = proxy.useDict(
    "learn_status",
    "exam_participate_status",
    "not_or_is"
  )
  const loading = ref(false)

  // 课程分页数据
  let coursePageNum = ref(1)
  let coursePageSize = ref(5)
  let courseTotal = ref(0)

  // 考试分页数据
  let examPageNum = ref(1)
  let examPageSize = ref(5)
  let examTotal = ref(0)

  // 问卷分页数据
  let questionnairePageNum = ref(1)
  let questionnairePageSize = ref(5)
  let questionnaireTotal = ref(0)

  const emit = defineEmits(["updateCurrentView"])

  const handleBack = () => {
    emit("updateCurrentView", "hourStatistics")
  }

  const props = defineProps({
    row: {
      type: Object,
      default: () => ({})
    }
  })

  function handleQuery() {
    getList()
  }
  /** 重置按钮操作 */
  function resetQuery() {
    dateRange.value = []
    coursePageNum.value = 1
    examPageNum.value = 1
    questionnairePageNum.value = 1

    handleQuery()
  }

  const courseData = ref([])
  const examData = ref([])
  const questionnaireData = ref([])
  // 获取页面上方Echarts与下方三个表格数据
  const getList = async () => {
    loading.value = true
    try {
      await fetchStatisticsInfo()
      await fetchCourseList()
      await fetchExamList()
      await fetchQuestionnaireList()
    } finally {
      loading.value = false
    }
  }
  // 获取Echarts数据
  const fetchStatisticsInfo = async () => {
    const queryData = {
      userId: props.row.userId,
      queryTimeFrom: dateRange.value[0],
      queryTimeTo: dateRange.value[1]
    }
    const { code, data } = await getTrainingStatisticsInfo(queryData)
    if (code === 200) {
      updateChart(data)
    }
  }
  // 获取课程表格数据
  const fetchCourseList = async () => {
    let queryData = {
      pageNum: coursePageNum.value,
      pageSize: coursePageSize.value,
      userId: props.row.userId,
      queryTimeFrom: dateRange.value[0],
      queryTimeTo: dateRange.value[1]
    }
    const courseRes = await getTrainingCourseList(queryData)
    if (courseRes.code === 200) {
      courseData.value = courseRes.rows
      courseTotal.value = courseRes.total
    }
  }
  // 获取考试表格数据
  const fetchExamList = async () => {
    let queryData = {
      pageNum: examPageNum.value,
      pageSize: examPageSize.value,
      userId: props.row.userId,
      queryTimeFrom: dateRange.value[0],
      queryTimeTo: dateRange.value[1]
    }
    const examRes = await getTrainingExamList(queryData)
    if (examRes.code === 200) {
      examData.value = examRes.rows
      examTotal.value = examRes.total
    }
  }
  // 获取问卷表格数据
  const fetchQuestionnaireList = async () => {
    let queryData = {
      pageNum: questionnairePageNum.value,
      pageSize: questionnairePageSize.value,
      userId: props.row.userId,
      queryTimeFrom: dateRange.value[0],
      queryTimeTo: dateRange.value[1]
    }
    const questionnaireRes = await getTrainingQuestionnaireList(queryData)
    if (questionnaireRes.code === 200) {
      questionnaireData.value = questionnaireRes.rows
      questionnaireTotal.value = questionnaireRes.total
    }
  }

  const option = ref(null)
  // 更新Echarts数据
  const updateChart = data => {
    if (!data) return
    option.value = {
      legend: {},
      color: ["#91cc75", "#5470c6"],
      tooltip: {},
      dataset: {
        source: [
          ["培训类型", "已完成", "未完成"],
          ["课程", data.courseCompleteCount, data.todoCourseCount],
          ["考试", data.examCompleteCount, data.todoExamCount],
          ["问卷", data.questionnaireCompleteCount, data.todoQuestionnaireCount]
        ]
      },
      xAxis: { type: "category" },
      yAxis: {},
      series: [{ type: "bar" }, { type: "bar" }]
    }
  }

  const handleExport = () => {
    proxy.download(
      "reportforms/report/export",
      {
        userId: props.row.userId,
        userName: props.row.userName,
        deptName: props.row.deptName,
        queryTimeFrom: dateRange.value[0],
        queryTimeTo: dateRange.value[1]
      },
      `${props.row.userName}学员培训档案_${dayjs().format("YYYY-MM-DD")}.xlsx`
    )
  }

  getList()
</script>

<style scoped lang="scss">
  .profile-container {
    display: flex;
    align-items: center;
    flex-direction: column;

    .title {
      font-size: 40px;
      margin-bottom: 20px;
      color: #666666;
    }
    .profile {
      background: url("@/assets/images/profile-background.jpg") no-repeat;
      height: 200px;
      width: 85%;
      background-size: 100% 100%;
      display: flex;
      align-items: center;
      padding: 0 60px;
      > img {
        width: 150px;
        height: 150px;
        border: 8px solid #81b8fc;
        border-radius: 50%;
      }

      .profile-info {
        margin-left: 30px;
        color: white;
        display: flex;
        flex-direction: column;
        align-items: center;
        > div {
          margin: 15px 0;
        }
        :first-child {
          font-size: 24px;
        }
      }
    }

    .content {
      width: 85%;
      padding: 20px;
      display: flex;
      flex-direction: column;
      align-items: center;
      box-shadow: -3px 0 3px -2px #d4d4d4, 3px 0 3px -2px #d4d4d4;
      border-bottom: 1px solid #d4d4d4;

      .content-datePicker {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;

        :deep(.el-input__wrapper) {
          flex-grow: 0;
        }

        :deep(.el-date-editor) {
          margin-right: 30px;
        }
      }

      .indexpart {
        margin-top: 50px;
        height: 300px;
        width: 90%;
      }

      .course {
        margin-top: 30px;
        width: 100%;
        .course-header {
          position: relative;
          display: flex;
          height: 60px;
          background-color: #f4f3f3;
          align-items: center;
          justify-content: space-between;

          .left {
            background-color: #5ca1fc;
            height: 100%;
            width: 200px;
            align-items: center;
            color: white;
            display: flex;
            :first-child {
              width: 30px;
              height: 30px;
              border-radius: 50%;
              background-color: white;
              text-align: center;
              line-height: 30px;
              color: #81b8fc;
              margin-left: 30px;
              margin-right: 30px;
            }
          }

          .left::after {
            content: "";
            position: absolute;
            left: 140px;
            width: 0;
            height: 0;
            border-style: solid;
            border-width: 0 60px 60px 0;
            border-color: transparent #f4f3f3 transparent transparent;
          }

          .pagination {
            margin-right: 30px;
          }
        }
      }
    }
  }
</style>
