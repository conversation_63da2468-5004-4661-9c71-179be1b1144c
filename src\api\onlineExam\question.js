import request from "@/utils/request"
// 查询试题列表
export function listQuestion(query) {
  return request({
    url: "/exam/question/list",
    method: "get",
    params: query
  })
}

// 查询试题详细
export function getQuestion(questionId) {
  return request({
    url: "/exam/question/" + questionId,
    method: "get"
  })
}

// 新增试题
export function addQuestion(data) {
  return request({
    url: "/exam/question",
    method: "post",
    data: data
  })
}

// 修改试题
export function updateQuestion(data) {
  return request({
    url: "/exam/question",
    method: "put",
    data: data
  })
}

// 删除试题
export function delQuestion(questionId) {
  return request({
    url: "/exam/question/" + questionId,
    method: "delete"
  })
}
