<!--
 * @Description: 新增/修改新闻弹窗
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-04-04 13:19:35
 * @LastEditTime: 2023-10-07 09:34:16
-->
<template>
  <el-dialog
    v-model="visible"
    :title="!dataForm.newsId ? `新增` : `修改`"
    :close-on-click-modal="false"
    center
  >
    <el-form
      ref="dataFormRef"
      :model="dataForm"
      :rules="dataRules"
      label-width="140px"
      @keyup.enter="submitHandle()"
    >
      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item label="业务类型" prop="dataType">
            <el-select
              v-model="dataForm.dataType"
              clearable
              placeholder="业务类型"
            >
              <el-option
                v-for="dict in devops_news_data_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="所属目录" prop="catalogueId">
            <el-tree-select
              v-model="dataForm.catalogueId"
              :data="catalogueOptions"
              :props="{
                value: 'catalogueId',
                label: 'catalogueName',
                children: 'children',
                disabled: 'disabled'
              }"
              value-key="catalogueId"
              check-strictly
              @current-change="catalogueSelect"
              default-expand-all
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="标题" prop="newsTitle">
            <el-input
              v-model="dataForm.newsTitle"
              placeholder="标题"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="22">
          <el-form-item label="内容" prop="newsContent">
            <el-input type="textarea" v-model="dataForm.newsContent"></el-input>
          </el-form-item>
        </el-col>
        <template v-if="dataForm.dataType === '1'">
          <el-col :span="22">
            <el-form-item label="发布时间" prop="dateRange">
              <el-date-picker
                v-model="dataForm.dateRange"
                style="width: 240px"
                :editable="false"
                @change="selectDate"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="datetimerange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否置顶" prop="isTop">
              <el-radio-group v-model="dataForm.isTop">
                <el-radio label="1">是</el-radio>
                <el-radio label="2">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否设为最新新闻" prop="isUpNews">
              <el-radio-group v-model="dataForm.isUpNews">
                <el-radio label="1">是</el-radio>
                <el-radio label="2">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否推介" prop="isIntroduction">
              <el-radio-group v-model="dataForm.isIntroduction">
                <el-radio label="1">是</el-radio>
                <el-radio label="2">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否公开" prop="isOpen">
              <el-radio-group v-model="dataForm.isOpen">
                <el-radio label="1">是</el-radio>
                <el-radio label="2">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </template>
      </el-row>
    </el-form>
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="submitHandle()">确定</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
  import { getNews, addNews, updateNews } from "@/api/opertaionManage/news.js"
  import { catalogueList } from "@/api/system/catalogue"
  import catalogue from "@/utils/catalogue.js"

  const emit = defineEmits(["refreshDataList"])
  const catalogueOptions = ref([])

  const { proxy } = getCurrentInstance()
  const { devops_news_data_type } = proxy.useDict("devops_news_data_type")

  const visible = ref(false)
  const dataFormRef = ref()
  const dataForm = ref({})

  // 时间选择器
  const selectDate = () => {
    if (dataForm.value.dateRange != null) {
      dataForm.value.publishStartTime = dataForm.value.dateRange[0]
      dataForm.value.publishEndTime = dataForm.value.dateRange[1]
    } else {
      dataForm.value.publishStartTime = ""
      dataForm.value.publishEndTime = ""
    }
  }

  // 打开弹窗事件
  const openDialog = async id => {
    await getTreeselect()

    // 重置表单数据
    dataForm.value = {}

    if (id) {
      const { data } = await getNews(id)
      Object.assign(dataForm.value, data)
      dataForm.value.dateRange = [
        dataForm.value.publishStartTime,
        dataForm.value.publishEndTime
      ]
    }
    visible.value = true
  }

  /** 查询菜单下拉树结构 */
  async function getTreeselect() {
    catalogueOptions.value = []
    const response = await catalogueList({
      catalogueType: catalogue.NEWS_CATALOGUE
    })
    const catalogueTree = {
      catalogueId: 0,
      catalogueName: "主类目",
      children: [],
      disabled: true
    }
    catalogueTree.children = proxy.handleTree(response.rows, "catalogueId")
    catalogueOptions.value.push(catalogueTree)
  }
  const catalogueSelect = (data, node) => {
    dataForm.value.catalogueName = data.catalogueName
  }

  const dataRules = ref({
    dataType: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
    catalogueId: [
      { required: true, message: "必填项不能为空", trigger: "blur" }
    ],
    newsTitle: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
    newsContent: [
      { required: true, message: "必填项不能为空", trigger: "blur" }
    ],
    dateRange: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
    isTop: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
    isUpNews: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
    isIntroduction: [
      { required: true, message: "必填项不能为空", trigger: "blur" }
    ],
    isOpen: [{ required: true, message: "必填项不能为空", trigger: "blur" }]
  })

  // 表单提交
  const submitHandle = () => {
    dataFormRef.value.validate(valid => {
      if (!valid) {
        return false
      }
      if (!dataForm.value.newsId) {
        addNews(dataForm.value).then(res => {
          if (res.code === 200) {
            proxy.$modal.msgSuccess("新增成功")
            visible.value = false
            emit("refreshDataList")
          }
        })
      } else {
        updateNews(dataForm.value).then(res => {
          if (res.code === 200) {
            proxy.$modal.msgSuccess("修改成功")
            visible.value = false
            emit("refreshDataList")
          }
        })
      }
    })
  }

  defineExpose({
    openDialog
  })
</script>
