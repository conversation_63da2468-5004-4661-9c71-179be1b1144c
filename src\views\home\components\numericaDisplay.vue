<!--
 * @Description: 首页-平台信息数值展示
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-08-03 09:08:49
 * @LastEditTime: 2023-08-31 09:47:35
-->

<template>
  <el-card :bordered="false">
    <template #header> <div class="title">平台信息数值展示</div> </template>
    <div class="display-list">
      <div class="display-item" v-for="item in displayData.length" :key="item">
        <displayCard :displayData="displayData[item - 1]" />
      </div>
    </div>
  </el-card>
</template>

<script setup name="numericaDisplay">
  import displayCard from "./DisplayCard"
  const props = defineProps({
    infoData: {
      type: Object,
      default: () => ({})
    }
  })

  const displayData = ref([])
  watch(
    () => props.infoData,
    () => {
      if (!props.infoData) return
      displayData.value = [
        {
          label: "平台登录",
          value: props.infoData.platformLogin,
          percent: props.infoData.loginPercentChange,
          color: "#42d782",
          unit: "次"
        },
        {
          label: "课程学习",
          value: props.infoData.courseLearned,
          percent: props.infoData.courseLearnedPercentChange,
          color: "#f2862f",
          unit: "次"
        },
        {
          label: "课程时长",
          value: props.infoData.courseStudyTime,
          percent: props.infoData.studyTimePercentChange,
          color: "#6f44d6",
          unit: "分"
        },
        {
          label: "考试参与",
          value: props.infoData.examsTaken,
          percent: props.infoData.examsTakenPercentChange,
          color: "#d72e28",
          unit: "次"
        }
      ]
    },
    { immediate: true }
  )
</script>

<style scoped lang="scss">
  .title {
    padding: 8px;
    font-size: 19px;
    font-weight: bolder;
  }
  .display-list {
    display: flex;
    flex-wrap: wrap;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 20px;

    .display-item {
      margin: 10px;
    }
  }
</style>
