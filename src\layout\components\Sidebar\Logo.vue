<template>
  <div
    class="sidebar-logo-container"
    :class="{ collapse: collapse }"
    :style="{
      backgroundColor:
        domainName === 'taibao'
          ? 'background-color: #0065b3'
          : sideTheme === 'theme-dark'
          ? variables.menuBackground
          : variables.menuLightBackground
    }"
  >
    <transition name="sidebarLogoFade">
      <router-link
        v-if="collapse"
        key="collapse"
        class="sidebar-logo-link"
        to="/"
      >
        <img
          :src="
            tenantLogo ||
            'https://beckwelldb.obs.cn-east-3.myhuaweicloud.com/logo1.png'
          "
          class="sidebar-logo"
        />
      </router-link>
      <router-link v-else key="expand" class="sidebar-logo-link" to="/">
        <img
          class="sidebar-title"
          :src="
            tenantLogo ||
            'https://beckwelldb.obs.cn-east-3.myhuaweicloud.com/logo1.png'
          "
          alt=""
        />
      </router-link>
    </transition>
  </div>
</template>

<script setup>
  import variables from "@/assets/styles/variables.module.scss"
  import useSettingsStore from "@/store/modules/settings"
  import useTenantStore from "@/store/modules/tenant"

  defineProps({
    collapse: {
      type: Boolean,
      required: true
    }
  })

  const settingsStore = useSettingsStore()
  const tenantStore = useTenantStore()
  const sideTheme = computed(() => settingsStore.sideTheme)

  const { tenantLogo, domainName } = storeToRefs(tenantStore)
</script>

<style lang="scss" scoped>
  .sidebarLogoFade-enter-active {
    transition: opacity 1.5s;
  }

  .sidebarLogoFade-enter,
  .sidebarLogoFade-leave-to {
    opacity: 0;
  }

  .sidebar-logo-container {
    position: relative;
    width: 100%;
    height: 80px;
    line-height: 60px;
    background: #2b2f3a;
    text-align: center;
    overflow: hidden;

    & .sidebar-logo-link {
      height: 100%;
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;

      & .sidebar-logo {
        width: 80%;
        vertical-align: middle;
        margin-right: 12px;
        max-width: 100%;
      }

      & .sidebar-title {
        max-width: 100%;
        vertical-align: middle;
        color: #fff;
        max-height: 100%;
      }
    }

    &.collapse {
      .sidebar-logo {
        margin-right: 0px;
      }
    }
  }
</style>
