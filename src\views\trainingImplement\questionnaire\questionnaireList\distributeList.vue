<!--
 * @Description: 下发问卷列表
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-05-11 10:37:29
 * @LastEditTime: 2023-10-17 14:56:37
-->

<template>
  <div class="app-container">
    <el-row :gutter="10" class="mb-8px">
      <div class="home_process_header">
        <el-icon class="backIcon" size="20" @click="backMonitor">
          <ArrowLeftBold />
        </el-icon>
        <el-divider direction="vertical"></el-divider>
        问卷：{{ row.surveyTitle }}
      </div>
    </el-row>
    <el-row :gutter="10" class="mb-8px">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAddOrEdit">
          新增
        </el-button>
      </el-col>
      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table :data="tableData" :border="false">
      <el-table-column label="下发时间" prop="createTime" />
      <el-table-column label="开始时间" prop="startTime" />
      <el-table-column label="结束时间" prop="endTime" />
      <el-table-column
        label="操作"
        width="200"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-button
            link
            type="primary"
            icon="Edit"
            @click="handleAddOrEdit(scope.row)"
          >
            修改
          </el-button>
          <el-button
            link
            type="primary"
            icon="Delete"
            @click="handleDelete(scope.row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 弹窗, 新增 / 修改 -->
    <addEditDialog
      ref="addEditDialogRef"
      @refreshDataList="getList"
    ></addEditDialog>
  </div>
</template>

<script setup name="distributeList">
  import addEditDialog from "./components/addEditDialog.vue"
  import {
    listIssued,
    delIssued
  } from "@/api/trainingImplement/questionnaire.js"

  const { proxy } = getCurrentInstance()
  const emit = defineEmits(["updateCurrentView"])
  const props = defineProps({
    row: {
      type: Object,
      default: () => ({})
    }
  })

  const tableData = ref([])
  const loading = ref(true)
  const showSearch = ref(true)
  const total = ref(0)

  const data = reactive({
    queryParams: {
      pageNum: 1,
      pageSize: 10
    }
  })

  const { queryParams } = toRefs(data)

  /** 查询目录列表 */
  async function getList() {
    loading.value = true
    let queryData = {
      ...queryParams.value,
      questionnaireId: props.row.questionnaireId
    }
    const response = await listIssued(queryData)
    tableData.value = response.rows || []
    total.value = response.total
    loading.value = false
  }

  const backMonitor = () => {
    emit("updateCurrentView", "questionnaireList")
  }

  const addEditDialogRef = ref(null)
  // 新增/修改
  const handleAddOrEdit = row => {
    addEditDialogRef.value.openDialog(props.row.questionnaireId, row?.issuedId)
  }

  /** 删除按钮操作 */
  function handleDelete(row) {
    proxy.$modal
      .confirm('是否确认删除id为"' + row.issuedId + '"的数据项?')
      .then(async () => {
        await delIssued(row.issuedId)
        proxy.$modal.msgSuccess("删除成功")
        getList()
      })
  }

  getList()
</script>

<style lang="scss" scoped>
  .arrangeTable {
    margin: 0 20px;
  }

  .backIcon {
    svg {
      height: 0.7em;
    }
  }
</style>
