<!--
 * @Description: 首页-个人信息与当前时间
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-04-11 09:59:45
 * @LastEditTime: 2023-04-12 09:23:07
-->
<template>
  <el-card :bordered="false">
    <div class="userInfoCard">
      <div class="userInfo">
        <el-avatar class="userAvatar" :src="userStore.avatar" />
        <div class="snowy-index-card-left-one-username">
          <span class="boldText">
            {{ userStore.name }}
          </span>
        </div>
      </div>
      <div class="currentTime">
        <span class="boldText">
          {{ currentTime }}
        </span>
      </div>
    </div>
  </el-card>
</template>

<script setup name="userInfo">
import dayjs from "dayjs"
import { onBeforeUnmount } from "vue"
import useUserStore from "@/store/modules/user"

const userStore = useUserStore()
const currentTime = ref(dayjs().format("YYYY年MM月DD日 HH时mm分ss秒"))
// 运行定时器，一秒获取一次
const interval = window.setInterval(() => {
  currentTime.value = dayjs().format("YYYY年MM月DD日 HH时mm分ss秒")
}, 1000)
// 这个界面不在我们视线中的时候，关闭定时器
onBeforeUnmount(() => {
  window.clearInterval(interval)
})
</script>

<style scoped lang="scss">
.userInfoCard {
  display: flex;
  justify-content: space-between;
  .userInfo {
    display: flex;
    .userAvatar {
      width: 60px;
      height: 60px;
    }

    .snowy-index-card-left-one-username {
      margin-left: 10px;
      display: flex;
      flex-direction: column;
      justify-content: center;
    }
  }
  .currentTime {
    display: flex;
    align-items: center;
    margin-right: 10px;
  }

  .boldText {
    font-weight: 600;
    margin: 2px;
    font-size: 18px;
  }
}
</style>
