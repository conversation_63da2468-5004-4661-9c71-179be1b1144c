/*
 * @Description:
 * @Author: <PERSON><PERSON>
 * @LastEditors: <PERSON><PERSON>
 * @Date: 2023-05-29 15:48:48
 * @LastEditTime: 2023-07-25 09:43:45
 */
import request from "@/utils/request"

//获取全部商品信息
export function listExchange(query) {
  return request({
    url: "/devops/goods/list",
    method: "get",
    params: query
  })
}

// 根据ID获取商品信息
export function getExchange(goodsId) {
  return request({
    url: "/devops/goods/" + goodsId,
    method: "get"
  })
}

// 新增商品
export function addExchange(data) {
  return request({
    url: "/devops/goods",
    method: "post",
    data: data
  })
}

// 修改商品信息
export function updateExchange(data) {
  return request({
    url: "/devops/goods",
    method: "put",
    data: data
  })
}

// 删除商品信息
export function delExchange(goodsIds) {
  return request({
    url: "/devops/goods/" + goodsIds,
    method: "delete"
  })
}
