<!--
 * @Description: 上传视频
 * @Author: <PERSON><PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-05-08 11:06:23
 * @LastEditTime: 2024-11-13 13:23:51
-->
<template>
  <el-dialog v-model="visible" title="新增章节" center>
    <el-form
      ref="dataFormRef"
      :model="dataForm"
      :rules="dataRules"
      label-width="130px"
    >
      <el-row :gutter="10">
        <el-col :span="24">
          <el-form-item label="章节名称" prop="coursePassageName">
            <el-input v-model="dataForm.coursePassageName" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="章节文件" prop="videoFileList">
            <WordUpload
              v-model="dataForm.videoFileList"
              :fileSize="100"
              :limit="30"
              :fileType="FILE_COURSE_TYPE_MAP[COURSE_TYPE.WORD_COURSE]"
              @getFileArray="handleFileArrayUpdate"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="submitHandle()">确定</el-button>
    </template>
  </el-dialog>
</template>

<script setup name="chooseVideoDialog">
  import { COURSE_TYPE, FILE_COURSE_TYPE_MAP } from "@/utils/constant.js"

  const { proxy } = getCurrentInstance()
  const visible = ref(false)
  const dataForm = ref({})
  const dataFormRef = ref()
  const props = defineProps({
    courseId: {
      type: String
    },
    courseType: {
      type: Array,
      default: () => []
    }
  })

  const emit = defineEmits(["refreshDataList"])

  const dataRules = ref({
    coursePassageName: [
      { required: true, message: "必填项不能为空", trigger: "blur" }
    ],
    videoType: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
    videoFileList: [
      { required: true, message: "必填项不能为空", trigger: "blur" }
    ]
  })

  const editIndex = ref(null)
  const openDialog = async (row, index) => {
    editIndex.value = index
    dataForm.value = {
      coursePassageName: '',
      videoFileList: []
    }
    if (row) {
      dataForm.value = {
        coursePassageName: row.coursePassageName,
        videoFileList: row.videoFileList?.map(file => ({
          ...file,
          uid: file.uid || new Date().getTime() + Math.random().toString(36).substr(2)
        })) || []
      }
    }
    visible.value = true
    if (props.courseId) {
      dataForm.value.courseId = props.courseId
    }
  }

  // 表单提交
  const submitHandle = () => {
    dataFormRef.value.validate(valid => {
      if (!valid) {
        return false
      }

      // 确保 videoFileList 是数组并且验证所有文件是否都填写了时长
      const fileList = Array.isArray(dataForm.value.videoFileList)
        ? dataForm.value.videoFileList
        : []

      if (fileList.length === 0) {
        proxy.$modal.msgWarning("请上传文件")
        return false
      }

      const hasEmptyDuration = fileList.some(file => !file.passageDuration)
      if (hasEmptyDuration) {
        proxy.$modal.msgWarning("请为所有文件填写时长")
        return false
      }

      if (!editIndex.value) {
        proxy.$modal.msgSuccess("新增成功")
        visible.value = false
        emit("refreshDataList", dataForm.value)
        editIndex.value = null
      } else {
        proxy.$modal.msgSuccess("修改成功")
        visible.value = false
        emit("refreshDataList", dataForm.value, editIndex.value)
        editIndex.value = null
      }
    })
  }

  // 添加文件列表更新处理方法
  const handleFileArrayUpdate = (files) => {
    dataForm.value.videoFileList = files
  }

  defineExpose({
    openDialog
  })
</script>
