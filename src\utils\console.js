/*
 * @Description: console.ts
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-01-10 14:03:30
 * @LastEditTime: 2024-01-10 14:03:35
 */
function randomColor() {
  let r = Math.floor(Math.random() * 256)
  let g = Math.floor(Math.random() * 256)
  let b = Math.floor(Math.random() * 256)
  //返回随机生成的颜色
  return "rgb(" + r + "," + g + "," + b + ")"
}
console.log = (function (oriLogFunc) {
  return function (...data) {
    const icons = [
      "🌵",
      "🎍",
      "🐾",
      "🌀",
      "🐚",
      "🥝",
      "🥜",
      "🥕",
      "🥒",
      "🌽",
      "🍒",
      "🍅",
      "🍑",
      "🍋",
      "🍈",
      "🌶",
      "🌰",
      "🍠",
      "🍆",
      "🍄",
      "🍐",
      "🍌",
      "🍍",
      "🍇",
      "🍏",
      "🍓",
      "🍎",
      "🍊",
      "🐴",
      "🐗",
      "🦄",
      "🐑",
      "🐶",
      "🐔",
      "🐼",
      "🐒",
      "🌝",
      "💄",
      "💋",
      "👠",
      "👗",
      "👙",
      "🧣",
      "🍰",
      "🍭",
      "🍳",
      "🎄",
      "🎱",
      "⚽",
      "🏀",
      "🎵",
      "🚄",
      "⭕",
      "❌",
      "❓",
      "❗",
      "💯"
    ]
    const icon = icons[Math.floor(Math.random() * icons.length)]
    const bgColor = randomColor()
    const color = randomColor()
    oriLogFunc.call(
      console,
      `%c ${icon} `,
      `font-size:20px;background-color: ${bgColor};color: ${color};`,
      ...data
    )
  }
})(console.log)
