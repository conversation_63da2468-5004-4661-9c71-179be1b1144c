import request from "@/utils/request"
// 查询用户注册信息列表
export function listRegistration(query) {
  return request({
    url: "/system/registration/list",
    method: "get",
    params: query
  })
}

// 查询用户注册信息详细
export function getRegistration(id) {
  return request({
    url: "/system/registration/" + id,
    method: "get"
  })
}

// 新增用户注册信息
export function addRegistration(data) {
  return request({
    url: "/system/registration",
    method: "post",
    data: data
  })
}

// 修改用户注册信息
export function updateRegistration(data) {
  return request({
    url: "/system/registration",
    method: "put",
    data: data
  })
}

// 删除用户注册信息
export function delRegistration(id) {
  return request({
    url: "/system/registration/" + id,
    method: "delete"
  })
}
