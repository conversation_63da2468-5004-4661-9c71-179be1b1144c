<!--
 * @Description: 用户服务协议
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-03-15 08:41:14
 * @LastEditTime: 2024-03-15 14:32:24
-->

<template>
  <div class="app-container">
    <div class="agree-container">
      <div class="agree-title">用户服务协议 </div>
      <div class="agree-subtitle">有关视频内容发布规范</div>
      <p class="agree-text">
        我们致力于提供文明、理性、友善、高质量的交流平台。请保证所发布信息内容(无论是否公开)符合法律法规要求，如您发布涉及国内外时事公共政策、社会事件等相关信息时，应当准确标注信息来源。
      </p>
      <p class="agree-text">1. 不得上传、发布、传播以下违法违规内容:</p>
      <p class="agree-text">(1)反对宪法确定的基本原则的;</p>
      <p class="agree-text">
        (2)危害国家安全，泄露国家秘密，颠覆国家政权，破坏国家统一的;
      </p>
      <p class="agree-text">(3)损害国家荣誉和利益的;</p>
      <p class="agree-text"
        >(4)歪曲、丑化、亵渎、否定英雄烈士事迹和精神，以侮辱、诽谤或者其他方式侵害英雄烈士的姓名、肖像、名誉、荣誉的;</p
      >
      <p class="agree-text"
        >(5)宣扬恐怖主义、极端主义或者煽动实施恐怖活动、极端主义活动的;</p
      >
      <p class="agree-text">(6)宣扬民族仇恨、民族歧视，破坏民族团结的;</p>
      <p class="agree-text">(7)破坏国家宗教政策，宣扬邪教和封建迷信的;</p>
      <p class="agree-text"
        >(8)编造、散布谣言、虚假信息，扰乱经济秩序和社会秩序、破坏社会稳定的;</p
      >
      <p class="agree-text"
        >(9)散布、传播淫秽、色情、赌博、暴力、凶杀、恐怖、欺诈或者教唆犯罪的;</p
      >
      <p class="agree-text"
        >(10)侮辱或者诽谤他人，侵害他人名誉权、隐私权、肖像权、知识产权或其他合法权益的;</p
      >
      <p class="agree-text">(11)违反互联网广告相关法律法规的商业广告;</p>
      <p class="agree-text">(12)法律、行政法规禁止的其他内容。</p>
      <p class="agree-text"
        >2.不得制作、上传、发布以下不良内容，亦应当主动抵制此类内容的传播:</p
      >
      <p class="agree-text">(1)使用夸张标题，内容与标题严重不符的;</p>
      <p class="agree-text">(2)炒作绯闻、丑闻、劣迹等的;</p>
      <p class="agree-text">(3)不当评述自然灾害、重大事故等灾难的;</p>
      <p class="agree-text">(4)带有性暗示、性挑逗等易使人产生性联想的;</p>
      <p class="agree-text">(5)展现血腥、惊悚、残忍等致人身心不适的;</p>
      <p class="agree-text">(6)煽动人群歧视、地域歧视等的;</p>
      <p class="agree-text">(7)宣扬低俗、庸俗、媚俗内容的;</p>
      <p class="agree-text"
        >(8)可能引发未成年人模仿不安全行为和违反社会公德行为、诱导未成年人不良嗜好等的;</p
      >
      <p class="agree-text">(9)对他人进行暴力恐吓、威胁，实施网络暴力行为的;</p>
      <p class="agree-text">(10)散布污言秽语，损害社会公序良俗的;</p>
      <p class="agree-text"
        >(11)其他含有违反法律法规、公共政策、公序良俗，或可能干扰我们正常运营，侵犯其他用户或第三方合法权益的。</p
      >
      <p class="agree-text"
        >3.不得上传、发布、传播含有高危险性、危害表演者自身或他人身心健康的内容:</p
      >
      <p class="agree-text">(1)任何暴力和/或自残行为内容的;</p>
      <p class="agree-text"
        >(2)任何威胁生命健康、利用危险器械表演，或其表演方法可能危及自身或他人人身和/或财产安全的;</p
      >
      <p class="agree-text"
        >(3)怂恿、诱导他人参与可能会造成人身伤害或导致死亡的危险活动的。</p
      >
      <p class="agree-text"
        >4.不得利用基于深度学习、虚拟现实等的新技术新应用制作、发布、传播虚假新闻资讯信息。您在发布或传播利用基于深度学习、虚拟现实、生成式人工智能等新技术新应用制作的非真实音视频信息，或其他可能导致公众混淆或误认的信息内容时，应当以显著方式予以标识。</p
      >
      <p class="agree-text"
        >5.不得发布扰乱平台经营秩序的信息，包括但不限于垃圾信息、商业招揽信息、过度营销信息，与所评论的内容没有关联的信息，刻意使用字符组合以逃避技术审核的信息。</p
      >
      <p class="agree-text"
        >6.尊重知识产权，不得侵犯他人的知识产权，包括版权、商标权等。</p
      >
      <p class="agree-text"
        >7.保护个人隐私，不泄露他人的个人隐私信息，包括不限于姓名、地址、手机号码等。</p
      >
      <p class="agree-text">8.不得出现广告或赞助相关内容。</p>
      <p class="agree-text"
        >如果我们有合理理由认为您的行为违反或可能违反上述约定的，我们有权进行处理，包括在不事先通知的情况下终止向您提供服务，并依法追究相关方的法律责任。</p
      >
    </div>
    <div class="sign-container">
      <img
        v-if="user.signature"
        crossorigin="anonymous"
        :src="user.signature"
        alt=""
      />
      <Esign v-else ref="esignRef" @closeson="submitHandle" />
    </div>
    <div class="btn-container">
      <el-button @click="handleBack">返回</el-button>
      <el-button type="primary" @click="handleSubmit">
        <span v-if="user.signature">继续</span>
        <span v-else>提交</span>
      </el-button>
    </div>
  </div>
</template>

<script setup name="userServiceAgree">
  import dayjs from "dayjs"
  import { fileUpload } from "@/api/system/upload"
  import { updateUserInfo } from "@/api/system/user"
  import useUserStore from "@/store/modules/user"

  const { user } = storeToRefs(useUserStore())
  const route = useRoute()
  const { proxy } = getCurrentInstance()
  const emit = defineEmits(["updateCurrentView"])
  const props = defineProps({
    row: {
      type: Object,
      default: () => ({})
    }
  })

  // 点击继续或提交，判断是否需要签名还是直接跳转
  const handleSubmit = () => {
    if (user.value.signature) {
      emit(
        "updateCurrentView",
        props.row.agreeFlag === "course" ? "addEditCourse" : "addEditMeans"
      )
    } else {
      proxy.$refs.esignRef.handlePreview1()
    }
  }

  // 拿到组件返回的签名并通过接口上传提交再跳转
  const submitHandle = async pngUrl => {
    const fileBlob = btof(
      pngUrl,
      `sign-${dayjs().format("YYYY/MM/DD HH:mm:ss")}`
    )
    // 利用FormData传参
    const MultipartFile = new FormData()
    // file 是后端接受图片的字段
    MultipartFile.append("file", fileBlob)
    const res = await fileUpload(MultipartFile)
    if (res.code === 200) {
      const res2 = await updateUserInfo({
        userId: user.value.userId,
        userName: user.value.userName,
        signature: res.data.url
      })
      if (res2.code === 200) {
        proxy.$modal.msgSuccess("签名上传成功")
        emit(
          "updateCurrentView",
          props.row.agreeFlag === "course" ? "addEditCourse" : "addEditMeans"
        )
        useUserStore().getInfo()
      }
    } else {
      return proxy.$modal.msgError(`上传失败!`)
    }
  }

  const btof = (data, fileName) => {
    const dataArr = data.split(",")
    const byteString = atob(dataArr[1])
    const options = {
      type: "image/jpeg",
      endings: "native"
    }
    const u8Arr = new Uint8Array(byteString.length)
    for (let i = 0; i < byteString.length; i++) {
      u8Arr[i] = byteString.charCodeAt(i)
    }
    return new File([u8Arr], fileName + ".jpg", options)
  }

  const handleBack = () => {
    if (props.row.agreeFlag === "course") {
      proxy.$tab.closeOpenPage({
        path: "/onlineCourse/courseList",
        query: {
          queryParams: route.query.queryParams
            ? JSON.parse(JSON.stringify(route.query.queryParams))
            : undefined
        }
      })
    } else if (props.row.agreeFlag === "means") {
      proxy.$tab.closeOpenPage({
        path: "/trainingImplement/meansManage/meansList"
      })
    }
  }
</script>

<style lang="scss" scoped>
  .app-container {
    padding: 0 200px 50px 200px;
    font-family: "Arial Negreta", "Arial Normal", "Arial", sans-serif;
    font-weight: 700;
  }
  .agree-container {
    margin-bottom: 50px;
    .agree-title,
    .agree-subtitle {
      display: flex;
      align-items: center;
      justify-content: center;
      color: #333333;
    }
    .agree-title {
      font-weight: bold;
      font-size: 24px;
      margin: 20px 0;
    }
    .agree-subtitle {
      margin: 15px 0;
      font-size: 16px;
    }
    .agree-text {
      font-size: 14px;
      line-height: 29px;
    }
  }

  .sign-container {
    display: flex;
    justify-content: center;
    align-self: start;
    width: 1000px;
  }

  .btn-container {
    margin-top: 30px;
    width: 1000px;
    display: flex;
    justify-content: center;
    :deep(.el-button) {
      width: 100px;
    }
  }
</style>
