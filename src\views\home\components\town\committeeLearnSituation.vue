<!--
 * @Description: 北蔡防灾减灾-对应居委学习情况
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-12-18 17:07:43
 * @LastEditTime: 2024-04-01 16:31:46
-->
<template>
  <div class="target-view">
    <div class="header">
      <span class="title">
        {{
          activeTab === "1" ? "对应居委" : activeTab === "3" ? "居民" : ""
        }}学习情况
      </span>

      <el-select
        v-if="activeTab === '2'"
        v-model="queryParams.businessType"
        placeholder="经营类型"
        clearable
        style="width: 200px"
        @change="getData"
      >
        <el-option
          v-for="dict in business_type"
          :key="dict.value"
          :label="dict.label"
          :value="dict.value"
        />
      </el-select>

      <el-tree-select
        v-else
        ref="treeSelectRef"
        v-model="queryParams.deptId"
        :data="deptOptions"
        :props="{ value: 'dept_id', label: 'dept_name', children: 'children' }"
        value-key="dept_id"
        node-key="dept_id"
        placeholder="请选择部门"
        style="width: 200px"
        clearable
        @change="getData"
      />
    </div>
    <div class="view-con">
      <Echarts :option="option" />
    </div>
  </div>
</template>

<script setup>
  import {
    towncountryDist,
    towncountryStore,
    towncountryResident
  } from "@/api/home/<USER>/index"

  const { proxy } = getCurrentInstance()
  const { business_type } = proxy.useDict("business_type")
  const props = defineProps({
    activeTab: {
      type: String
    }
  })

  const queryParams = ref({
    queryTimeFrom: "",
    queryTimeTo: "",
    deptId: "",
    businessType: ""
  })
  const deptOptions = ref([])
  const getData = async () => {
    let res
    if (props.activeTab === "1") {
      if (
        !queryParams.value.queryTimeFrom ||
        !queryParams.value.queryTimeTo ||
        !queryParams.value.deptId
      )
        return
      res = await towncountryDist(queryParams.value)
    } else if (props.activeTab === "2") {
      if (
        !queryParams.value.queryTimeFrom ||
        !queryParams.value.queryTimeTo ||
        !queryParams.value.businessType
      )
        return
      res = await towncountryStore({ ...queryParams.value, userType: "02" })
    } else {
      if (!queryParams.value.queryTime || !queryParams.value.deptId) return
      res = await towncountryResident({ ...queryParams.value, userType: "03" })
    }
    setOption(res.data)
  }

  const option = ref(null)
  const setOption = data => {
    const color = ["#5470c6", "#fac858"]
    option.value = {
      color,
      legend: {
        data: [props.activeTab === "3" ? "学习人数" : "考试成绩", "学习时长"]
      },
      tooltip: {},
      grid: {
        left: "20",
        right: "20",
        top: "30",
        bottom: "10",
        containLabel: true
      },
      xAxis: [
        {
          type: "category",
          data: data.map(item => {
            if (props.activeTab === "2") {
              return item.user_name
            } else {
              return item.dept_name
            }
          }),
          axisLabel: {
            width: 80,
            overflow: "truncate",
            interval: 0,
            rotate: 60
          }
        }
      ],
      yAxis: [
        {
          type: "value",
          name: "分数",
          alignTicks: true,
          axisLine: {
            lineStyle: {
              color: color[0]
            }
          }
        },
        {
          type: "value",
          name: "时长(分钟)",
          alignTicks: true,
          axisLine: {
            lineStyle: {
              color: color[1]
            }
          }
        }
      ],
      dataZoom: [
        {
          // start: 0,// 默认为0
          // end: 100,// 默认为100
          type: "slider",
          show: true,
          handleSize: 0, // 滑动条的 左右2个滑动条的大小
          startValue: 0, // 初始显示值
          endValue: 20, // 结束显示值
          height: 15, // 组件高度
          // left: "5%", // 左边的距离
          // right: "5%", // 右边的距离
          bottom: -10, // 底边的距离
          // borderColor: "#000",
          fillerColor: "#409eff",
          borderRadius: 5,
          backgroundColor: "#eee", // 两边未选中的滑动条区域的颜色
          showDataShadow: false, // 是否显示数据阴影 默认auto
          showDetail: false, // 即拖拽时候是否显示详细数值信息 默认true
          realtime: true, // 是否实时更新
          filterMode: "filter"
        }
      ],
      series: [
        {
          name: props.activeTab === "3" ? "学习人数" : "考试成绩",
          type: "bar",
          barWidth: "20%",
          yAxisIndex: 0,
          data: data.map(item => {
            if (props.activeTab === "3") {
              return item.learners
            } else {
              return item.avg_paper_score
            }
          })
        },
        {
          name: "学习时长",
          type: "bar",
          barWidth: "20%",
          yAxisIndex: 1,
          data: data.map(item => Math.floor(item.total_study_duration / 60))
        }
      ]
    }
  }

  const treeSelectRef = ref(null)
  onBeforeMount(() => {
    proxy.$mitt.on(
      "event",
      ({ queryTime, queryTimeFrom, queryTimeTo, deptOption }) => {
        // 处理接口入参开始/结束时间
        if (props.activeTab === "3") {
          queryParams.value.queryTime = queryTime
        } else {
          queryParams.value.queryTimeFrom = queryTimeFrom
          queryParams.value.queryTimeTo = queryTimeTo
        }
        // 处理接口入参部门
        if (props.activeTab === "2") {
          queryParams.value.businessType = business_type.value[0]?.value
        } else {
          deptOptions.value = deptOption
          queryParams.value.deptId = deptOption[0]?.dept_id
          nextTick(() => {
            treeSelectRef.value.setCheckedKeys([queryParams.value.deptId], true)
          })
        }
        getData()
      }
    )
    // setTimeout(() => {
    //   setOption([
    //     {
    //       avg_paper_score: 20,
    //       dept_name: "紫叶一居",
    //       dept_id: 1205,
    //       total_study_duration: 3621
    //     },
    //     {
    //       avg_paper_score: 0,
    //       dept_name: "紫叶二居",
    //       dept_id: 1206,
    //       total_study_duration: 0
    //     },
    //     {
    //       avg_paper_score: 0,
    //       dept_name: "莲安居委",
    //       dept_id: 1207,
    //       total_study_duration: 0
    //     },
    //     {
    //       avg_paper_score: 0,
    //       dept_name: "莲中居委",
    //       dept_id: 1208,
    //       total_study_duration: 0
    //     },
    //     {
    //       avg_paper_score: 0,
    //       dept_name: "天池居委",
    //       dept_id: 1209,
    //       total_study_duration: 0
    //     },
    //     {
    //       avg_paper_score: 0,
    //       dept_name: "安建居委",
    //       dept_id: 1210,
    //       total_study_duration: 0
    //     },
    //     {
    //       avg_paper_score: 0,
    //       dept_name: "海东居委",
    //       dept_id: 1211,
    //       total_study_duration: 0
    //     },
    //     {
    //       avg_paper_score: 0,
    //       dept_name: "莲溪一居",
    //       dept_id: 1212,
    //       total_study_duration: 0
    //     },
    //     {
    //       avg_paper_score: 0,
    //       dept_name: "莲溪四居",
    //       dept_id: 1213,
    //       total_study_duration: 0
    //     },
    //     {
    //       avg_paper_score: 0,
    //       dept_name: "莲溪六居",
    //       dept_id: 1214,
    //       total_study_duration: 0
    //     },
    //     {
    //       avg_paper_score: 0,
    //       dept_name: "莲溪八居",
    //       dept_id: 1215,
    //       total_study_duration: 0
    //     },
    //     {
    //       avg_paper_score: 0,
    //       dept_name: "莲溪九居",
    //       dept_id: 1216,
    //       total_study_duration: 0
    //     },
    //     {
    //       avg_paper_score: 0,
    //       dept_name: "香花居委",
    //       dept_id: 1217,
    //       total_study_duration: 0
    //     },
    //     {
    //       avg_paper_score: 0,
    //       dept_name: "虹南居委",
    //       dept_id: 1218,
    //       total_study_duration: 0
    //     },
    //     {
    //       avg_paper_score: 0,
    //       dept_name: "和平居委",
    //       dept_id: 1219,
    //       total_study_duration: 0
    //     },
    //     {
    //       avg_paper_score: 0,
    //       dept_name: "龙港居委",
    //       dept_id: 1220,
    //       total_study_duration: 0
    //     },
    //     {
    //       avg_paper_score: 0,
    //       dept_name: "龙博居委",
    //       dept_id: 1221,
    //       total_study_duration: 0
    //     },
    //     {
    //       avg_paper_score: 0,
    //       dept_name: "博华居委",
    //       dept_id: 1222,
    //       total_study_duration: 0
    //     },
    //     {
    //       avg_paper_score: 0,
    //       dept_name: "香溢居委",
    //       dept_id: 1223,
    //       total_study_duration: 0
    //     },
    //     {
    //       avg_paper_score: 0,
    //       dept_name: "潘姚工作站",
    //       dept_id: 1224,
    //       total_study_duration: 0
    //     },
    //     {
    //       avg_paper_score: 0,
    //       dept_name: "杨桥村",
    //       dept_id: 1225,
    //       total_study_duration: 0
    //     },
    //     {
    //       avg_paper_score: 18,
    //       dept_name: "中界村",
    //       dept_id: 1226,
    //       total_study_duration: 1966
    //     },
    //     {
    //       avg_paper_score: 0,
    //       dept_name: "12312",
    //       dept_id: 1332,
    //       total_study_duration: 0
    //     },
    //     {
    //       avg_paper_score: 0,
    //       dept_name: "独秀馒头",
    //       dept_id: 1337,
    //       total_study_duration: 0
    //     },
    //     {
    //       avg_paper_score: 0,
    //       dept_name: "0",
    //       dept_id: 1409,
    //       total_study_duration: 0
    //     },
    //     {
    //       avg_paper_score: 0,
    //       dept_name: "0",
    //       dept_id: 1410,
    //       total_study_duration: 0
    //     },
    //     {
    //       avg_paper_score: 0,
    //       dept_name: "0",
    //       dept_id: 1411,
    //       total_study_duration: 0
    //     },
    //     {
    //       avg_paper_score: 0,
    //       dept_name: "0",
    //       dept_id: 1412,
    //       total_study_duration: 0
    //     },
    //     {
    //       avg_paper_score: 0,
    //       dept_name: "0",
    //       dept_id: 1413,
    //       total_study_duration: 0
    //     },
    //     {
    //       avg_paper_score: 0,
    //       dept_name: "0",
    //       dept_id: 1414,
    //       total_study_duration: 0
    //     },
    //     {
    //       avg_paper_score: 0,
    //       dept_name: "0",
    //       dept_id: 1415,
    //       total_study_duration: 0
    //     },
    //     {
    //       avg_paper_score: 0,
    //       dept_name: "威尔士健身会所北蔡宝华",
    //       dept_id: 1416,
    //       total_study_duration: 0
    //     },
    //     {
    //       avg_paper_score: 0,
    //       dept_name: "大金空调",
    //       dept_id: 1417,
    //       total_study_duration: 0
    //     }
    //   ])
    // }, 1000)
  })
</script>

<style scoped lang="scss">
  .header {
    display: flex;
    justify-content: space-between;
  }
  .title {
    line-height: 40px;
    font-weight: bold;
    margin-left: 10px;
  }
  .view-con {
    background: #fff;
    padding: 14px 20px;
    border-radius: 10px;
    height: 260px;
  }
</style>
