<template>
  <div class="app-container">
    <el-dialog
      v-model="dialogVisible"
      title="选择资料"
      width="70%"
      top="110px"
      @close="close"
      :key="new Date().getTime()"
      center
    >
      <el-form
        :model="queryParams"
        ref="queryRef"
        :inline="true"
        v-show="showSearch"
      >
        <el-form-item label="资料名称" prop="manageName">
          <el-input
            v-model="queryParams.manageName"
            placeholder="请输入资料名称"
            clearable
            style="width: 240px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="资料目录" prop="catalogueId">
          <el-tree-select
            v-model="queryParams.catalogueId"
            :data="catalogueOptions"
            :props="{
              value: 'catalogueId',
              label: 'catalogueName',
              children: 'children',
              disabled: 'disabled'
            }"
            clearable
            value-key="catalogueId"
            placeholder="选择资料目录"
            check-strictly
            default-expand-all
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-table
        ref="chooseMeansTableRef"
        :data="tableData"
        highlight-current-row
        @current-change="handleCurrentChange"
        row-key="manageId"
      >
        <el-table-column
          :selectable="selectable"
          v-if="props.selectionType === 'multiple'"
          type="selection"
          width="55"
          reserve-selection
        />
        <el-table-column label="资料封面" prop="cover" width="100">
          <template #default="scope">
            <el-popover
              v-if="scope.row.cover"
              placement="right"
              :width="400"
              trigger="hover"
            >
              <img :src="scope.row.cover" width="375" height="375" />
              <template #reference>
                <img
                  :src="scope.row.cover"
                  style="max-height: 60px; max-width: 60px"
                />
              </template>
            </el-popover>
            <div v-else>--</div>
          </template>
        </el-table-column>
        <el-table-column label="资料名称" prop="manageName" />
        <el-table-column label="所属目录" prop="catalogueName" width="135" />
        <el-table-column label="资料类型" prop="manageType" width="100">
          <template #default="scope">
            <dict-tag :options="sys_flie_type" :value="scope.row.manageType" />
          </template>
        </el-table-column>
      </el-table>

      <pagination
        class="dialog-pagination"
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
        :autoScroll="false"
      />
      <template #footer>
        <el-button @click="close">取消</el-button>
        <el-button type="primary" @click="save">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="ChooseMeansDialog">
  import { listMeans } from "@/api/trainingImplement/means"
  import { catalogueList } from "@/api/system/catalogue.js"
  import catalogue from "@/utils/catalogue.js"

  const emit = defineEmits(["fetch-data"])
  const { proxy } = getCurrentInstance()
  const props = defineProps({
    selectionType: {
      type: String,
      default: "single"
    },
    disabledList: {
      type: Array,
      default: () => []
    }
  })

  const { sys_flie_type } = proxy.useDict("sys_flie_type")
  const tableData = ref([])
  const showSearch = ref(true)
  const dialogVisible = ref(false)
  const total = ref(0)
  const catalogueOptions = ref([])

  const queryParams = ref({
    pageNum: 1,
    pageSize: 10
  })

  const openDialog = async id => {
    reset()
    if (id) {
      currentRow.value = props.selectionType === "single" ? id : id.split(",")
    }
    dialogVisible.value = true
    await getList()
    await getTreeselect()
  }

  const reset = () => {
    currentRow.value = ""
    queryParams.value = {
      pageNum: 1,
      pageSize: 10
    }
  }

  const tableSelectChange = () => {
    if (props.selectionType === "multiple") {
      if (currentRow.value && currentRow.value.length > 0) {
        nextTick(() => {
          tableData.value.forEach((item, index) => {
            if (currentRow.value.find(v => v == item.manageId)) {
              proxy.$refs["chooseMeansTableRef"].toggleRowSelection(
                proxy.$refs["chooseMeansTableRef"].data[index],
                true
              )
            }
          })
        })
      }
    }
  }

  /** 查询资料列表 */
  const getList = async () => {
    const res = await listMeans(queryParams.value)
    if (res.code === 200) {
      tableData.value = res.rows
      total.value = res.total
      tableSelectChange()
    }
  }

  /** 查询目录下拉树结构 */
  function getTreeselect() {
    catalogueOptions.value = []
    catalogueList({ catalogueType: catalogue.MEANS_CATALOGUE }).then(
      response => {
        const meansCatalogue = {
          catalogueId: 0,
          catalogueName: "主类目",
          children: [],
          disabled: true
        }
        meansCatalogue.children = proxy.handleTree(response.rows, "catalogueId")
        catalogueOptions.value.push(meansCatalogue)
      }
    )
  }

  /** 搜索按钮操作 */
  function handleQuery() {
    getList()
  }

  /** 重置按钮操作 */
  function resetQuery() {
    reset()
    handleQuery()
  }

  // 关闭弹框并重置操作
  const close = () => {
    reset()
    dialogVisible.value = false
  }

  const save = () => {
    if (props.selectionType === "multiple") {
      currentRow.value = proxy.$refs["chooseMeansTableRef"].getSelectionRows()
    }
    emit("fetch-data", currentRow.value)
    proxy.$modal.msgSuccess("操作成功")
    close()
  }

  const currentRow = ref()
  const handleCurrentChange = val => {
    currentRow.value = val
  }

  const selectable = row => {
    if (!props.disabledList || props.disabledList.length === 0) return true
    if (props.disabledList.includes(row.manageId)) {
      return false
    } else {
      return true
    }
  }

  defineExpose({
    openDialog
  })
</script>

<style lang="scss" scoped>
  :deep(.el-table .cell) {
    white-space: pre-line;
  }
  :deep(.pagination-container .el-pagination) {
    position: static;
  }
</style>
