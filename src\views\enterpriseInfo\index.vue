<!--
 * @Description: 短视频管理
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-04-10 17:21:58
 * @LastEditTime: 2023-04-12 15:32:53
-->
<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true">
      <el-form-item label="企业名称" prop="enterpriseName">
        <el-input
          v-model="queryParams.enterpriseName"
          placeholder="企业名称"
          clearable
          style="width: 240px"
          @keyup.enter="getList"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="getList"
          >搜索</el-button
        >
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb-8px">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="openForm('create')">
          新增
        </el-button>
      </el-col>
    </el-row>
    <el-table
      v-loading="loading"
      :data="tableData"
      :stripe="true"
      :show-overflow-tooltip="true"
      highlight-current-row
      @current-change="handleCurrentChange"
    >
      <el-table-column prop="enterpriseName" label="企业名称"></el-table-column>
      <el-table-column prop="contactName" label="联系人"></el-table-column>
      <el-table-column prop="position" label="职务"> </el-table-column>
      <el-table-column prop="phone" label="电话"> </el-table-column>
      <el-table-column prop="address" label="地址"> </el-table-column>
      <el-table-column prop="enterpriseIntro" label="企业简介" width="600">
      </el-table-column>
      <el-table-column
        label="操作"
        width="250"
        class-name="small-padding fixed-width"
        fixed="right"
      >
        <template #default="scope">
          <!-- <el-button
            link
            type="primary"
            icon="View"
            @click="handleDetail(scope.row)"
            >查看</el-button
          > -->
          <el-button
            link
            :type="scope.row.status === '0' ? 'warning' : 'primary'"
            icon="Edit"
            @click="openForm('update', scope.row.id)"
            >{{ scope.row.status === "0" ? "审批" : "修改" }}</el-button
          >
          <el-button
            link
            type="danger"
            icon="Delete"
            @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <el-tabs model-value="teamMember">
      <el-tab-pane label="产品信息" name="teamMember">
        <productList :enterprise-id="currentRow.id" />
      </el-tab-pane>
    </el-tabs>

    <!-- 弹窗, 新增 / 修改 -->
    <addEditDialog
      ref="addEditDialogRef"
      @refreshDataList="getList"
    ></addEditDialog>
  </div>
</template>

<script setup name="videoList">
  import addEditDialog from "./addEditDialog.vue"
  import productList from "./components/productList.vue"
  import { listEnterprise, delEnterprise } from "@/api/enterpriseInfo/index.js"

  const { proxy } = getCurrentInstance()

  const tableData = ref([])
  const loading = ref(false)
  const total = ref(0)
  const addEditDialogRef = ref()

  const data = reactive({
    queryParams: {
      pageNum: 1,
      pageSize: 10,
      enterpriseName: undefined
    }
  })

  const { queryParams } = toRefs(data)

  /** 选中行操作 */
  const currentRow = ref({}) // 选中行
  const handleCurrentChange = row => {
    currentRow.value = row
  }

  const openForm = (type, id) => {
    addEditDialogRef.value.openDialog(type, id)
  }

  /** 查询目录列表 */
  function getList() {
    loading.value = true
    listEnterprise(queryParams.value).then(response => {
      tableData.value = response.rows || []
      total.value = response.total
      loading.value = false
    })
  }

  /** 删除按钮操作 */
  function handleDelete(row) {
    proxy.$modal
      .confirm('是否确认删除名称为"' + row.enterpriseName + '"的数据项?')
      .then(function () {
        return delEnterprise(row.id)
      })
      .then(() => {
        getList()
        proxy.$modal.msgSuccess("删除成功")
      })
      .catch(() => {})
  }

  function resetQuery() {
    proxy.resetForm("queryRef")
    getList()
  }

  getList()
</script>
<style lang="scss" scoped></style>
