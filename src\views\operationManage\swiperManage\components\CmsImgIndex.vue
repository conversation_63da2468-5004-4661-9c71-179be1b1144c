<!--
 * @Description: 资料列表
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-03-08 11:28:06
 * @LastEditTime: 2023-10-19 09:19:43
-->
<template>
  <el-card>
    <el-form
      ref="queryRef"
      :inline="true"
      :model="queryParams"
      @keyup.enter="getList"
      v-show="showSearch"
    >
      <el-form-item>
        <el-input v-model="queryParams.name" placeholder="名称"></el-input>
      </el-form-item>
      <el-form-item>
        <!--这个属性只能选择一个月 value-format="yyyy-MM-dd"-->
        <el-date-picker
          v-model="dateRange"
          style="width: 240px"
          :editable="false"
          @change="selectDate"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>

      <!-- <el-form-item>
        <el-select
          v-model="queryParams.isDisplay"
          placeholder="是否显示"
          clearable
          style="width: 200px"
        >
          <el-option
            v-for="dict in img_is_show"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item> -->
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb-8px">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleUpdate"
          >新增</el-button
        >
      </el-col>
      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="tableData" border style="width: 100%">
      <el-table-column type="selection" width="50"></el-table-column>
      <el-table-column prop="name" label="名称"></el-table-column>
      <el-table-column prop="img" label="图片">
        <template #default="scope">
          <el-popover placement="right" :width="400" trigger="hover">
            <img :src="scope.row.img" width="375" height="375" />
            <template #reference>
              <img
                :src="scope.row.img"
                style="max-height: 60px; max-width: 60px"
              />
            </template>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column prop="updateTime" label="更新时间"></el-table-column>
      <!-- <el-table-column prop="linkUrlWeb" label="web地址"></el-table-column> -->
      <!-- <el-table-column prop="isDisplay" label="是否显示">
        <template #default="scope">
          <dict-tag :options="img_is_show" :value="scope.row.isDisplay"
        /></template>
      </el-table-column> -->
      <el-table-column label="操作" fixed="right" width="150">
        <template #default="scope">
          <el-button type="primary" link @click="handleUpdate(scope.row)"
            >修改</el-button
          >
          <el-button type="primary" link @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 弹窗, 新增 / 修改 -->
    <addEditDialog
      ref="addEditDialogRef"
      @refreshDataList="getList"
    ></addEditDialog>
  </el-card>
</template>

<script setup name="cmsImgIndex">
  import addEditDialog from "./addEditDialog.vue"
  import { listImg, delImg } from "@/api/opertaionManage/img.js"

  const { proxy } = getCurrentInstance()
  const { img_is_show } = proxy.useDict("img_is_show")

  const tableData = ref([])
  const loading = ref(true)
  const showSearch = ref(true)
  const total = ref(0)

  const props = defineProps({
    itemsTypeId: {
      type: Number,
      required: true
    }
  })

  const data = reactive({
    queryParams: {
      pageNum: 1,
      pageSize: 10
    }
  })

  const { queryParams } = toRefs(data)

  /** 查询目录列表 */
  function getList() {
    loading.value = true
    listImg({ ...queryParams.value, carouselId: props.itemsTypeId }).then(
      response => {
        tableData.value = response.rows || []
        total.value = response.total
        loading.value = false
      }
    )
  }
  /** 搜索按钮操作 */
  function handleQuery() {
    getList()
  }
  /** 重置按钮操作 */
  function resetQuery() {
    dateRange.value = []
    queryParams.value = {
      pageNum: 1,
      pageSize: 10
    }
    handleQuery()
  }

  const dateRange = ref([])
  const selectDate = () => {
    if (dateRange.value != null) {
      queryParams.value.beginCreateTime = dateRange.value[0]
      queryParams.value.endCreateTime = dateRange.value[1]
    } else {
      queryParams.value.beginCreateTime = ""
      queryParams.value.endCreateTime = ""
    }
  }

  /** 修改按钮操作 */
  const addEditDialogRef = ref()
  function handleUpdate(row) {
    addEditDialogRef.value.openDialog(props.itemsTypeId, row.carouselImgId)
  }
  /** 删除按钮操作 */
  function handleDelete(row) {
    proxy.$modal
      .confirm('是否确认删除名称为"' + row.name + '"的数据项?')
      .then(function () {
        return delImg(row.carouselImgId)
      })
      .then(() => {
        getList()
        proxy.$modal.msgSuccess("删除成功")
      })
      .catch(() => {})
  }

  getList()
</script>
<style lang="scss" scoped></style>
