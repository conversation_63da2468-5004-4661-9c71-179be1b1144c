<!--
 * @Description: 首页
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-03-03 17:06:07
 * @LastEditTime: 2024-05-11 17:24:53
-->
<template>
  <div class="home">
    <el-row :gutter="20">
      <el-col :span="24">
        <div class="home-title">
          <span>数据一览</span>

          <el-date-picker
            style="width: 260px; margin-left: 60px"
            v-model="dateRange"
            value-format="YYYY-MM-DD"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="selectDate"
          ></el-date-picker>
        </div>
        <ul class="list-item">
          <li>
            <p>登录人数</p>
            <span class="num" style="color: #8287fc">{{
              dashboardData.loginCount
            }}</span>
            <span
              class="icon"
              style="
                background: linear-gradient(to bottom, #cb87ee, #947fed);
                box-shadow: 0 0 6px #8287fc;
              "
            >
              <el-icon size="20">
                <i class="iconfont icon-dengluzhanghao"></i>
              </el-icon>
            </span>
          </li>

          <li>
            <p>学习人数</p>
            <span class="num" style="color: #53d4c8">{{
              dashboardData.studyCount
            }}</span>
            <span
              class="icon"
              style="
                background: linear-gradient(to bottom, #6de7c2, #24d3c3);
                box-shadow: 0 0 6px #24d3c3;
              "
            >
              <el-icon size="20">
                <i class="iconfont icon-xuexiyuandi"></i>
              </el-icon>
            </span>
          </li>
          <li>
            <p>考试人数</p>
            <span class="num" style="color: #ed9d32">{{
              dashboardData.examCount
            }}</span>
            <span
              class="icon"
              style="
                background: linear-gradient(to bottom, #fe7b63, #f86135);
                box-shadow: 0 0 6px #f86135;
              "
            >
              <el-icon size="20">
                <i class="iconfont icon-kaoshi2"></i>
              </el-icon>
            </span>
          </li>
          <template v-if="domainName !== 'eduxd'">
            <li>
              <p>学习时长</p>
              <span class="num" style="color: #0384e8">
                {{ formatSecond(dashboardData.studyDuration)[0] }}<span>时</span
                >{{ formatSecond(dashboardData.studyDuration)[1]
                }}<span>分</span
                >{{ formatSecond(dashboardData.studyDuration)[2]
                }}<span>秒</span>
              </span>
              <span
                class="icon"
                style="
                  background: linear-gradient(to bottom, #11c8fc, #0384e8);
                  box-shadow: 0 0 6px #0384e8;
                "
              >
                <el-icon size="20">
                  <i class="iconfont icon-tongbuxuexi-"></i>
                </el-icon>
              </span>
            </li>
            <li>
              <p>测试准确率</p>
              <span class="num" style="color: #fe5dae">
                {{ dashboardData.examAccuracy
                }}<span style="font-size: 20px">%</span>
              </span>
              <span
                class="icon"
                style="
                  background: linear-gradient(to bottom, #ff96c0, #fe5dae);
                  box-shadow: 0 0 6px #fe5dae;
                "
              >
                <el-icon size="20">
                  <i class="iconfont icon-RectangleCopy"></i>
                </el-icon>
              </span>
            </li>
          </template>
        </ul>
      </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-top: 50px">
      <el-col :span="10"> <UnlearnSituation /></el-col>
      <el-col :span="14"> <UnlearnRank /></el-col>
    </el-row>
  </div>
</template>

<script setup>
  import UnlearnSituation from "./components/unep/unlearnSituation.vue"
  import UnlearnRank from "./components/unep/unlearnRank.vue"
  import { unepDashboard } from "@/api/home/<USER>/index"
  import { formatSecond } from "@/utils/common"
  import { reactive } from "vue"
  import useTenantStore from "@/store/modules/tenant"

  const tenantStore = useTenantStore()
  const { domainName } = storeToRefs(tenantStore)
  let date = new Date()
  let year = date.getFullYear()
  let month =
    date.getMonth() + 1 < 10 ? "0" + (date.getMonth() + 1) : date.getMonth() + 1
  let day = date.getDate() < 10 ? "0" + date.getDate() : date.getDate()
  let startDate = year + "-" + month + "-" + "01"
  let endDate = year + "-" + month + "-" + day
  // 默认当前日期
  let dateRange = ref([endDate, endDate])

  const selectDate = () => {
    getData()
  }
  const dashboardData = reactive({
    loginCount: "",
    studyCount: "",
    examCount: "",
    studyDuration: "",
    examAccuracy: ""
  })

  const getData = () => {
    unepDashboard({
      queryTimeFrom: dateRange.value[0],
      queryTimeTo: dateRange.value[1]
    }).then(res => {
      let data = res.data
      dashboardData.loginCount = data.loginCount
      dashboardData.studyCount = data.studyCount
      dashboardData.examCount = data.examCount
      dashboardData.studyDuration = data.studyDuration

      dashboardData.examAccuracy = data.examAccuracy
        ? (Number(data.examAccuracy) * 100).toFixed(1)
        : 0
    })
  }
  getData()
</script>

<style lang="scss" scoped>
  .iconfont {
    font-size: 20px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-2px, -2px);
    color: #fff;
  }
  .home {
    min-height: calc(100vh - 84px);
    background: #f3f5ff;
    padding: 20px;
    color: #222d61;
  }
  .home-title {
    line-height: 40px;
    font-weight: bold;
    font-size: 20px;
    text-indent: 10px;
    margin-bottom: 10px;
  }
  .list-item {
    white-space: nowrap;
    display: flex;
    > li:not(:last-child) {
      margin-right: 20px;
    }
    > li {
      flex: 1;
      max-width: 284px;
      padding: 16px 20px;
      /* margin: 0 10px; */
      position: relative;
      display: inline-block;
      background: #fff;
      line-height: 30px;
      font-weight: bold;
      border-radius: 10px;
    }
    .icon {
      position: absolute;
      bottom: 20px;
      right: 20px;
      width: 36px;
      height: 36px;
      border-radius: 100%;
    }
    .num {
      margin-top: 10px;
      font-weight: bold;
      display: inline-block;
      font-size: 32px;
      > span {
        font-size: 16px;
        margin: 0 2px;
      }
    }
  }
</style>
