pipeline {
  agent {
    node {
      label 'nodejs'
    }

  }
  stages {
    stage('拉取阿里云效代码ts') {
      agent none
      steps {
        container('nodejs') {
          git(url: 'https://codeup.aliyun.com/bw/beckwell-web-training.git', credentialsId: 'aly-yunxiao-id', branch: 'master', changelog: true, poll: false)
          sh 'ls -al'
          sh 'ls -al'
        }

      }
    }

    stage('nodejs编译') {
      agent none
      steps {
        container('nodejs') {
          sh 'npm install --registry=https://registry.npm.taobao.org'
          sh 'npm run build:prod'
          sh 'ls'
        }

      }
    }

    stage('构建training镜像') {
      agent none
      steps {
        container('nodejs') {
          sh 'ls'
          sh 'docker build -t beckwell-web-training:latest -f Dockerfile .'
        }

      }
    }

   stage('推送training镜像至Harbor') {
      agent none
      steps {
        container('nodejs') {
          withCredentials([usernamePassword(credentialsId : 'harbor-id' ,passwordVariable : 'HARBOR_PWD_VAL' ,usernameVariable : 'HARBOR_USER_VAL' ,)]) {
            sh 'echo "$HARBOR_PWD_VAL" | docker login $REGISTRY -u "$HARBOR_USER_VAL" --password-stdin'
            sh 'docker tag beckwell-web-training:latest $REGISTRY/$DOCKERHUB_NAMESPACE/beckwell-web-training:SNAPSHOT-$BUILD_NUMBER'
            sh 'docker push $REGISTRY/$DOCKERHUB_NAMESPACE/beckwell-web-training:SNAPSHOT-$BUILD_NUMBER'
          }

        }

      }
    }

   stage('部署training到生产环境') {
      agent none
      steps {
        kubernetesDeploy(configs: 'deploy/**', enableConfigSubstitution: true, kubeconfigId: "$KUBECONFIG_CREDENTIAL_ID")
      }
    }


    stage('发送确认邮件') {
      agent none
      steps {
        mail(to: '<EMAIL>', subject: '学习平台构建结果', body: '构建成功了')
      }
    }

  }
  environment {
    DOCKER_CREDENTIAL_ID = 'dockerhub-id'
    GITHUB_CREDENTIAL_ID = 'github-id'
    KUBECONFIG_CREDENTIAL_ID = 'demo-kubeconfig'
    REGISTRY = '*************:8084'
    DOCKERHUB_NAMESPACE = 'edu'
    GITHUB_ACCOUNT = 'kubesphere'
    APP_NAME = 'devops-java-sample'
  }
  parameters {
    string(name: 'TAG_NAME', defaultValue: '', description: '')
  }
}