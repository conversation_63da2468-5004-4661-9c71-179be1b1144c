<!--
 * @Description: 考试列表
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-03-06 16:01:30
 * @LastEditTime: 2023-12-11 13:47:57
-->
<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
    >
      <el-form-item label="考试名称" prop="examName">
        <el-input
          v-model="queryParams.examName"
          placeholder="考试名称"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb-8px">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd">
          新增
        </el-button>
      </el-col>
      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="tableData">
      <el-table-column label="考试名称" prop="baseName" />
      <el-table-column label="关联试卷" prop="paperName" />
      <el-table-column label="创建人" prop="createBy" />
      <el-table-column label="创建时间" prop="createTime" />
      <el-table-column
        label="操作"
        width="320"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-button
            link
            type="primary"
            icon="View"
            @click="viewArrange(scope.row)"
            >查看安排</el-button
          >
          <el-button
            link
            type="primary"
            icon="Pointer"
            @click="handleArrange(scope.row)"
            >安排</el-button
          >
          <el-button
            link
            type="primary"
            icon="Edit"
            @click="handleUpdate(scope.row)"
            >修改</el-button
          >
          <el-button
            link
            type="primary"
            icon="Delete"
            @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
    <!-- 安排考试弹窗 -->
    <arrangeDialog ref="arrangeDialogRef" @fetch-data="getList" />
    <!-- 查看安排弹窗 -->
    <viewArrangeDialog ref="viewArrangeDialogRef" />
  </div>
</template>

<script setup name="examList">
  import { delExam, listExam } from "@/api/onlineExam/exam"
  import arrangeDialog from "./components/arrangeDialog"
  import viewArrangeDialog from "./components/viewArrangeDialog.vue"

  const { proxy } = getCurrentInstance()

  const tableData = ref([])
  const loading = ref(true)
  const showSearch = ref(true)
  const total = ref(0)

  const data = reactive({
    queryParams: {
      pageNum: 1,
      pageSize: 10
    }
  })

  const { queryParams } = toRefs(data)

  /** 查询目录列表 */
  function getList() {
    loading.value = true
    listExam(queryParams.value).then(response => {
      tableData.value = response.rows || []
      total.value = response.total
      loading.value = false
    })
  }
  /** 搜索按钮操作 */
  function handleQuery() {
    getList()
  }
  /** 重置按钮操作 */
  function resetQuery() {
    proxy.resetForm("queryRef")
    handleQuery()
  }
  /** 安排按钮操作 */
  const handleArrange = row => {
    proxy.$refs["arrangeDialogRef"].openDialog("add", row)
  }
  const handleAdd = () => {
    proxy.$tab.closeOpenPage({
      path: "/onlineExam/examAdd"
    })
  }
  /** 修改按钮操作 */
  function handleUpdate(row) {
    proxy.$tab.closeOpenPage({
      path: "/onlineExam/examAdd",
      query: row
    })
  }
  /** 删除按钮操作 */
  function handleDelete(row) {
    proxy.$modal
      .confirm('是否确认删除名称为"' + row.baseName + '"的数据项?')
      .then(function () {
        return delExam(row.baseId)
      })
      .then(() => {
        getList()
        proxy.$modal.msgSuccess("删除成功")
      })
      .catch(() => {})
  }

  const viewArrange = row => {
    proxy.$refs["viewArrangeDialogRef"].openDialog(row)
  }

  getList()
</script>

<style lang="scss" scoped>
  .arrangeTable {
    margin: 0 20px;
  }
</style>
