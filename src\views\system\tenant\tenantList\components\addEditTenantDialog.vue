<!--
 * @Description: 新增/修改租户
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-05-11 15:38:10
 * @LastEditTime: 2024-05-13 10:11:28
-->

<template>
  <el-dialog :title="title" v-model="open" width="1200px" append-to-body>
    <el-form ref="tenantRef" :model="form" :rules="rules" label-width="100px">
      <el-row>
        <el-col :span="24">
          <el-form-item label="租户名" prop="name">
            <el-input
              maxlength="20"
              v-model="form.name"
              placeholder="请输入租户名"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="租户LOGO" prop="tenantIcon">
            <ImageUpload v-model="form.tenantIcon" :limit="1" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="登录背景" prop="backgroundImage">
            <ImageUpload v-model="form.backgroundImage" :limit="1" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="背景音乐" prop="tenantBgm">
            <FileUpload
              v-model="form.tenantBgm"
              :fileSize="50"
              :fileType="['mp3', 'wav', 'APE']"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="视频防挂机" prop="hookTimer">
            <span class="text">
              每<div class="margin-number-input">
                <el-input-number v-model="form.hookTimer" :min="0" /> </div
              >秒调用一次(未设置表示不启用视频防挂机)
            </span>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="租户套餐" prop="packageId">
            <el-select
              v-model="form.packageId"
              placeholder="请选择租户套餐"
              clearable
              style="width: 200px"
            >
              <el-option
                v-for="dict in packageList"
                :key="dict.id"
                :label="dict.name"
                :value="dict.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="课程套餐" prop="coursePackageId">
            <el-select
              v-model="form.coursePackageId"
              placeholder="请选择课程套餐"
              clearable
              style="width: 200px"
              @select="selectPackage"
            >
              <el-option
                v-for="dict in coursePackageList"
                :key="dict.id"
                :label="dict.name"
                :value="dict.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="联系人" prop="contactName">
            <el-input v-model="form.contactName" placeholder="请输入联系人" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="联系手机" prop="contactMobile">
            <el-input
              v-model="form.contactMobile"
              placeholder="请输入联系手机"
              maxlength="11"
              @input="v => (form.contactMobile = v.replace(/[^\d.]/g, ''))"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="账号额度" prop="accountCount">
            <el-input-number
              v-model="form.accountCount"
              controls-position="right"
              :min="0"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="过期时间" prop="expireTime">
            <el-date-picker
              v-model="form.expireTime"
              type="date"
              placeholder="请选择过期时间"
              value-format="YYYY-MM-DD"
              :disabled-date="disabledDate"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="绑定域名" prop="domain">
            <el-input v-model="form.domain" placeholder="请输入绑定域名">
              <template #append>eduadmin.bkehs.com</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="租户状态" prop="status">
            <el-radio-group v-model="form.status">
              <el-radio
                v-for="dict in sys_normal_disable"
                :key="dict.value"
                :label="dict.value"
                >{{ dict.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button
          type="primary"
          @click="submitForm"
          :submitDisabled="submitDisabled"
          >确 定</el-button
        >
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
  import { extraTenantFieldList } from "@/utils/constant"
  import { listPackage as listCoursePackage } from "@/api/system/package"
  import {
    addTenant,
    getTenant,
    updateTenant,
    listPackage
  } from "@/api/system/tenant"

  const emit = defineEmits(["refreshDataList"])
  const { proxy } = getCurrentInstance()
  const { sys_normal_disable } = proxy.useDict("sys_normal_disable")
  const form = ref({
    id: undefined,
    name: undefined,
    tenantIcon: undefined,
    backgroundImage: undefined,
    tenantBgm: undefined,
    packageId: undefined,
    coursePackageId: undefined,
    contactName: undefined,
    contactMobile: undefined,
    accountCount: undefined,
    expireTime: undefined,
    domain: undefined,
    status: undefined,
    hookTimer: undefined,
    remark: undefined
  })
  const rules = ref({
    name: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
    packageId: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
    tenantIcon: [
      { required: true, message: "必填项不能为空", trigger: "blur" }
    ],
    coursePackageId: [
      { required: true, message: "必填项不能为空", trigger: "blur" }
    ],
    contactName: [
      { required: true, message: "必填项不能为空", trigger: "blur" }
    ],
    accountCount: [
      { required: true, message: "必填项不能为空", trigger: "blur" }
    ],
    expireTime: [
      { required: true, message: "必填项不能为空", trigger: "blur" }
    ],
    domain: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
    status: [{ required: true, message: "必填项不能为空", trigger: "blur" }]
  })
  const title = ref("新增租户")
  const open = ref(false)
  // 打开弹窗事件
  const openDialog = async id => {
    await fetchPackageList()
    await fetchCoursePackageList()

    if (id) {
      getTenant(id).then(response => {
        form.value = response.data
        form.value.status = String(form.value.status)
        convertRemarkData()
        title.value = "修改租户"
      })
    }
    open.value = true
  }

  const convertRemarkData = () => {
    if (!form.value.remark) return
    // 假设数据格式为 "key1:value1;key2:value2;key3:value3"
    const remarkArr = form.value.remark.includes(";")
      ? form.value.remark.split(";")
      : [form.value.remark]
    remarkArr.forEach(pair => {
      // 每个pair是一个“键:值”对，使用`:`再次分割
      const [key, value] = pair.split(":")
      // 将分割出来的键值对添加到对象中
      if (key && value) {
        form.value[key] = value
      }
    })
  }

  const disabledDate = time => {
    return time.getTime() < Date.now()
  }

  const submitDisabled = ref(false)
  // 套餐select事件
  const selectPackage = val => {
    submitDisabled.value = false
    const selectedItem = packageList.value.find(item => item.id === val)
    if (selectedItem.packagePrice === -1) {
      proxy.$modal.msgWarning("该套餐下无课程，请先添加课程")
      submitDisabled.value = true
    }
  }

  /** 提交按钮 */
  function submitForm() {
    let func = form.value.id ? updateTenant : addTenant
    proxy.$refs["tenantRef"].validate(valid => {
      if (!valid) return
      // 数据处理
      const submitData = JSON.parse(JSON.stringify(form.value))
      const remarkStrList = []
      extraTenantFieldList.forEach(item => {
        if (item && submitData[item]) {
          remarkStrList.push(`${item}: ${submitData[item]}`)
          delete submitData[item]
        }
      })
      submitData.remark = remarkStrList.join(";")
      func(submitData).then(response => {
        proxy.$modal.msgSuccess("修改成功")
        reset()
        open.value = false
        emit("refreshDataList")
      })
    })
  }
  const packageList = ref([])
  const fetchPackageList = async () => {
    let queryData = {
      pageSize: 999,
      pageNum: 1
    }
    const res = await listPackage(queryData)
    if (res.code === 200) {
      packageList.value = res.rows || []
    }
  }

  const coursePackageList = ref([])
  const fetchCoursePackageList = async () => {
    let queryData = {
      pageSize: 999,
      pageNum: 1
    }
    const res = await listCoursePackage(queryData)
    if (res.code === 200) {
      coursePackageList.value = res.rows || []
    }
  }
  /** 表单重置 */
  function reset() {
    proxy.resetForm("tenantRef")
    form.value = {
      id: undefined,
      name: undefined,
      tenantIcon: undefined,
      backgroundImage: undefined,
      tenantBgm: undefined,
      packageId: undefined,
      coursePackageId: undefined,
      contactName: undefined,
      contactMobile: undefined,
      accountCount: undefined,
      expireTime: undefined,
      domain: undefined,
      status: undefined,
      hookTimer: undefined,
      remark: undefined
    }
  }
  /** 取消按钮 */
  function cancel() {
    reset()
    open.value = false
    emit("refreshDataList")
  }
  defineExpose({
    openDialog
  })
</script>

<style lang="scss" scoped>
  .text {
    font-size: 14px;
    font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB,
      Microsoft YaHei, Arial, sans-serif;
    font-weight: 500;
    color: #606266;
  }

  .margin-number-input {
    display: inline-block;
    margin: 0 5px;
  }
</style>
