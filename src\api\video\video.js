/*
 * @Description: 短视频管理相关API
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-04-11 13:39:56
 * @LastEditTime: 2023-04-25 10:34:16
 */
import request from "@/utils/request"

// 查询短视频列表
export function listVideo(query) {
  return request({
    url: "/devops/vlog/list",
    method: "get",
    params: query
  })
}

// 查询短视频详细
export function getVideo(examId) {
  return request({
    url: "/devops/vlog/" + examId,
    method: "get"
  })
}

// 新增短视频
export function addVideo(data) {
  return request({
    url: "/devops/vlog",
    method: "post",
    data: data
  })
}

// 修改短视频
export function updateVideo(data) {
  return request({
    url: "/devops/vlog",
    method: "put",
    data: data
  })
}

// 删除短视频
export function delVideo(examId) {
  return request({
    url: "/devops/vlog/" + examId,
    method: "delete"
  })
}
