/*
 * @Description: 消息推送模块API
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-07-04 16:24:03
 * @LastEditTime: 2023-07-04 16:24:18
 */

import request from "@/utils/request"

// 推送邮件
export function sendEmail(data) {
  return request({
    url: "/system/account/sendEmail",
    method: "post",
    data: data
  })
}

// 推送短信
export function sendSms(data) {
  return request({
    url: "/system/sms/sendSms",
    method: "post",
    data: data
  })
}
