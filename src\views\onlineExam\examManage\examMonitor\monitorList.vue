<!--
 * @Description: 考试监控
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-03-29 15:03:25
 * @LastEditTime: 2024-02-26 08:45:24
-->

<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
    >
      <el-form-item label="考试名称" prop="baseName">
        <el-input
          v-model="queryParams.baseName"
          placeholder="请输入考试名称"
          clearable
          style="width: 240px"
          @keyup.enter="getList"
        />
      </el-form-item>
      <el-form-item label="考试时间" style="width: 308px">
        <el-date-picker
          v-model="dateRange"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="selectDate"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="getList"
          >搜索</el-button
        >
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb-8px">
      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
        :search="false"
      ></right-toolbar>
    </el-row>
    <el-table :data="tableData" :border="false" v-loading="loading">
      <el-table-column label="考试名称" prop="baseName" />
      <el-table-column label="开始时间" prop="startTime" />
      <el-table-column label="结束时间" prop="endTime" />
      <el-table-column
        label="考试时长（分钟）"
        prop="examDurationLimit"
        width="150"
      />
      <el-table-column label="安排人数" prop="arrangeCount" width="120" />
      <el-table-column label="参与人数" prop="arrangeJoinCount" width="120" />
      <el-table-column label="通过率" prop="passRate" width="120">
        <template #default="scope"> {{ scope.row.passRate * 100 }}% </template>
      </el-table-column>
      <el-table-column label="平均成绩" prop="avgScore" width="110">
        <template #default="scope">
          {{ parseFloat(scope.row.avgScore?.toFixed(2)) || 0 }}分
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        width="200"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-button
            link
            type="primary"
            icon="Edit"
            @click="goPersonnelList(scope.row)"
          >
            人员列表
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script setup name="monitorList">
  import { listArrange } from "@/api/onlineExam/exam"

  const emit = defineEmits(["updateCurrentView"])

  const { proxy } = getCurrentInstance()
  const dateRange = ref([])
  const tableData = ref([])
  const loading = ref(true)
  const showSearch = ref(true)
  const total = ref(0)

  const data = reactive({
    queryParams: {
      pageNum: 1,
      pageSize: 10
    }
  })

  const { queryParams } = toRefs(data)

  /** 查询目录列表 */
  function getList() {
    loading.value = true
    listArrange(queryParams.value).then(response => {
      tableData.value = response.rows || []
      total.value = response.total
      loading.value = false
    })
  }

  /** 重置按钮操作 */
  function resetQuery() {
    dateRange.value = []
    queryParams.value = {
      pageNum: 1,
      pageSize: 10
    }
    proxy.resetForm("queryRef")
    getList()
  }
  // 人员列表
  const goPersonnelList = row => {
    emit("updateCurrentView", "personnelList", row)
  }
  const selectDate = () => {
    if (dateRange.value != null) {
      queryParams.value.queryTimeFrom = dateRange.value[0]
      queryParams.value.queryTimeTo = dateRange.value[1]
    } else {
      queryParams.value.queryTimeFrom = ""
      queryParams.value.queryTimeTo = ""
    }
  }

  getList()
</script>

<style lang="scss" scoped>
  .arrangeTable {
    margin: 0 20px;
  }
</style>
