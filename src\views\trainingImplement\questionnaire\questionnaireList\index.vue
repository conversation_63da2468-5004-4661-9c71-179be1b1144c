<!--
 * @Description: 调查问卷列表-入口文件
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-05-10 16:11:58
 * @LastEditTime: 2024-12-06 11:56:51
-->
<template>
  <section class="index-container">
    <component
      :is="currentView"
      :row="row"
      @updateCurrentView="updateCurrentView"
    ></component>
  </section>
</template>

<script setup name="questionnaireListIndex">
  import questionnaireList from "./questionnaireList.vue"
  import distributeList from "./distributeList.vue"
  import resultList from "./resultList.vue"
  import resultDetail from "./resultDetail.vue"
  import statisticsList from "./statistiscs.vue"

  const row = ref({})
  const currentView = shallowRef(questionnaireList)

  const updateCurrentView = (view, item) => {
    row.value = item
    if (view === "distributeList") {
      currentView.value = distributeList
    } else if (view === "resultList") {
      currentView.value = resultL<PERSON>
    }  else if (view === "resultDetail") {
      currentView.value = resultDetail
    }else if (view === "questionnaireList") {
      currentView.value = questionnaireList
    } else if (view === "statisticsList") {
      currentView.value = statisticsList
    }
  }
</script>
