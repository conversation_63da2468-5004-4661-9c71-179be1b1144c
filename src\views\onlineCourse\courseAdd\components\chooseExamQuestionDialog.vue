<template>
  <div class="app-container">
    <el-dialog
      v-model="dialogVisible"
      title="添加题目"
      width="1000px"
      top="110px"
      @close="close"
      :key="new Date().getTime()"
      center
    >
      <el-form
        :model="queryParams"
        ref="queryRef"
        :inline="true"
        v-show="showSearch"
      >
        <el-form-item label="课程目录" prop="catalogueId">
          <el-tree-select
            v-model="queryParams.catalogueId"
            :data="catalogueOptions"
            :props="{
              value: 'catalogueId',
              label: 'catalogueName',
              children: 'children',
              disabled: 'disabled'
            }"
            clearable
            value-key="catalogueId"
            placeholder="选择课程目录"
            check-strictly
            default-expand-all
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-table
        ref="examQuestionRef"
        v-loading="loading"
        :data="tableData"
        row-key="questionId"
      >
        <el-table-column type="selection" width="55" reserve-selection />
        <el-table-column label="试题类型" width="100" prop="questionType">
          <template #default="scope">
            <dict-tag
              :options="exam_question_type"
              :value="scope.row.questionType"
            />
          </template>
        </el-table-column>
        <el-table-column label="所属目录" width="150" prop="catalogueName">
        </el-table-column>
        <el-table-column label="题干" prop="questionName"> </el-table-column>
        <el-table-column label="难度" width="60" prop="degreeType">
          <template #default="scope">
            <dict-tag
              :options="exam_degree_type"
              :value="scope.row.degreeType"
            />
          </template>
        </el-table-column>
      </el-table>

      <pagination
        class="dialog-pagination"
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
        :autoScroll="false"
      />
      <template #footer>
        <el-button @click="close">取消</el-button>
        <el-button type="primary" @click="save">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="chooseQuestionDialog">
  import { listQuestion } from "@/api/onlineExam/question"

  import { catalogueList } from "@/api/system/catalogue.js"
  import catalogue from "@/utils/catalogue.js"

  const emit = defineEmits(["fetch-data"])
  const { proxy } = getCurrentInstance()

  const tableData = ref([])
  const loading = ref(true)
  const showSearch = ref(true)
  const dialogVisible = ref(false)
  const total = ref(0)

  //-----------------------------------------------------
  const catalogueOptions = ref([])
  const { exam_question_type, exam_degree_type } = proxy.useDict(
    "exam_question_type",
    "exam_degree_type"
  )

  const data = reactive({
    queryParams: {
      pageNum: 1,
      pageSize: 10
    },
    selectedRows: []
  })

  const { queryParams, selectedRows } = toRefs(data)

  //** 弹框打开事件 */
  const openDialog = async idString => {
    dialogVisible.value = true
    selectedRows.value = ""
    if (idString) {
      selectedRows.value = Array.isArray(idString)
        ? idString
        : idString.split(",")
    }
    getList()
  }

  const tableSelectChange = () => {
    if (selectedRows.value && selectedRows.value.length > 0) {
      nextTick(() => {
        tableData.value.forEach((item, index) => {
          if (selectedRows.value.find(v => v == item.questionId)) {
            proxy.$refs["examQuestionRef"].toggleRowSelection(
              proxy.$refs["examQuestionRef"].data[index],
              true
            )
          }
        })
      })
    }
  }

  /** 查询目录列表 */
  function getList() {
    loading.value = true
    listQuestion(queryParams.value).then(response => {
      tableData.value = response.rows || []
      total.value = response.total
      tableSelectChange()
      loading.value = false
    })
  }

  /** 搜索按钮操作 */
  function handleQuery() {
    getList()
  }
  /** 重置按钮操作 */
  function resetQuery() {
    proxy.resetForm("queryRef")
    handleQuery()
  }
  // 关闭弹框并重置操作
  const close = () => {
    proxy.resetForm("queryRef")
    dialogVisible.value = false
  }
  const save = () => {
    const selectionRows = proxy.$refs["examQuestionRef"].getSelectionRows()
    const newSelectionRows = selectionRows.map(item => {
      return {
        ...item,
        questionCatalogueId: item.catalogueId,
        questionCatalogueName: item.catalogueName
      }
    })
    const idArray = selectionRows.map(item => item.questionId)
    emit("fetch-data", idArray, newSelectionRows)
    proxy.$modal.msgSuccess("操作成功")
    close()
  }

  /** 查询目录下拉树结构 */
  const getTreeselect = async () => {
    catalogueOptions.value = []
    const response = await catalogueList({
      catalogueType: catalogue.SUBJECT_CATALOGUE
    })
    const catalogueTree = {
      catalogueId: 0,
      catalogueName: "主类目",
      children: [],
      disabled: true
    }
    catalogueTree.children = proxy.handleTree(response.rows, "catalogueId")
    catalogueOptions.value.push(catalogueTree)
  }

  getTreeselect()

  defineExpose({
    openDialog
  })
</script>

<style lang="scss" scoped>
  :deep(.pagination-container .el-pagination) {
    position: static;
  }
</style>
