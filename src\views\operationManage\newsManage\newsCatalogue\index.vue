<!--
 * @Description: 新闻目录
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-04-04 15:24:02
 * @LastEditTime: 2024-02-07 14:27:02
-->
<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
    >
      <el-form-item label="目录名称" prop="catalogueName">
        <el-input
          v-model="queryParams.catalogueName"
          placeholder="请输入目录名称"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb-8px">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button type="info" plain icon="Sort" @click="toggleExpandAll"
          >展开/折叠</el-button
        >
      </el-col>
      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-if="refreshTable"
      v-loading="loading"
      :data="catalogueTableData"
      row-key="catalogueId"
      :default-expand-all="isExpandAll"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
    >
      <el-table-column
        align="left"
        header-align="center"
        prop="catalogueName"
        label="目录名称"
      ></el-table-column>
      <el-table-column label="排序" prop="orderNum" width="70" />
      <el-table-column
        label="创建人"
        width="160"
        prop="createBy"
      ></el-table-column>
      <el-table-column label="创建时间" width="160" prop="createTime">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        width="280"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-button
            link
            type="primary"
            icon="Edit"
            @click="handleUpdate(scope.row)"
            >修改</el-button
          >
          <el-button
            link
            type="primary"
            icon="Plus"
            @click="handleAdd(scope.row)"
            >新增</el-button
          >
          <el-button
            link
            type="primary"
            icon="Delete"
            @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <!-- 添加或修改目录对话框 -->
    <el-dialog :title="title" v-model="open" width="680px" append-to-body>
      <el-form
        ref="catalogueRef"
        :model="form"
        :rules="rules"
        label-width="100px"
      >
        <el-row>
          <el-col :span="24">
            <el-form-item label="上级目录">
              <el-tree-select
                v-model="form.parentId"
                :data="catalogueOptions"
                :props="{
                  value: 'catalogueId',
                  label: 'catalogueName',
                  children: 'children'
                }"
                value-key="catalogueId"
                placeholder="选择上级目录"
                check-strictly
                :render-after-expand="false"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="目录名称" prop="catalogueName">
              <el-input
                v-model="form.catalogueName"
                placeholder="请输入目录名称"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="显示排序" prop="orderNum">
              <el-input-number
                v-model="form.orderNum"
                controls-position="right"
                :min="0"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="questionCatalogue">
  import {
    catalogueList,
    addCatalogue,
    updateCatalogue,
    delCatalogue,
    getCatalogue
  } from "@/api/system/catalogue"
  import catalogue from "@/utils/catalogue.js"
  const { proxy } = getCurrentInstance()

  const catalogueTableData = ref([])
  const open = ref(false)
  const loading = ref(true)
  const showSearch = ref(true)
  const title = ref("")
  const catalogueOptions = ref([])
  const isExpandAll = ref(false)
  const refreshTable = ref(true)

  const data = reactive({
    form: {
      catalogueType: catalogue.NEWS_CATALOGUE
    },
    queryParams: {
      catalogueName: undefined,
      catalogueType: catalogue.NEWS_CATALOGUE
    },
    rules: {
      catalogueName: [
        { required: true, message: "目录名称不能为空", trigger: "blur" }
      ],
      orderNum: [
        { required: true, message: "显示排序不能为空", trigger: "blur" }
      ]
    }
  })

  const { queryParams, form, rules } = toRefs(data)

  /** 查询目录列表 */
  function getList() {
    loading.value = true
    catalogueList(queryParams.value).then(response => {
      catalogueTableData.value = proxy.handleTree(response.rows, "catalogueId")
      loading.value = false
    })
  }
  /** 查询目录下拉树结构 */
  function getTreeselect() {
    catalogueOptions.value = []
    catalogueList({ catalogueType: catalogue.NEWS_CATALOGUE }).then(
      response => {
        const catalogueTree = {
          catalogueId: 0,
          catalogueName: "主类目",
          children: []
        }
        catalogueTree.children = proxy.handleTree(response.rows, "catalogueId")
        catalogueOptions.value.push(catalogueTree)
      }
    )
  }
  /** 取消按钮 */
  function cancel() {
    open.value = false
    reset()
  }
  /** 表单重置 */
  function reset() {
    form.value = {
      catalogueId: undefined,
      parentId: 0,
      catalogueName: undefined,
      catalogueType: catalogue.NEWS_CATALOGUE
    }
    proxy.resetForm("catalogueRef")
  }
  /** 搜索按钮操作 */
  function handleQuery() {
    getList()
  }
  /** 重置按钮操作 */
  function resetQuery() {
    proxy.resetForm("queryRef")
    handleQuery()
  }
  /** 新增按钮操作 */
  function handleAdd(row) {
    reset()
    getTreeselect()
    if (row != null && row.catalogueId) {
      form.value.parentId = row.catalogueId
    } else {
      form.value.parentId = 0
    }
    open.value = true
    title.value = "添加目录"
  }
  /** 展开/折叠操作 */
  function toggleExpandAll() {
    refreshTable.value = false
    isExpandAll.value = !isExpandAll.value
    nextTick(() => {
      refreshTable.value = true
    })
  }
  /** 修改按钮操作 */
  async function handleUpdate(row) {
    reset()
    await getTreeselect()
    getCatalogue(row.catalogueId).then(response => {
      form.value = response.data
      open.value = true
      title.value = "修改目录"
    })
  }
  /** 提交按钮 */
  function submitForm() {
    proxy.$refs["catalogueRef"].validate(valid => {
      if (valid) {
        if (form.value.catalogueId != undefined) {
          updateCatalogue(form.value).then(response => {
            proxy.$modal.msgSuccess("修改成功")
            open.value = false
            getList()
          })
        } else {
          addCatalogue(form.value).then(response => {
            proxy.$modal.msgSuccess("新增成功")
            open.value = false
            getList()
          })
        }
      }
    })
  }
  /** 删除按钮操作 */
  function handleDelete(row) {
    proxy.$modal
      .confirm('是否确认删除名称为"' + row.catalogueName + '"的数据项?')
      .then(function () {
        return delCatalogue(row.catalogueId)
      })
      .then(() => {
        getList()
        proxy.$modal.msgSuccess("删除成功")
      })
      .catch(() => {})
  }

  getList()
</script>
