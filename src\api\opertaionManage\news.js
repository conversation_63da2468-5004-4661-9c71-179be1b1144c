/*
 * @Description: 新闻管理相关API
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-04-10 13:11:02
 * @LastEditTime: 2023-04-10 13:25:19
 */
import request from "@/utils/request"
// 查询新闻列表
export function listNews(query) {
  return request({
    url: "/devops/news/list",
    method: "get",
    params: query
  })
}

// 查询新闻详细
export function getNews(newsId) {
  return request({
    url: "/devops/news/" + newsId,
    method: "get"
  })
}

// 新增新闻
export function addNews(data) {
  return request({
    url: "/devops/news",
    method: "post",
    data: data
  })
}

// 修改新闻
export function updateNews(data) {
  return request({
    url: "/devops/news",
    method: "put",
    data: data
  })
}

// 删除新闻
export function delNews(newsId) {
  return request({
    url: "/devops/news/" + newsId,
    method: "delete"
  })
}
