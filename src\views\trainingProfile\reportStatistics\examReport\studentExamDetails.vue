<!--
 * @Description: 学员考试明细
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-08-09 13:54:55
 * @LastEditTime: 2025-01-17 11:50:12
-->

<template>
  <div class="app-container">
    <el-page-header
      class="home_process_header"
      @back="handleBack"
      :content="`考试名称： ${row.examName}`"
    ></el-page-header>

    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
    >
      <el-form-item label="姓名" prop="userName">
        <el-input
          v-model="queryParams.userName"
          placeholder="姓名"
          clearable
          style="width: 240px"
          @keyup.enter="getList"
        />
      </el-form-item>
      <el-form-item
        label="所属分公司"
        prop="domainName"
        v-if="roles.includes('taibao_admin') || user.userId === 1"
      >
        <el-tree-select
          v-model="queryPara<PERSON>.domainName"
          :data="deptOptions"
          :props="{ value: 'domainName', label: 'label', children: 'children' }"
          value-key="id"
          placeholder="请选择所属分公司"
          check-strictly
          clearable
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="getList">
          搜索
        </el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb-8px">
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport">
          导出
        </el-button>
      </el-col>
      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table :data="tableData" v-loading="loading">
      <el-table-column label="序号" width="60" type="index" />
      <el-table-column label="姓名" prop="userName" width="100" />
      <el-table-column label="所属部门" prop="deptName" />
      <el-table-column label="上级部门" prop="higherDeptName" />
      <el-table-column label="考试成绩" prop="userPaperScore" width="80" />
      <el-table-column label="开始时间" prop="startTime" width="150" />
      <el-table-column label="交卷时间" prop="submitTime" width="150" />
      <el-table-column label="考试用时" prop="examTime" width="120">
        <template #default="scope">
          {{ formatSeconds(scope.row.examTime) }}
        </template>
      </el-table-column>
      <el-table-column label="切屏次数" prop="frequency" width="80" />
      <el-table-column label="是否通过" prop="isPass" width="100">
        <template #default="scope">
          <el-tag type="success" v-if="scope.row.isPass === '通过'">
            通过
          </el-tag>
          <el-tag type="danger" v-else>
            {{ scope.row.isPass }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="考试次数" prop="examCount" width="80" />
      <el-table-column label="名次" prop="ranking" width="80" />

      <el-table-column
        label="操作"
        width="130"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-button
            link
            type="primary"
            icon="View"
            @click="handleExamPaperDetail(scope.row)"
          >
            详情
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script setup name="studentExamDetails">
  import { deptTreeSelect } from "@/api/system/user"
  import { getExamTakers } from "@/api/onlineExam/exam"
  import { formatSeconds } from "@/utils/common.js"
  import useUserStore from "@/store/modules/user"

  const { roles, user } = storeToRefs(useUserStore())
  const { proxy } = getCurrentInstance()
  const emit = defineEmits(["updateCurrentView"])
  const props = defineProps({
    row: {
      type: Object,
      default: () => ({})
    }
  })

  const tableData = ref([])
  const loading = ref(true)
  const showSearch = ref(true)
  const total = ref(0)

  const data = reactive({
    queryParams: {
      pageNum: 1,
      pageSize: 10
    }
  })

  const { queryParams } = toRefs(data)

  /** 查询目录列表 */
  function getList() {
    loading.value = true
    let queryData = {
      ...queryParams.value,
      arrangeId: props.row.arrangeId
    }
    getExamTakers(queryData).then(response => {
      tableData.value = response.rows || []
      total.value = response.total
      loading.value = false
    })
  }

  const handleBack = () => {
    emit("updateCurrentView", "examReport")
  }

  /** 重置按钮操作 */
  function resetQuery() {
    proxy.resetForm("queryRef")
    getList()
  }

  const handleExamPaperDetail = row => {
    if (!row.paperAnswerId)
      return proxy.$modal.msgWarning("该人员还未参与考试，暂无法查看详情")
    emit("updateCurrentView", "examPaperDetails", {
      ...row,
      arrangeId: props.row.arrangeId,
      examName: props.row.examName
    })
  }

  /** 导出按钮操作 */
  function handleExport() {
    proxy.download(
      "exam/arrange/examTakers/export",
      {
        ...queryParams.value,
        arrangeId: props.row.arrangeId
      },
      `examTakers_${new Date().getTime()}.xlsx`
    )
  }
  const deptOptions = ref([])
  /** 查询部门下拉树结构 */
  const getBranchOfficeTree = async () => {
    const response = await deptTreeSelect({
      parentId: 100
    })
    deptOptions.value = response.data
  }

  getList()
  getBranchOfficeTree()
</script>

<style lang="scss" scoped>
  .backIcon {
    svg {
      height: 0.7em;
    }
  }
</style>
