<!--
 * @Description: 新增/修改轮播图弹窗
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-03-14 13:41:32
 * @LastEditTime: 2023-03-14 16:35:08
-->
<template>
  <el-dialog
    v-model="visible"
    :title="!dataForm.carouselId ? '新增' : '修改'"
    :close-on-click-modal="false"
  >
    <el-form
      ref="dataFormRef"
      :model="dataForm"
      :rules="dataRules"
      label-width="100px"
      @keyup.enter="submitHandle()"
    >
      <el-form-item label="名称" prop="name">
        <el-input v-model="dataForm.name" placeholder="亮点名称"></el-input>
      </el-form-item>
      <el-form-item label="显示名称" prop="realName">
        <el-input v-model="dataForm.realName" placeholder="显示名称"></el-input>
      </el-form-item>
      <el-form-item label="标记" prop="flag">
        <el-input v-model="dataForm.flag" placeholder="标记"></el-input>
      </el-form-item>
      <el-form-item label="备注" prop="mark">
        <el-input type="textarea" v-model="dataForm.mark"></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="submitHandle()">确定</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import {
  getSwiper,
  addSwiper,
  updateSwiper
} from "@/api/opertaionManage/swiper.js"

const emit = defineEmits(["refreshDataList"])

const { proxy } = getCurrentInstance()
const visible = ref(false)
const dataFormRef = ref()

let dataForm = reactive({})

const openDialog = (id) => {
  visible.value = true
  dataForm.id = ""

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields()
  }

  if (id) {
    getCarousel(id)
  }
}

const getCarousel = (id) => {
  getSwiper(id).then((res) => {
    Object.assign(dataForm, res.data)
  })
}

const dataRules = ref({
  name: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
  realName: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
  flag: [{ required: true, message: "必填项不能为空", trigger: "blur" }]
})

// 表单提交
const submitHandle = () => {
  dataFormRef.value.validate((valid) => {
    if (!valid) {
      return false
    }
    if (!dataForm.carouselId) {
      addSwiper(dataForm).then((res) => {
        if (res.code === 200) {
          proxy.$modal.msgSuccess("新增成功")
          visible.value = false
          emit("refreshDataList")
        }
      })
    } else {
      updateSwiper(dataForm).then((res) => {
        if (res.code === 200) {
          proxy.$modal.msgSuccess("修改成功")
          visible.value = false
          emit("refreshDataList")
        }
      })
    }
  })
}

defineExpose({
  openDialog
})
</script>
