<!--
 * @Description: 
 * @Author: <PERSON><PERSON><PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-09-08 15:40:09
 * @LastEditTime: 2023-10-18 16:03:49
-->
<template>
  <div class="target-view">
    <div>
      <span class="title">任务分布</span>

      <el-date-picker
        style="width: 260px; float: right; margin-right: 10px"
        v-model="dateRange"
        value-format="YYYY-MM-DD"
        type="daterange"
        range-separator="-"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        @change="selectDate"
      ></el-date-picker>
    </div>
    <div class="view-con">
      <Echarts :option="option" />
    </div>
  </div>
</template>

<script setup>
  import { getTaskDashboard } from "@/api/home/<USER>"

  const option = ref(null)

  let date = new Date()
  let year = date.getFullYear()
  let month =
    date.getMonth() + 1 < 10 ? "0" + (date.getMonth() + 1) : date.getMonth() + 1
  let day = date.getDate() < 10 ? "0" + date.getDate() : date.getDate()
  let startDate = year + "-" + month + "-" + "01"
  let endDate = year + "-" + month + "-" + day
  let dateRange = ref([startDate, endDate])

  const getTarget = () => {
    getTaskDashboard({
      queryTimeFrom: dateRange.value[0],
      queryTimeTo: dateRange.value[1]
    }).then(res => {
      setOption(res.data)
    })
  }

  const selectDate = () => {
    getTarget()
  }

  getTarget()
  const setOption = data => {
    let text = String(data.totalTask)
    let tx = "28%"

    if (text.length === 2) {
      tx = "25%"
    } else if (text.length === 3) {
      tx = "24%"
    }
    let color = ["#8388fa", "#ffcc33", "#50d6c0", "#EC808D"]
    option.value = {
      tooltip: {
        trigger: "item"
      },
      color,
      legend: {
        orient: "vertical",
        left: "55%",
        top: "20%",
        itemGap: 25,
        itemWidth: 10,
        icon: "circle",
        itemHeight: 10,
        textStyle: {
          fontSize: 14
        },

        formatter: function (name) {
          if (data.totalTask === 0) {
            return name
          }
          if (name === "全部任务") {
            return (
              name +
              "   " +
              ((data.totalTask / data.totalTask) * 100).toFixed(0) +
              "%" +
              "    " +
              data.totalTask
            )
          } else if (name === "未开始") {
            return (
              name +
              "       " +
              ((data.notStartedTask / data.totalTask) * 100).toFixed(0) +
              "%" +
              "      " +
              data.notStartedTask
            )
          } else if (name === "进行中") {
            return (
              name +
              "       " +
              ((data.inProgressTask / data.totalTask) * 100).toFixed(0) +
              "%" +
              "      " +
              data.inProgressTask
            )
          } else if (name === "已超期") {
            return (
              name +
              "       " +
              ((data.expiredTask / data.totalTask) * 100).toFixed(0) +
              "%" +
              "      " +
              data.expiredTask
            )
          }
        },
        data: [
          {
            name: "全部任务",
            icon: "circle",
            textStyle: {
              color: color[0]
            }
          },
          {
            name: "未开始",
            icon: "circle",
            textStyle: {
              color: color[1]
            }
          },
          {
            name: "进行中",
            icon: "circle",
            textStyle: {
              color: color[2]
            }
          },
          {
            name: "已超期",
            icon: "circle",
            textStyle: {
              color: color[3]
            }
          }
        ]
      },
      title: {
        text,
        textStyle: {
          fontSize: 36,
          color: "#7f8af5",
          fontWeight: 400
        },

        x: tx,
        y: "40%"
      },

      series: [
        {
          name: "背景",
          type: "pie",
          radius: ["46%", "68%"],
          center: ["30%", "50%"],
          emphasis: {
            scale: false
          },
          z: 1,
          label: {
            show: false
          },
          tooltip: {
            show: false
          },
          data: [{ value: "0", itemStyle: { color: "#fff" } }],
          itemStyle: {
            shadowColor: "rgba(0, 0, 0, 0.2)",
            shadowBlur: 4
            /* shadowOffsetX: 1,
            shadowOffsetY: 1 */
          }
        },
        {
          name: "任务分布",
          type: "pie",
          radius: ["50%", "65%"],
          center: ["30%", "50%"],

          label: {
            show: false
          },
          z: 3,
          data: [
            { value: data.totalTask, name: "全部任务" },
            { value: data.notStartedTask, name: "未开始" },
            { value: data.inProgressTask, name: "进行中" },
            { value: data.expiredTask, name: "已超期" }
          ]
        }
      ]
    }
  }
</script>

<style scoped lang="scss">
  .title {
    line-height: 40px;
    font-weight: bold;
    margin-left: 10px;
  }
  .view-con {
    background: #fff;
    padding: 14px 20px;
    border-radius: 10px;
    height: 298px;
  }
</style>
