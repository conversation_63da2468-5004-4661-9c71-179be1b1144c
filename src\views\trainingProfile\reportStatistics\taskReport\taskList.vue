<!--
 * @Description: 任务实施报表任务列表
 * @Author: <PERSON><PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-08-11 08:55:20
 * @LastEditTime: 2024-03-05 16:53:34
-->
<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
    >
      <el-form-item label="任务名称" prop="taskName">
        <el-input
          v-model="queryParams.taskName"
          placeholder="任务名称"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="考试时间" prop="dateRange">
        <el-date-picker
          v-model="dateRange"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="selectDate"
        ></el-date-picker>
      </el-form-item>

      <el-form-item
        label="所属分公司"
        prop="tenantId"
        v-if="roles.includes('taibao_admin') || user.userId === 1"
      >
        <el-tree-select
          v-model="queryParams.tenantId"
          :data="deptOptions"
          :props="{ value: 'domainName', label: 'label', children: 'children' }"
          value-key="id"
          placeholder="请选择所属分公司"
          check-strictly
          clearable
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb-8px">
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="tableData">
      <el-table-column label="任务名称" prop="taskName" />
      <el-table-column label="课程数" prop="courseCount" />
      <el-table-column label="考试数" prop="examCount" />
      <el-table-column label="问卷数" prop="questionnaireCount" />
      <el-table-column label="资料数" prop="manageCount" />
      <el-table-column label="开始时间" prop="startTime" />
      <el-table-column label="结束时间" prop="endTime" />
      <el-table-column label="参与人数" prop="participants" />
      <el-table-column label="完成人数" prop="completedCount" />
      <el-table-column label="完成率" prop="completionRate">
        <template #default="scope">
          {{ (scope.row?.completionRate * 100).toFixed(2) || 0 }}%
        </template>
      </el-table-column>
      <el-table-column label="操作" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button
            link
            type="primary"
            icon="View"
            @click="handleTrack(scope.row)"
            >明细</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script setup name="taskList">
  import { taskList } from "@/api/task/task.js"
  import dayjs from "dayjs"
  import { deptTreeSelect } from "@/api/system/user"
  import useUserStore from "@/store/modules/user"

  const { roles, user } = storeToRefs(useUserStore())
  const { proxy } = getCurrentInstance()
  const tableData = ref([])
  const loading = ref(true)
  const showSearch = ref(true)
  const total = ref(0)
  const dateRange = ref([])

  const emit = defineEmits(["updateCurrentView"])

  const data = reactive({
    queryParams: {
      pageNum: 1,
      pageSize: 10
    }
  })

  const { queryParams } = toRefs(data)

  /** 查询目录列表 */
  function getList() {
    loading.value = true
    taskList(queryParams.value).then(response => {
      tableData.value = response.rows || []
      total.value = response.total
      loading.value = false
    })
  }
  /** 搜索按钮操作 */
  function handleQuery() {
    getList()
  }
  /** 重置按钮操作 */
  function resetQuery() {
    dateRange.value = []
    proxy.resetForm("queryRef")
    handleQuery()
  }

  //选择日期
  const selectDate = () => {
    if (dateRange.value != null) {
      queryParams.value.queryTimeFrom = dateRange.value[0]
      queryParams.value.queryTimeTo = dateRange.value[1]
    } else {
      queryParams.value.queryTimeFrom = ""
      queryParams.value.queryTimeTo = ""
    }
  }

  const handleTrack = row => {
    emit("updateCurrentView", "taskDetail", row)
  }

  /** 导出按钮操作 */
  function handleExport() {
    proxy.download(
      "course/task/export/taskList",
      {
        ...queryParams.value
      },
      `任务实施报表_${dayjs().format("YYYY-MM-DD")}.xlsx`
    )
  }
  const deptOptions = ref([])
  /** 查询部门下拉树结构 */
  const getBranchOfficeTree = async () => {
    const response = await deptTreeSelect({
      parentId: 100
    })
    deptOptions.value = response.data
  }

  getList()
  if (roles.value.includes("taibao_admin") || user.value.userId === 1) {
    getBranchOfficeTree()
  }
</script>

<style lang="scss" scoped></style>
