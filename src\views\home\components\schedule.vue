<!--
 * @Description: 首页-日历
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-04-11 16:39:21
 * @LastEditTime: 2023-04-12 11:51:40
-->
<template>
  <div>
    <el-calendar
      v-model:value="calendarValue"
      :fullscreen="false"
      @select="onPanelSelect"
    />
    <el-card :bordered="false">
      <el-timeline>
        <el-timeline-item :key="schedule.id" v-for="schedule in scheduleList">
          {{ schedule.scheduleTime }} {{ schedule.scheduleContent }}
          <a style="float: right"
            ><delete-outlined @click="deleteSchedules(schedule)"
          /></a>
        </el-timeline-item>
      </el-timeline>
    </el-card>
  </div>
</template>

<script setup name="schedule">
import dayjs from "dayjs"
const scheduleList = ref([])
const calendarValue = ref(dayjs())

// 点击某一天
const onPanelSelect = () => {
  seleScheduleList()
}
</script>

<style scoped>
.add-schedule {
  cursor: pointer;
  margin-top: -10px;
}
</style>
