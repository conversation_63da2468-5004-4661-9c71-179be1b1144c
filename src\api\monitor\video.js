/*
 * @Description:
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-12-06 15:31:08
 * @LastEditTime: 2024-12-06 15:31:24
 */
import request from "@/utils/request"

// 查询视频监控列表
export function videoMonitorList(query) {
  return request({
    url: "/system/monitor/list",
    method: "get",
    params: query
  })
}

// 查询视频监控详情列表
// 传userId、taskId
export function videoMonitorDetailList(query) {
  return request({
    url: "/system/monitor/detail-list",
    method: "get",
    params: query
  })
}
