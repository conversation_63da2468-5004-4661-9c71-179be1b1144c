<!--
 * @Description: 试题列表页面
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-02-27 13:40:20
 * @LastEditTime: 2023-12-11 13:47:40
-->
<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
    >
      <el-form-item label="试题类型" prop="questionType">
        <el-select
          v-model="queryParams.questionType"
          placeholder="试题类型"
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="dict in exam_question_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="试题目录" prop="catalogueId">
        <el-tree-select
          v-model="queryParams.catalogueId"
          :data="catalogueOptions"
          :props="{
            value: 'catalogueId',
            label: 'catalogueName',
            children: 'children',
            disabled: 'disabled'
          }"
          clearable
          value-key="catalogueId"
          placeholder="选择试题目录"
          check-strictly
          default-expand-all
        />
      </el-form-item>
      <el-form-item label="题干" prop="questionName">
        <el-input
          v-model="queryParams.questionName"
          placeholder="请输入题干"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb-8px">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd">
          新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="info" plain icon="Upload" @click="handleImport">
          导入
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport">
          导出
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
        >
          删除
        </el-button>
      </el-col>
      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="tableData"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column
        prop="questionId"
        label="Id"
        width="80"
      ></el-table-column>
      <el-table-column label="试题类型" width="100" prop="questionType">
        <template #default="scope">
          <dict-tag
            :options="exam_question_type"
            :value="scope.row.questionType"
          />
        </template>
      </el-table-column>
      <el-table-column label="所属目录" width="150" prop="catalogueName">
      </el-table-column>
      <el-table-column label="题干" prop="questionName"> </el-table-column>
      <el-table-column label="难度" width="60" prop="degreeType">
        <template #default="scope">
          <dict-tag :options="exam_degree_type" :value="scope.row.degreeType" />
        </template>
      </el-table-column>
      <el-table-column label="创建时间" width="180" prop="createTime">
      </el-table-column>
      <el-table-column
        label="操作"
        width="200"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-button
            link
            type="primary"
            icon="Edit"
            @click="handleUpdate(scope.row)"
            >修改</el-button
          >
          <el-button
            link
            type="primary"
            icon="Delete"
            @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 试题导入对话框 -->
    <ExcelUpload
      ref="excelUploadRef"
      v-bind="upload"
      @uploadSuccess="getList"
    />
  </div>
</template>

<script setup name="questionList">
  import { catalogueList } from "@/api/system/catalogue"
  import catalogue from "@/utils/catalogue.js"
  import ExcelUpload from "@/components/ExcelUpload/index.vue"
  import { delQuestion, listQuestion } from "@/api/onlineExam/question"

  const { proxy } = getCurrentInstance()
  const { exam_question_type, exam_degree_type } = proxy.useDict(
    "exam_question_type",
    "exam_degree_type"
  )

  const catalogueOptions = ref([])
  const tableData = ref([])
  const loading = ref(true)
  const showSearch = ref(true)
  const ids = ref([])
  const multiple = ref(true)
  const total = ref(0)
  /*** 试题导入参数 */
  const upload = reactive({
    title: "试题",
    // 是否更新已经存在的用户数据
    updateSupport: 0,
    // 上传的地址
    url: "/exam/question/importQuestion",
    // 模板下载地址
    templateUrl: "/exam/question/importTemplate",
    // 模板名称前缀
    templateName: "question"
  })

  const data = reactive({
    queryParams: {
      pageNum: 1,
      pageSize: 10
    }
  })

  const { queryParams } = toRefs(data)

  /** 查询目录列表 */
  function getList() {
    loading.value = true
    listQuestion(queryParams.value).then(response => {
      tableData.value = response.rows || []
      total.value = response.total
      loading.value = false
    })
  }
  /** 搜索按钮操作 */
  function handleQuery() {
    getList()
  }
  /** 重置按钮操作 */
  function resetQuery() {
    proxy.resetForm("queryRef")
    handleQuery()
  }
  const handleAdd = () => {
    proxy.$tab.closeOpenPage({
      path: "/onlineExam/questionAdd"
    })
  }
  /** 修改按钮操作 */
  function handleUpdate(row) {
    proxy.$tab.closeOpenPage({
      path: "/onlineExam/questionAdd",
      query: row
    })
  }
  /** 删除按钮操作 */
  function handleDelete(row) {
    const questionIds = row.questionId || ids.value
    proxy.$modal
      .confirm('是否确认删除试题编号为"' + questionIds + '"的数据项?')
      .then(function () {
        return delQuestion(questionIds)
      })
      .then(() => {
        getList()
        proxy.$modal.msgSuccess("删除成功")
      })
      .catch(() => {})
  }

  /** 多选框选中数据 */
  function handleSelectionChange(selection) {
    ids.value = selection.map(item => item.questionId)
    multiple.value = !selection.length
  }

  /** 导入按钮操作 */
  function handleImport() {
    proxy.$refs["excelUploadRef"].openDialog()
  }
  /** 查询菜单下拉树结构 */
  function getTreeselect() {
    catalogueOptions.value = []
    catalogueList({ catalogueType: catalogue.SUBJECT_CATALOGUE }).then(
      response => {
        const catalogueTree = {
          catalogueId: 0,
          catalogueName: "主类目",
          children: [],
          disabled: true
        }
        catalogueTree.children = proxy.handleTree(response.rows, "catalogueId")
        catalogueOptions.value.push(catalogueTree)
      }
    )
  }
  /** 导出按钮操作 */
  function handleExport() {
    proxy.download(
      "exam/question/export",
      {
        ...queryParams.value
      },
      `试题导出_${new Date().getTime()}.xlsx`
    )
  }

  getList()
  getTreeselect()
</script>
