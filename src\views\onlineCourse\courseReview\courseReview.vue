<!--
 * @Description: 课程审批页面
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-09-06 14:24:54
 * @LastEditTime: 2024-11-28 11:18:19
-->
<template>
  <div class="app-container">
    <el-button
      class="mb-8px"
      type="primary"
      icon="ArrowLeftBold"
      size="20"
      @click="handleBack"
    >
      返 回
    </el-button>
    <el-form :model="form" ref="formRef" label-width="100px" :rules="rules">
      <el-form-item label="课程名称" prop="courseName">
        <el-input v-model="form.courseName" disabled />
      </el-form-item>
      <el-form-item label="课程目录" prop="catalogueId">
        <el-tree-select
          v-model="form.catalogueId"
          :data="catalogueOptions"
          :props="{
            value: 'catalogueId',
            label: 'catalogueName',
            children: 'children',
            disabled: 'disabled'
          }"
          clearable
          value-key="catalogueId"
          placeholder="选择课程目录"
          check-strictly
          @current-change="catalogueSelect"
          default-expand-all
          disabled
          multiple
        />
      </el-form-item>
      <el-form-item label="课程编号" prop="courseCode">
        <el-input v-model="form.courseCode" disabled />
      </el-form-item>
      <el-form-item label="课程类型" prop="courseType">
        <el-select
          v-model="form.courseType"
          clearable
          style="width: 200px"
          disabled
          multiple
        >
          <el-option
            v-for="dict in pedu_course_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="课程封面" prop="courseImage">
        <ImageUpload v-model="form.courseImage" :limit="1" disabled />
      </el-form-item>
      <el-form-item label="课程等级" prop="courseLevel">
        <el-select
          v-model="form.courseLevel"
          clearable
          style="width: 200px"
          disabled
        >
          <el-option
            v-for="dict in course_level_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="课程标签" prop="courseLabel">
        <el-select
          v-model="form.courseLabel"
          clearable
          disabled
          style="width: 200px"
          multiple
        >
          <el-option
            v-for="dict in course_label_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="课程价格" prop="coursePrice">
        <el-input
          maxlength="8"
          @input="v => (form.coursePrice = v.replace(/[^\d.]/g, ''))"
          v-model="form.coursePrice"
          disabled
        >
          <template #append>元</template>
        </el-input>
      </el-form-item>
      <el-form-item label="讲师" prop="lecturer">
        <el-input v-model="form.lecturer" disabled />
      </el-form-item>
      <el-form-item label="上架时间" prop="onShelfTime">
        <el-date-picker
          v-model="form.onShelfTime"
          type="date"
          disabled
          placeholder="上架时间"
          value-format="YYYY-MM-DD"
        />
      </el-form-item>
      <el-form-item label="下架时间" prop="offShelfTime">
        <el-date-picker
          v-model="form.offShelfTime"
          type="date"
          disabled
          placeholder="下架时间"
          value-format="YYYY-MM-DD"
        />
      </el-form-item>
      <el-form-item label="课程介绍" prop="courseIntroduction">
        <el-input
          v-model="form.courseIntroduction"
          :rows="2"
          maxlength="100"
          type="textarea"
          disabled
        />
      </el-form-item>
      <!-- <el-form-item label="课程大纲" prop="curriculum">
        <el-input
          v-model="form.curriculum"
          :rows="2"
          maxlength="100"
          type="textarea"
          disabled
        />
      </el-form-item>
      <el-form-item label="课程目标" prop="courseObjectives">
        <el-input
          v-model="form.courseObjectives"
          :rows="2"
          maxlength="100"
          type="textarea"
          disabled
        />
      </el-form-item>
      <el-form-item label="选修设置" prop="optionalSettings">
        <el-radio-group v-model="form.optionalSettings" disabled>
          <el-radio-button label="K">开放选修</el-radio-button>
          <el-radio-button label="F">范围选修</el-radio-button>
        </el-radio-group>
      </el-form-item> -->
      <el-form-item label="学分" prop="credits">
        <el-input-number
          v-model="form.credits"
          :step="0.1"
          :precision="1"
          disabled
          :min="0"
          :max="999"
        />
      </el-form-item>
      <el-form-item label="学时" prop="creditHours">
        <el-input-number
          v-model="form.creditHours"
          :step="0.1"
          :precision="1"
          disabled
          :min="0"
          :max="999"
        />
      </el-form-item>
      <!-- <el-form-item label="积分" prop="integral">
        <el-input-number
          v-model="form.integral"
          :step="0.1"
          :precision="1"
          disabled
          :min="0"
          :max="999"
        />
      </el-form-item> -->

      <el-form-item label="证书设置" prop="certificateName">
        <el-input v-model="form.certificateName" disabled />
      </el-form-item>

      <el-form-item label="是否开启人脸" prop="needSnapshot">
        <el-radio-group v-model="form.needSnapshot" disabled>
          <el-radio-button label="0">不启用</el-radio-button>
          <el-radio-button label="1">启用</el-radio-button>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="倍速播放" prop="multiplyPlayback">
        <el-radio-group v-model="form.multiplyPlayback" disabled>
          <el-radio-button label="1">始终允许</el-radio-button>
          <el-radio-button label="0">不允许</el-radio-button>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="进度条拖动" prop="progressBarDrag">
        <el-radio-group v-model="form.progressBarDrag" disabled>
          <el-radio-button label="1">始终允许</el-radio-button>
          <el-radio-button label="0">不允许</el-radio-button>
        </el-radio-group>
      </el-form-item>
      <!-- <el-form-item
        label="必修人员"
        prop="requiredUserName"
        label-width="106px"
      >
        <el-input
          v-model="form.requiredUserName"
          :rows="6"
          type="textarea"
          disabled
        />
      </el-form-item> -->
      <el-form-item label="关联的考试" prop="associatedExamsName">
        <el-input v-model="form.associatedExamsName" disabled />
      </el-form-item>
      <el-form-item label="关联试题" prop="ids">
        <el-input v-model="form.ids" disabled />
      </el-form-item>

      <el-form-item label="用户群体" prop="userGroups">
        <el-select
          v-model="form.userGroups"
          clearable
          style="width: 200px"
          disabled
          multiple
        >
          <el-option
            v-for="dict in course_user_groups"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="场所" prop="location">
        <el-select
          v-model="form.location"
          clearable
          style="width: 200px"
          disabled
          multiple
        >
          <el-option
            v-for="dict in course_location"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="form.remark"
          :rows="2"
          maxlength="200"
          type="textarea"
          disabled
        />
      </el-form-item>
      <el-row>
        <el-col :span="16">
          <el-table :data="passageList" style="margin-bottom: 20px">
            <el-table-column type="expand">
              <template #default="scope">
                <div
                  v-for="(item, index) in scope.row.videoFileList"
                  class="file-name"
                >
                  <p m="t-0 b-2" @click="reviewVideo(item)">
                    {{ index + 1 }}.
                    {{ item.fileName || getFileName(item.fileUrl) }}
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                    {{ formatSecondStr(item.passageDuration) }}
                  </p>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="coursePassageId" label="编号" width="120" />
            <el-table-column prop="videoType" label="类型" width="150">
              <template #default="scope">
                <dict-tag :options="video_type" :value="scope.row.videoType" />
              </template>
            </el-table-column>
            <el-table-column prop="coursePassageName" label="章节名称" />
          </el-table>
        </el-col>
      </el-row>
      <template v-if="flag === 'review'">
        <el-form-item label="审核结果" prop="approvalResults">
          <el-radio-group v-model="form.approvalResults">
            <el-radio :label="0">通过</el-radio>
            <el-radio :label="1">驳回</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="审核意见" prop="approvalRemark">
          <el-input
            v-model="form.approvalRemark"
            :rows="2"
            maxlength="200"
            type="textarea"
          />
        </el-form-item>
      </template>

      <el-form-item>
        <el-button v-if="flag === 'review'" type="primary" @click="submitForm">
          提交
        </el-button>
        <el-button @click="handleBack"> 返回 </el-button>
      </el-form-item>
    </el-form>

    <el-dialog
      v-model="dialogVideoVisible"
      title="视频预览"
      width="960"
      @close="videoPlayDialogClose"
    >
      <video
        ref="videoRef"
        controlslist="nodownload"
        width="900"
        height="600"
        controls
        :src="videoItem.fileUrl"
      ></video>
    </el-dialog>
  </div>
</template>

<script setup name="courseReview">
  import { getFileName, formatSecondStr } from "@/utils/common.js"
  import { getCourse, approvalCourse } from "@/api/onlineCourse/course.js"
  import { catalogueList } from "@/api/system/catalogue.js"
  import { listPassage } from "@/api/onlineCourse/passage.js"
  import catalogue from "@/utils/catalogue.js"
  import useUserStore from "@/store/modules/user"

  const emit = defineEmits(["updateCurrentView"])
  const props = defineProps({
    row: {
      type: Object,
      default: () => ({})
    }
  })
  const userStore = useUserStore()
  const { proxy } = getCurrentInstance()

  const {
    course_user_groups,
    course_location,
    course_type,
    pedu_course_type,
    video_type,
    course_label_type,
    course_level_type
  } = proxy.useDict(
    "course_user_groups",
    "course_location",
    "course_type",
    "pedu_course_type",
    "video_type",
    "course_label_type",
    "course_level_type"
  )

  const catalogueOptions = ref([])
  let passageList = ref([])

  const data = reactive({
    form: {
      coursePrice: 0
    }
  })

  let dialogVideoVisible = ref(false)
  let videoItem = ref()

  const { form } = toRefs(data)

  const rules = ref({
    approvalResults: [
      { required: true, message: "必填项不能为空", trigger: "blur" }
    ]
  })

  // 初始化时需要特殊处理的key数组
  const getProcessKeyArr = [
    "courseLabel",
    "courseType",
    "catalogueName",
    "catalogueId",
    "userGroups",
    "location"
  ]
  const flag = ref("")
  if (props.row) {
    flag.value = props.row.flag
    getCourse(props.row.courseId).then(res => {
      form.value = res.data
      // 批量将逗号分割的字符串转为数组
      getProcessKeyArr.forEach(key => {
        form.value[key] = form.value[key] ? form.value[key].split(",") : []
      })
      // 将目录ID数组中的元素都转为Number 解决回显问题
      form.value.catalogueId = form.value.catalogueId.map(Number)
      if (
        form.value.courseQuestionLinkList &&
        form.value.courseQuestionLinkList.length > 0
      ) {
        form.value.ids = form.value.courseQuestionLinkList.map(
          item => item.questionId
        )
      }
    })
  }

  // 监听标签，如果有免费则将价格重新赋值为0，以解决因为先输入价格在选择免费后不重新赋值为0的逻辑
  watch(
    () => form.value.courseLabel,
    value => {
      if (!value) return
      if (value.includes("0")) form.value.coursePrice = 0
    }
  )

  /** 查询菜单下拉树结构 */
  function getTreeselect() {
    catalogueOptions.value = []
    catalogueList({ catalogueType: catalogue.COURSE_CATALOGUE }).then(
      response => {
        const catalogueTree = {
          catalogueId: 0,
          catalogueName: "主类目",
          children: [],
          disabled: true
        }
        catalogueTree.children = proxy.handleTree(response.rows, "catalogueId")
        catalogueOptions.value.push(catalogueTree)
      }
    )
  }
  // 目录树select event
  const catalogueSelect = (data, node) => {
    form.value.catalogueName = data.catalogueName
  }

  // 数据提交
  const submitForm = async () => {
    let submitData = {
      courseId: form.value.courseId,
      courseStatus: form.value.courseStatus,
      approvalResults: form.value.approvalResults,
      approvalRemark: form.value.approvalRemark
    }
    const res = await approvalCourse(submitData)
    if (res.code === 200) {
      proxy.$modal.msgSuccess("操作成功")
      handleBack()
    }
  }

  // 获取章节列表
  const fetchData = async () => {
    let queryData = {
      courseId: props.row.courseId,
      pageSize: 999
    }
    const { rows } = await listPassage(queryData)
    passageList.value = rows
  }

  const handleBack = () => {
    emit("updateCurrentView", "courseReviewList", props.row.queryParams)
  }

  const reviewVideo = item => {
    dialogVideoVisible.value = true
    videoItem.value = item
  }

  const videoRef = ref(null)
  const videoPlayDialogClose = () => {
    videoRef.value && videoRef.value.pause()
  }

  fetchData()
  getTreeselect()
</script>

<style lang="scss" scoped>
  :deep(.el-radio-group) {
    display: flex;
  }
  :deep(.el-input-number .el-input__wrapper) {
    padding-left: 50px;
    padding-right: 50px;
  }
  :deep(.el-form-item__content) {
    display: block;
  }
  :deep(.el-input) {
    display: inline-block;
    width: auto;
  }
  :deep(.el-textarea__inner) {
    display: inline-block;
    width: 400px;
  }

  .extendButton {
    position: absolute;
    left: 420px;
    top: 50px;
  }

  .extendCerBtn {
    position: absolute;
    left: 220px;
    top: 0;
  }
  .createExamBtn {
    margin-left: 20px;
  }

  .chapter:before {
    content: "*";
    color: var(--el-color-danger);
    margin-top: 7px;
  }

  .item_error {
    color: var(--el-color-danger);
    font-size: 12px;
    line-height: 1;
    padding-top: 2px;
    position: absolute;
    top: 100%;
    left: 0;
  }

  .file-name {
    height: 30px;
    margin-left: 30px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    > :last-child {
      margin-right: 30px;
      color: #589cfd;
      cursor: pointer;
    }
  }
</style>
