<!--
 * @Description: 查看问卷结果列表
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-05-06 10:52:42
 * @LastEditTime: 2024-05-07 13:28:43
-->
<template>
  <div class="app-container">
    <el-row :gutter="10" class="mb-8px">
      <div class="home_process_header">
        <el-icon class="backIcon" size="20" @click="handleBack">
          <ArrowLeftBold />
        </el-icon>
        <el-divider direction="vertical"></el-divider>
        问卷：{{ row.surveyTitle }}
      </div>
    </el-row>
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
    >
      <el-form-item label="试题类型" prop="questionType">
        <el-select
          v-model="queryParams.questionType"
          placeholder="试题类型"
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="dict in questionnaire_question_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="题干" prop="questionName">
        <el-input
          v-model="queryParams.questionName"
          placeholder="请输入题干"
          clearable
          style="width: 240px"
          @keyup.enter="getList"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="getList"
          >搜索</el-button
        >
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb-8px">
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport">
          导出
        </el-button>
      </el-col>
    </el-row>

    <el-row :gutter="10" class="center-container">
      <el-col :span="4" class="survey-title">
        {{ row.surveyTitle }}
      </el-col>
      <el-col :span="4"> 问卷人数：{{ surveyCount }} </el-col>
      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="tableData">
      <el-table-column
        prop="traningQuestionId"
        label="Id"
        width="120"
      ></el-table-column>
      <el-table-column label="试题类型" width="200" prop="questionType">
        <template #default="scope">
          <dict-tag
            :options="questionnaire_question_type"
            :value="scope.row.questionType"
          />
        </template>
      </el-table-column>
      <el-table-column label="题干" prop="questionName"> </el-table-column>
      <el-table-column label="创建时间" width="280" prop="createTime">
      </el-table-column>
      <el-table-column
        label="操作"
        width="150"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-button link type="primary" @click="handleDetail(scope.row)"
            >详情</el-button
          >
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup name="resultList">
  import dayjs from "dayjs"
  import {
    listQuestionResult,
    getParticipantsCount
  } from "@/api/trainingImplement/questionnaire.js"

  const { proxy } = getCurrentInstance()
  const { questionnaire_question_type } = proxy.useDict(
    "questionnaire_question_type"
  )
  const emit = defineEmits(["updateCurrentView"])
  const props = defineProps({
    row: {
      type: Object,
      default: () => ({})
    }
  })

  const tableData = ref([])
  const loading = ref(true)
  const showSearch = ref(true)

  const queryParams = ref({
    questionType: "",
    questionName: ""
  })

  /** 查询目录列表 */
  async function getList() {
    loading.value = true
    let queryData = {
      ...queryParams.value,
      questionnaireId: props.row.questionnaireId
    }
    const response = await listQuestionResult(queryData)
    tableData.value = response.data || []
    loading.value = false
  }

  const surveyCount = ref(0)
  const getSurveyPersonCount = async () => {
    let queryData = {
      questionnaireId: props.row.questionnaireId
    }
    const res = await getParticipantsCount(queryData)
    surveyCount.value = res.data || 0
  }
  /** 重置按钮操作 */
  function resetQuery() {
    reset()
    getList()
  }

  const reset = () => {
    queryParams.value = {
      questionType: "",
      questionName: ""
    }
  }

  const handleBack = () => {
    emit("updateCurrentView", "questionnaireList")
  }

  const handleExport = () => {
    proxy.download(
      "course/answer/user-export",
      {
        questionnaireId: props.row.questionnaireId
      },
      `调查问卷结果_${dayjs().format("YYYY-MM-DD HH:mm:ss")}.xlsx`
    )
  }

  function handleDetail(row) {
    emit("updateCurrentView", "resultDetail", {
      ...row,
      surveyTitle: props.row.surveyTitle,
      questionnaireId: props.row.questionnaireId
    })
  }

  getList()
  getSurveyPersonCount()
</script>

<style lang="scss" scoped>
  .backIcon {
    svg {
      height: 0.7em;
    }
  }

  .center-container {
    font-size: 14px;
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    .survey-title {
      font-weight: bolder;
    }
  }
</style>
