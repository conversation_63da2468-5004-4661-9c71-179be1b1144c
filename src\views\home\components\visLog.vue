<!--
 * @Description: 首页-访问记录
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-04-11 09:59:45
 * @LastEditTime: 2023-06-28 11:29:04
-->
<template>
  <el-card :bordered="false">
    <template #header>
      <div class="header">
        <span class="title">访问记录</span>
        <a class="showMore" @click="leaveFor('/system/log/logininfor')">更多</a>
      </div>
    </template>
    <div class="timeline-div" v-if="visLogList.length > 0">
      <el-timeline>
        <el-timeline-item
          :key="visLog.id"
          v-for="visLog in visLogList"
          :color="getTimelineColor(visLog.msg)"
          >{{ visLog.accessTime }} &nbsp;&nbsp;&nbsp;{{ visLog.msg }}
          <p class="timeline-item-p">
            {{ visLog.ipaddr }}
          </p>
        </el-timeline-item>
      </el-timeline>
    </div>
    <el-empty v-else />
  </el-card>
</template>

<script setup name="indexVisLog">
  import router from "@/router"
  import { onMounted } from "vue"
  import useUserStore from "@/store/modules/user"
  import { list } from "@/api/system/logininfor"

  const userStore = useUserStore()
  const visLogList = ref([])
  const loading = ref(true)

  /** 查询登录日志 */
  function seleVisLogList() {
    loading.value = true
    list().then(response => {
      visLogList.value = response.rows
      loading.value = false
    })
  }

  onMounted(() => {
    // 进来后执行查询
    seleVisLogList()
  })
  // 是否展示更多按钮
  const displayMore = () => {
    return (
      userStore.roles && userStore.roles.toString().indexOf("superAdmin") !== -1
    )
  }
  // 查询数据
  // 跳转
  const leaveFor = (url = "/") => {
    router.replace({
      path: url
    })
  }
  // 获取颜色
  const getTimelineColor = value => {
    if (value === "登录成功") {
      return "blue"
    }
    if (value === "退出成功") {
      return "gray"
    }
  }
</script>
<style scoped lang="scss">
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .title {
      padding: 8px;
      font-size: 19px;
      font-weight: bolder;
    }

    .showMore {
      color: #1890ff;
      font-size: 15px;
      margin-right: 10px;
    }
  }
  .timeline-item-p {
    margin-bottom: 0px;
    color: rgb(188, 189, 190);
  }
  .timeline-div {
    height: 300px;
    /* overflow: auto; */
    overflow-y: scroll;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE 10+ */
  }
  .timeline-div::-webkit-scrollbar {
    display: none;
  }
</style>
