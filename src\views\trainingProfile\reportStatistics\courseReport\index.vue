<!--
 * @Description: 课程报表统计入口页面
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-10-11 09:48:01
 * @LastEditTime: 2023-10-11 09:50:41
-->
<template>
  <section class="index-container">
    <component
      :is="currentView"
      :row="row"
      @updateCurrentView="updateCurrentView"
    ></component>
  </section>
</template>

<script setup name="courseReportIndex">
  import courseReport from "./courseReport.vue"
  import reportDetails from "./reportDetails.vue"

  const row = ref({})
  const currentView = shallowRef(courseReport)

  const updateCurrentView = (view, item) => {
    row.value = item
    if (view === "reportDetails") {
      currentView.value = reportDetails
    } else if (view === "courseReport") {
      currentView.value = courseReport
    }
  }
</script>
