<template>
  <div class="app-container">
    <el-form
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="套餐名" prop="name">
        <el-input
          v-model="name"
          placeholder="请输入套餐名"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select
          v-model="status"
          placeholder="状态"
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="dict in sys_job_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间" style="width: 308px">
        <el-date-picker
          v-model="dateRange"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="selectDate"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb-8px">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd"
          >新增</el-button
        >
      </el-col>
      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="packageList">
      <el-table-column label="套餐编号" prop="packageCode" />
      <el-table-column label="套餐名" prop="name" />
      <el-table-column label="状态" prop="status">
        <template #default="scope">
          <dict-tag :options="sys_job_status" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="创建时间" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        width="240"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-button
            link
            type="primary"
            icon="Edit"
            @click="handleUpdate(scope.row)"
            >修改</el-button
          >
          <el-button
            link
            type="primary"
            icon="Delete"
            @click="handleDelete(scope.row)"
            >删除</el-button
          >
          <!-- <el-button
            link
            type="primary"
            icon="Edit"
            @click="showItemsDataHandle(scope.row)"
            >课程管理</el-button
          > -->
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="pageNum"
      v-model:limit="pageSize"
      @pagination="getList"
    />

    <addEditPackageDialog ref="addEditPackageDialogRef" @fetch-data="getList" />
    <CourseManageDrawer ref="courseManageDrawerRef" @fetch-data="getList" />
  </div>
</template>

<script setup name="PackageList">
  import { listPackage, delPackage } from "@/api/system/package"
  import CourseManageDrawer from "./components/courseManageDrawer.vue"
  import addEditPackageDialog from "./components/addEditPackageDialog.vue"

  const { proxy } = getCurrentInstance()
  const { sys_job_status } = proxy.useDict("sys_job_status")

  const loading = ref(false)
  const showSearch = ref(true)

  let pageNum = ref(1)
  let pageSize = ref(10)
  let total = ref(0)
  let packageList = ref()
  let name = ref("")
  let status = ref("")
  let dateRange = ref([])
  let queryTimeFrom = ref()
  let queryTimeTo = ref()

  const data = reactive({
    form: {}
  })

  const { form } = toRefs(data)

  /** 查询字典类型列表 */
  const getList = async () => {
    let queryData = {
      pageNum: pageNum.value,
      pageSize: pageSize.value,
      name: name.value,
      status: status.value,
      queryTimeFrom: queryTimeFrom.value,
      queryTimeTo: queryTimeTo.value
    }
    const res = await listPackage(queryData)
    packageList.value = res.rows
    total.value = res.total
  }
  /** 搜索按钮操作 */
  function handleQuery() {
    pageNum.value = 1
    getList()
  }
  /** 重置按钮操作 */
  function resetQuery() {
    name.value = ""
    status.value = ""
    queryTimeFrom.value = ""
    queryTimeTo.value = ""
    dateRange.value = []
    proxy.resetForm("queryRef")
    handleQuery()
  }
  /** 新增按钮操作 */
  function handleAdd() {
    proxy.$refs["addEditPackageDialogRef"].openDialog()
  }
  /** 修改按钮操作 */
  const handleUpdate = async row => {
    proxy.$refs["addEditPackageDialogRef"].openDialog(row.id)
  }
  /** 删除按钮操作 */
  function handleDelete(row) {
    const packageId = row.id
    proxy.$modal
      .confirm('是否确认删除套餐编号为"' + packageId + '"的数据项？')
      .then(function () {
        return delPackage(packageId)
      })
      .then(() => {
        getList()
        proxy.$modal.msgSuccess("删除成功")
      })
      .catch(() => {})
  }

  const courseManageDrawerRef = ref()
  const showItemsDataHandle = row => {
    courseManageDrawerRef.value.openDialog(row)
  }

  const selectDate = () => {
    if (dateRange.value != null) {
      queryTimeFrom.value = dateRange.value[0]
      queryTimeTo.value = dateRange.value[1]
    } else {
      queryTimeFrom.value = ""
      queryTimeTo.value = ""
    }
  }

  getList()
</script>
