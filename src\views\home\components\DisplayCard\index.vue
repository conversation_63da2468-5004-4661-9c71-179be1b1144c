<!--
 * @Description: DisplayCard
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-08-03 11:10:58
 * @LastEditTime: 2023-08-31 09:47:39
-->

<template>
  <el-card shadow="hover" class="display-card">
    <div class="display-item">
      <div class="display-title">
        <div class="title-left">
          <div class="circle" :style="`border: 3px solid ${randomColor()}`">
          </div
          >{{ displayData.label }}</div
        >
        <div class="title-right">{{ displayData.percent || 0 }}%</div>
      </div>
      <div class="display-num">
        <div class="num">{{ displayData.value || 0 }}</div>
        <div class="unit">{{ displayData.unit }}</div>
      </div>
    </div>
  </el-card>
</template>

<script setup name="displayCard">
  const props = defineProps({
    displayData: {
      type: Object,
      default: () => ({})
    }
  })
  // 颜色列表
  const colorList = [
    "#7265E6",
    "#FFBF00",
    "#00A2AE",
    "#F56A00",
    "#1890FF",
    "#606D80"
  ]
  // 获取随机颜色
  const randomColor = () => {
    if (props.displayData.color) {
      return props.displayData.color
    }
    return colorList[randomNum(0, colorList.length - 1)]
  }
  // 获取minNum到maxNum内的随机数
  const randomNum = (minNum, maxNum) => {
    switch (arguments.length) {
      case 1:
        return parseInt(Math.random() * minNum + 1, 10)
        // eslint-disable-next-line no-unreachable
        break
      case 2:
        return parseInt(Math.random() * (maxNum - minNum + 1) + minNum, 10)
        // eslint-disable-next-line no-unreachable
        break
      default:
        return 0
        // eslint-disable-next-line no-unreachable
        break
    }
  }
</script>

<style scoped lang="scss">
  .display-card {
    width: 300px;
    .display-item {
      font-size: 10px;
      color: #888a8d;
      .display-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 10px;

        .title-left {
          display: flex;
          justify-content: center;
          align-items: center;
          .circle {
            margin-right: 3px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
          }
        }
      }

      .display-num {
        color: #202020;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 28px;
        font-weight: bold;

        .unit {
          margin-left: 5px;
          font-size: 10px;
        }
      }
    }
  }
</style>
