<!--
 * @Description: 培训任务列表
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-05-25 09:37:33
 * @LastEditTime: 2025-07-04 11:52:14
-->

<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
      @submit.prevent
    >
      <el-form-item label="任务名称" prop="taskName">
        <el-input
          v-model="queryParams.taskName"
          placeholder="任务名称"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb-8px">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd">
          新增
        </el-button>
      </el-col>
      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="tableData">
      <el-table-column label="封面图片" prop="coverPhoto" width="130">
        <template #default="scope">
          <el-popover
            v-if="scope.row.coverPhoto"
            placement="right"
            width="400"
            trigger="hover"
          >
            <template #reference>
              <el-image
                style="width: 80px; height: 80px"
                :src="scope.row.coverPhoto"
              ></el-image>
            </template>
            <el-image :src="scope.row.coverPhoto"></el-image>
          </el-popover>
          <div v-else>--</div>
        </template>
      </el-table-column>
      <el-table-column label="任务名称" prop="taskName" />
      <el-table-column label="开始时间" prop="startTime" />
      <el-table-column label="结束时间" prop="endTime" />
      <el-table-column label="任务状态" prop="taskStatus" width="100">
        <template #default="scope">
          <dict-tag
            :options="training_task_status"
            :value="scope.row.taskStatus"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        width="320"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-button
            link
            type="primary"
            icon="Edit"
            @click="handleUpdate(scope.row)"
            >修改</el-button
          >
          <el-button
            link
            type="primary"
            icon="DocumentCopy"
            @click="handleCopy(scope.row)"
            >复制</el-button
          >
          <el-button
            link
            type="primary"
            icon="View"
            @click="handleTrack(scope.row)"
            >跟踪</el-button
          >
          <el-button
            link
            type="primary"
            icon="Delete"
            @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script setup name="trainingTaskList">
  import {
    delTrainingTask,
    listTrainingTask
  } from "@/api/trainingImplement/trainingTask.js"

  const { proxy } = getCurrentInstance()
  const { training_task_status } = proxy.useDict("training_task_status")

  const tableData = ref([])
  const loading = ref(true)
  const showSearch = ref(true)
  const total = ref(0)

  const emit = defineEmits(["updateCurrentView"])

  const data = reactive({
    queryParams: {
      pageNum: 1,
      pageSize: 10
    }
  })

  const { queryParams } = toRefs(data)

  /** 查询目录列表 */
  function getList() {
    loading.value = true
    listTrainingTask(queryParams.value).then(response => {
      tableData.value = response.rows || []
      total.value = response.total
      loading.value = false
    })
  }
  /** 搜索按钮操作 */
  function handleQuery() {
    getList()
  }
  /** 重置按钮操作 */
  function resetQuery() {
    proxy.resetForm("queryRef")
    handleQuery()
  }
  const handleAdd = () => {
    proxy.$tab.closeOpenPage({
      path: "/trainingImplement/trainingTaskAdd",
      query: {
        type: 'add'
      }
    })
  }
  /** 修改按钮操作 */
  function handleUpdate(row) {
    proxy.$tab.closeOpenPage({
      path: "/trainingImplement/trainingTaskAdd",
      query: {
        taskId: row.taskId,
        type: 'edit'
      }
    })
  }
  /** 删除按钮操作 */
  function handleDelete(row) {
    proxy.$modal
      .confirm('是否确认删除名称为"' + row.taskName + '"的数据项?')
      .then(function () {
        return delTrainingTask(row.taskId)
      })
      .then(() => {
        getList()
        proxy.$modal.msgSuccess("删除成功")
      })
      .catch(() => {})
  }

  const handleTrack = row => {
    emit("updateCurrentView", "taskDetail", row)
  }

  const handleCopy = row => {
    proxy.$tab.closeOpenPage({
      path: "/trainingImplement/trainingTaskAdd",
      query: {
        taskId: row.taskId,
        type: 'copy'
      }
    })
  }

  getList()
</script>
<style lang="scss" scoped></style>
