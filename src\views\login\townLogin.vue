<!--
 * @Description: 社区安全发展和防灾减灾登录页面
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-11-08 15:18:09
 * @LastEditTime: 2023-12-14 16:04:24
-->
<template>
  <div
    class="login"
    :style="
      tenantBgImg
        ? `background-image: url(${tenantBgImg}) !important`
        : `background-image: url(https://training-voc.obs.cn-north-4.myhuaweicloud.com/background.jpg) !important`
    "
  >
    <div class="login_main">
      <div class="logo" style="margin-bottom: 30px">
        <img
          class="logo-img"
          :src="
            tenantLogo ||
            'https://training-voc.obs.cn-north-4.myhuaweicloud.com/town_logo.png'
          "
          alt=""
        />
        <div class="title">安全发展和综合减灾知识平台</div>
      </div>
      <el-form
        ref="unepLoginRef"
        :model="loginForm"
        :rules="rules"
        class="login-form"
      >
        <!-- <div class="tabs">
          <div
            v-for="item in tabList"
            :class="{ active: activeTab === item.value }"
            @click="activeTab = item.value"
          >
            {{ item.label }}
          </div>
        </div> -->
        <div class="form-content">
          <template v-if="activeTab === '1'">
            <el-form-item prop="username">
              <el-input
                v-model="loginForm.username"
                type="text"
                size="large"
                auto-complete="off"
                placeholder="账号"
              >
                <template #prefix
                  ><svg-icon
                    icon-class="user"
                    class="el-input__icon input-icon"
                /></template>
              </el-input>
            </el-form-item>
            <el-form-item prop="password">
              <el-input
                v-model="loginForm.password"
                type="password"
                size="large"
                auto-complete="off"
                placeholder="密码"
                @keyup.enter="handleLogin"
              >
                <template #prefix
                  ><svg-icon
                    icon-class="password"
                    class="el-input__icon input-icon"
                /></template>
              </el-input>
            </el-form-item>
          </template>
          <template v-if="activeTab === '2'">
            <el-form-item prop="mobile">
              <div class="tel">
                <el-input
                  v-model="loginForm.mobile"
                  round
                  auto-complete="off"
                  placeholder="请输入您的手机号"
                  onfocus="this.placeholder=''"
                  onblur="this.placeholder='请输入您的手机号'"
                  ><template #prefix>
                    <div class="front-inner">
                      <div class="front-type">+86</div>
                      <el-divider direction="vertical"></el-divider>
                    </div>
                  </template>
                </el-input>
              </div>
            </el-form-item>
            <el-form-item prop="code">
              <div class="code">
                <el-input
                  v-model="loginForm.code"
                  auto-complete="off"
                  placeholder="请输入验证码"
                  @keyup.enter="handleLogin"
                >
                  <template #suffix>
                    <div class="end-inner">
                      <el-divider direction="vertical"></el-divider>
                      <div
                        class="end-type"
                        @click="getTelCode"
                        v-if="!sendMsgDisabled"
                        >获取验证码</div
                      >
                      <div class="end-interval" v-if="sendMsgDisabled">{{
                        time + "秒后获取"
                      }}</div>
                    </div>
                  </template>
                </el-input>
              </div>
            </el-form-item>
          </template>
          <el-form-item style="width: 100%">
            <el-button
              :loading="loading"
              size="large"
              type="primary"
              style="width: 100%; border-radius: 20px"
              @click.prevent="handleLogin"
              class="unepLoginButton"
            >
              <span v-if="!loading">登 录</span>
              <span v-else>登 录 中...</span>
            </el-button>
          </el-form-item>
          <div class="tip" v-if="activeTab === '2'"
            >未注册的手机号将默认开通平台账号</div
          >
        </div>
      </el-form>
    </div>
  </div>
</template>

<script setup>
  import { sendCode } from "@/api/login"
  import useUserStore from "@/store/modules/user"
  import { getToken } from "@/utils/auth"
  import useTenantStore from "@/store/modules/tenant"
  import { ElMessage } from "element-plus"

  const tenantStore = useTenantStore()
  const userStore = useUserStore()
  const router = useRouter()
  const { proxy } = getCurrentInstance()

  const { tenantBgImg, tenantLogo } = storeToRefs(tenantStore)
  const loginForm = ref({
    username: "",
    password: "",
    mobile: "",
    code: ""
  })

  const tabList = ref([
    {
      label: "企业登录",
      value: "1"
    },
    {
      label: "个人登录",
      value: "2"
    }
  ])
  const activeTab = ref("1")

  const rules = {
    username: [{ required: true, trigger: "blur", message: "请输入您的账号" }],
    password: [{ required: true, trigger: "blur", message: "请输入您的密码" }],
    code: [{ required: true, trigger: "change", message: "请输入验证码" }]
  }

  const loading = ref(false)
  // 注册开关
  const redirectUrl = ref(null)
  let time = ref(60)
  let sendMsgDisabled = ref(false)

  const handleLogin = () => {
    proxy.$refs.unepLoginRef.validate(valid => {
      if (valid) {
        loading.value = true
        // 调用action的登录方法
        if (activeTab.value === "1") {
          userStore
            .login(loginForm.value)
            .then(publicKey => {
              if (redirectUrl.value.indexOf("http") !== -1) {
                window.location.href = `${
                  redirectUrl.value
                }?token=${getToken()}`
              } else {
                router.push({ path: "/" })
              }
            })
            .catch(() => {
              loading.value = false
            })
        } else {
          loginForm.value.password = loginForm.value.code
          userStore
            .unepLogin(loginForm.value)
            .then(() => {
              if (redirectUrl.value.indexOf("http") !== -1) {
                window.location.href =
                  redirectUrl.value + "?" + "token=" + getToken()
              } else {
                router.push({ path: "/" })
              }
            })
            .catch(() => {
              loading.value = false
            })
        }
      }
    })
  }

  // redirect是获取哪个系统域名 比如:http://127.0.0.1:8080 方便登录成功以后跳转相应的系统
  if (window.location.href.indexOf("redirect") >= 0) {
    //如果url中包含redirect   split？分割成数组，取第二个
    let redirect = window.location.href.split("?")[1]
    redirect = redirect.substring(9) // 截取字符串第9位开始截取到最后
    redirectUrl.value = redirect
  }

  const getTelCode = async () => {
    let queryData = {
      mobile: loginForm.value.mobile,
      scene: 1
    }
    await sendCode(queryData)
    ElMessage({
      message: "验证码发送成功！请注意查看",
      type: "success"
    })
    sendMsgDisabled.value = true
    const interval = setInterval(() => {
      if (time.value-- <= 0) {
        time.value = 60
        sendMsgDisabled.value = false
        clearInterval(interval)
      }
    }, 1000)
  }
</script>

<style lang="scss" scoped>
  :deep(.el-input__wrapper) {
    // background-color: #f2f2f2;
    width: 298px;
    margin-left: 0 !important;
    border-radius: 20px !important;
  }
  .login {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    background-size: cover;

    .login_main {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      position: absolute;
      top: 150px;
      right: 25%;
      padding: 30px 0;
    }
  }
  .logo {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    .title {
      max-height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      color: #ee1d23;
      font-size: 24px;
      font-weight: 700;
      height: 20px;
      margin-bottom: 18px;
    }
    .logo-img {
      max-height: 80px;
      max-width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      margin: 0px auto 30px auto;
      text-align: center;
      color: #707070;
    }
  }

  .login-form {
    border-radius: 10px;
    background: #ffffff;
    width: 400px;
    border: 1px solid #dcdfe6;
    box-shadow: 0px 0px 6px rgba(0, 0, 0, 0.34901960784313724);

    .tabs {
      display: flex;
      border-radius: 10px 10px 0 0;
      background-color: #f2f2f2;
      div {
        display: flex;
        justify-content: center;
        align-items: center;
        flex: 1;
        height: 50px;
        color: #aaaaaa;
        font-weight: bold;
        cursor: pointer;
        font-size: 18px;
      }

      .active {
        border-radius: 10px 10px 0 0;
        background-color: #fff;
        color: #409eff;
      }
    }

    .form-content {
      margin-top: 30px;
      padding: 30px 50px;
    }
    .el-input {
      height: 40px;
      input {
        height: 40px;
      }
    }
    .input-icon {
      height: 39px;
      width: 14px;
      margin-left: 15px;
    }
    .tel {
      display: flex;
      width: 100%;
      align-items: center;

      .front-inner {
        display: flex;
        align-items: center;
        justify-content: center;
        .front-type {
          color: #7f7f7f;
        }
      }
    }

    .code {
      display: flex;
      width: 100%;
      align-items: center;
      :deep(.el-input__wrapper) {
        padding-left: 30px;

        .end-inner {
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 15px;
          .end-type {
            color: #4baef5;
            cursor: pointer;
          }

          .end-interval {
            color: #ccc;
          }
        }
      }
    }

    .unepLoginButton:hover {
      background-color: #409eff;
      font-size: 16px;
    }

    .tip {
      font-size: 12px;
      text-align: center;
      width: 100%;
      color: #aaaaaa;
      margin-top: -10px;
      margin-bottom: 20px;
    }
  }
  .login-tip {
    font-size: 13px;
    text-align: center;
    color: #bfbfbf;
  }
  .login-code {
    width: 33%;
    height: 40px;
    float: right;
    img {
      cursor: pointer;
      vertical-align: middle;
    }
  }
  .el-login-footer {
    height: 40px;
    line-height: 40px;
    position: fixed;
    bottom: 0;
    width: 100%;
    text-align: center;
    color: black;
    font-family: Arial;
    font-size: 12px;
    letter-spacing: 1px;
  }
  .login-code-img {
    height: 40px;
    padding-left: 12px;
  }

  :deep(.el-input--large .el-input__wrapper) {
    margin-left: 10px;
    padding: 1px 1px;
  }

  :deep(.el-input__prefix) {
    padding-left: 10px;
  }

  :deep(.el-input--large .el-input__inner) {
    padding-left: 10px;
  }

  :deep(.el-input__inner) {
    border-radius: 0 20px 20px 0;
  }
</style>
