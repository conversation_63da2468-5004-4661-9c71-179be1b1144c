<template>
  <el-dialog
    v-model="visible"
    :title="!dataForm.carouselImgId ? '新增' : '修改'"
    :close-on-click-modal="false"
    :key="new Date().getTime()"
  >
    <el-form
      ref="dataFormRef"
      :model="dataForm"
      :rules="dataRules"
      label-width="100px"
      @keyup.enter="submitHandle()"
    >
      <el-form-item label="图片名称" prop="name">
        <el-input v-model="dataForm.name" placeholder="图片名称"></el-input>
      </el-form-item>
      <el-form-item label="图片" prop="logoUrl">
        <ImageUpload v-model="dataForm.img" :limit="1" />
      </el-form-item>

      <!-- <el-form-item label="小程序链接" prop="linkUrlRouter">
        <el-input
          v-model="dataForm.linkUrlRouter"
          placeholder="小程序链接地址"
        ></el-input>
      </el-form-item>
      <el-form-item label="web链接" prop="linkUrlWeb">
        <el-input
          v-model="dataForm.linkUrlWeb"
          placeholder="web链接地址"
        ></el-input>
      </el-form-item>

      <el-form-item label="是否显示" prop="isDisplay">
        <el-radio-group v-model="dataForm.isDisplay">
          <el-radio label="1">显示</el-radio>
          <el-radio label="0">隐藏</el-radio>
        </el-radio-group>
      </el-form-item> -->
    </el-form>
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="submitHandle()">确定</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
  import { getImg, addImg, updateImg } from "@/api/opertaionManage/img.js"
  const emit = defineEmits(["refreshDataList"])

  const { proxy } = getCurrentInstance()
  const visible = ref(false)
  const dataFormRef = ref()

  const dataForm = ref({})
  const carouselId = ref("")

  const openDialog = (swiperId, id) => {
    visible.value = true
    dataForm.value.id = ""
    carouselId.value = swiperId

    // 重置表单数据
    dataForm.value = {}

    if (id) {
      getCarousel(id)
    }
  }

  const getCarousel = id => {
    getImg(id).then(res => {
      Object.assign(dataForm.value, res.data)
      // dataForm.value.isDisplay = String(dataForm.value.isDisplay)
    })
  }

  const dataRules = ref({
    name: [{ required: true, message: "必填项不能为空", trigger: "blur" }]
    // type: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
    // img: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
    // isDisplay: [{ required: true, message: "必填项不能为空", trigger: "blur" }]
  })

  // 表单提交
  const submitHandle = () => {
    dataFormRef.value.validate(valid => {
      if (!valid) {
        return false
      }

      if (!dataForm.value.carouselImgId) {
        addImg({ ...dataForm.value, carouselId: carouselId.value }).then(
          res => {
            if (res.code === 200) {
              proxy.$modal.msgSuccess("新增成功")
              visible.value = false
              emit("refreshDataList")
            }
          }
        )
      } else {
        updateImg(dataForm.value).then(res => {
          if (res.code === 200) {
            proxy.$modal.msgSuccess("修改成功")
            visible.value = false
            emit("refreshDataList")
          }
        })
      }
    })
  }

  defineExpose({
    openDialog,
    dataForm
  })
</script>
