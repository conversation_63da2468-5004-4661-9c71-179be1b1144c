<!--
 * @Description: 查看所选人员弹窗
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-03-28 15:15:06
 * @LastEditTime: 2024-05-24 11:22:06
-->

<template>
  <div class="app-container">
    <el-dialog
      v-model="dialogVisible"
      title="查看所选人员"
      width="70%"
      top="110px"
      @close="close"
      :key="new Date().getTime()"
      center
    >
      <el-form
        :model="queryParams"
        ref="queryRef"
        :inline="true"
        v-show="showSearch"
        label-width="68px"
      >
        <el-form-item label="用户名称" prop="userName">
          <el-input
            v-model="queryParams.userName"
            placeholder="请输入用户名称"
            clearable
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="手机号码" prop="phonenumber">
          <el-input
            v-model="queryParams.phonenumber"
            placeholder="请输入手机号码"
            clearable
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-table ref="viewPersonnelTableRef" :data="tableData">
        <el-table-column
          label="用户编号"
          width="100"
          key="userId"
          prop="userId"
        />
        <el-table-column label="用户名称" key="userName" prop="userName" />
        <el-table-column label="用户昵称" key="nickName" prop="nickName" />
        <el-table-column
          label="手机号码"
          key="phonenumber"
          prop="phonenumber"
          width="120"
        />
        <el-table-column label="邮箱" key="email" prop="email" width="200" />
      </el-table>

      <pagination
        class="dialog-pagination"
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
        :autoScroll="false"
      />
      <template #footer>
        <el-button @click="close">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="viewSelectedPersonnelDialog">
  import { getAllSelectedUserList } from "@/api/trainingImplement/trainingTask"

  const emit = defineEmits(["fetch-data"])
  const { proxy } = getCurrentInstance()

  const tableData = ref([])
  const showSearch = ref(true)
  const dialogVisible = ref(false)
  const total = ref(0)

  const data = reactive({
    queryParams: {
      pageNum: 1,
      pageSize: 10
    }
  })

  const { queryParams } = toRefs(data)

  const currentTaskId = ref("")
  //** 弹框打开事件 */
  const openDialog = async taskId => {
    currentTaskId.value = taskId
    reset()
    dialogVisible.value = true
    await getList()
  }

  const reset = () => {
    queryParams.value = {
      pageNum: 1,
      pageSize: 10
    }
  }

  /** 查询目录列表 */
  const getList = async () => {
    let queryData = {
      ...queryParams.value,
      taskId: currentTaskId.value
    }
    const res = await getAllSelectedUserList(queryData)
    if (res.code === 200) {
      tableData.value = res.rows
      total.value = res.total
    }
  }

  /** 搜索按钮操作 */
  function handleQuery() {
    getList()
  }
  /** 重置按钮操作 */
  function resetQuery() {
    proxy.resetForm("queryRef")
    handleQuery()
  }
  // 关闭弹框并重置操作
  const close = () => {
    proxy.resetForm("queryRef")
    dialogVisible.value = false
  }
  defineExpose({
    openDialog
  })
</script>

<style lang="scss" scoped>
  :deep(.pagination-container .el-pagination) {
    position: static;
  }
</style>
