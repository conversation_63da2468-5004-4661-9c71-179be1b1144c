<!--
 * @Description: 人员选择弹窗
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-05-12 14:46:11
 * @LastEditTime: 2023-11-02 08:48:38
-->
<template>
  <div class="app-container">
    <el-dialog
      v-model="dialogVisible"
      :title="title"
      width="70%"
      top="110px"
      @close="close"
      :key="new Date().getTime()"
      center
    >
      <el-form
        :model="queryParams"
        ref="queryRef"
        :inline="true"
        v-show="showSearch"
        label-width="68px"
      >
        <el-form-item label="用户名称" prop="userName">
          <el-input
            v-model="queryParams.userName"
            placeholder="请输入用户名称"
            clearable
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="手机号码" prop="phonenumber">
          <el-input
            v-model="queryParams.phonenumber"
            placeholder="请输入手机号码"
            clearable
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="部门" prop="deptId">
          <el-tree-select
            v-model="queryParams.deptId"
            :data="deptOptions"
            :props="{ value: 'id', label: 'label', children: 'children' }"
            value-key="id"
            placeholder="请选择部门"
            check-strictly
            clearable
          />
        </el-form-item>
        <el-form-item label="创建时间">
          <el-date-picker
            v-model="dateRange"
            value-format="YYYY-MM-DD"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          ></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-table
        ref="choosePersonnelTableRef"
        :data="tableData"
        row-key="userId"
      >
        <el-table-column type="selection" width="55" reserve-selection />
        <el-table-column
          label="用户编号"
          width="100"
          key="userId"
          prop="userId"
        />
        <el-table-column label="用户名称" key="userName" prop="userName" />
        <el-table-column label="用户昵称" key="nickName" prop="nickName" />
        <el-table-column label="部门" key="deptName" prop="dept.deptName" />
        <el-table-column
          label="手机号码"
          key="phonenumber"
          prop="phonenumber"
          width="120"
        />
        <el-table-column label="邮箱" key="email" prop="email" width="200" />
        <el-table-column label="创建时间" prop="createTime" width="160">
          <template #default="scope">
            <span>{{ parseTime(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        class="dialog-pagination"
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
        :autoScroll="false"
      />
      <template #footer>
        <el-button @click="close">取消</el-button>
        <el-button type="primary" @click="save">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="choosePersonnelDialog">
  import {
    listUser,
    deptTreeSelect,
    deptChildrenTreeSelect
  } from "@/api/system/user"

  const emit = defineEmits(["fetch-data"])
  const { proxy } = getCurrentInstance()

  const tableData = ref([])
  const showSearch = ref(true)
  const dialogVisible = ref(false)
  const total = ref(0)
  const dateRange = ref([])
  const deptOptions = ref([])

  const props = defineProps({
    title: {
      type: String,
      default: "选择人员"
    },
    emailRequired: {
      type: Boolean,
      default: false
    },
    phoneRequired: {
      type: Boolean,
      default: false
    },
    restrictedDeptId: {
      type: Number,
      required: false
    }
  })

  const data = reactive({
    queryParams: {
      pageNum: 1,
      pageSize: 10
    },
    selectedRows: []
  })

  const { queryParams, selectedRows } = toRefs(data)

  //** 弹框打开事件 */
  const openDialog = async ids => {
    reset()
    if (ids) {
      selectedRows.value = Array.isArray(ids) ? ids : ids.split(",")
    }
    dialogVisible.value = true
    await getList()
    getDeptTree()
  }

  const reset = () => {
    selectedRows.value = ""
    queryParams.value = {
      pageNum: 1,
      pageSize: 10
    }
  }

  const tableSelectChange = () => {
    if (selectedRows.value) {
      nextTick(() => {
        tableData.value.forEach((item, index) => {
          if (selectedRows.value.find(v => v == item.userId)) {
            proxy.$refs["choosePersonnelTableRef"].toggleRowSelection(
              proxy.$refs["choosePersonnelTableRef"].data[index],
              true
            )
          }
        })
      })
    }
  }

  /** 查询目录列表 */
  const getList = async () => {
    let queryData = {
      deptId: props.restrictedDeptId,
      ...proxy.addDateRange(queryParams.value, dateRange.value)
    }
    const res = await listUser(queryData)
    if (res.code === 200) {
      tableData.value = res.rows
      total.value = res.total
      tableSelectChange()
    }
  }

  /** 查询部门下拉树结构 */
  function getDeptTree() {
    if (props.restrictedDeptId) {
      deptChildrenTreeSelect(props.restrictedDeptId).then(response => {
        deptOptions.value = response.data
      })
    } else {
      deptTreeSelect().then(response => {
        deptOptions.value = response.data
      })
    }
  }

  /** 搜索按钮操作 */
  function handleQuery() {
    getList()
  }
  /** 重置按钮操作 */
  function resetQuery() {
    dateRange.value = []
    proxy.resetForm("queryRef")
    delete queryParams.value.deptId
    handleQuery()
  }
  // 关闭弹框并重置操作
  const close = () => {
    proxy.resetForm("queryRef")
    dialogVisible.value = false
  }
  const save = () => {
    const selectionRows =
      proxy.$refs["choosePersonnelTableRef"].getSelectionRows()
    if (!selectionRows || selectionRows.length === 0) {
      return proxy.$modal.msgWarning("至少选择一名人员")
    }
    if (props.emailRequired) {
      for (let i = 0; i < selectionRows.length; i++) {
        if (!selectionRows[i].email) {
          return proxy.$modal.msgWarning(
            "所选用户存在无邮箱信息人员，请先添加邮箱信息"
          )
        }
      }
    }
    if (props.phoneRequired) {
      for (let i = 0; i < selectionRows.length; i++) {
        if (!selectionRows[i].phonenumber) {
          return proxy.$modal.msgWarning(
            "所选用户存在无手机号码信息人员，请先添加手机号码"
          )
        }
      }
    }
    emit("fetch-data", selectionRows)
    proxy.$modal.msgSuccess("操作成功")
    close()
  }

  defineExpose({
    openDialog
  })
</script>

<style lang="scss" scoped>
  :deep(.pagination-container .el-pagination) {
    position: static;
  }
</style>
