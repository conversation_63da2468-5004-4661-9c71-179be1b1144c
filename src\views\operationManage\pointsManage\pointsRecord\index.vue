<!--
 * @Description: 积分日志
 * @Author: <PERSON>
 * @LastEditors: <PERSON><PERSON>
 * @Date: 2023-04-06 15:29:55
 * @LastEditTime: 2023-07-25 09:46:57
-->
<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
    >
      <el-form-item label="规则类型" prop="ruleType">
        <el-select
          v-model="queryParams.ruleType"
          placeholder="规则类型"
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="dict in devops_integral_rule_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="积分类型" prop="cycleType">
        <el-select
          v-model="queryParams.cycleType"
          placeholder="积分类型"
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="dict in devops_integral_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="用户" prop="userName">
        <el-input
          v-model="queryParams.userName"
          placeholder="用户"
          clearable
          style="width: 240px"
          @keyup.enter="getList"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="getList"
          >搜索</el-button
        >
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb-8px">
      <right-toolbar
        :search="false"
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="tableData">
      <el-table-column prop="ruleType" label="规则类型">
        <template #default="scope">
          <dict-tag
            :options="devops_integral_rule_type"
            :value="scope.row.ruleType"
          />
        </template>
      </el-table-column>
      <el-table-column prop="cycleType" label="积分类型">
        <template #default="scope">
          <dict-tag
            :options="devops_integral_type"
            :value="scope.row.cycleType"
          />
        </template>
      </el-table-column>
      <el-table-column prop="userName" label="用户"></el-table-column>
      <el-table-column prop="score" label="积分数"></el-table-column>
      <el-table-column prop="goodsId" label="兑换的商品"></el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script setup name="pointsRecordList">
  import { listRecord, delRecord } from "@/api/opertaionManage/points.js"

  const { proxy } = getCurrentInstance()
  const { devops_integral_type, devops_integral_rule_type } = proxy.useDict(
    "devops_integral_type",
    "devops_integral_rule_type"
  )

  const tableData = ref([])
  const loading = ref(false)
  const showSearch = ref(true)
  const total = ref(0)

  const data = reactive({
    queryParams: {
      pageNum: 1,
      pageSize: 10
    }
  })

  const { queryParams } = toRefs(data)

  /** 查询目录列表 */
  function getList() {
    loading.value = true
    listRecord(queryParams.value).then(response => {
      tableData.value = response.rows || []
      total.value = response.total
      loading.value = false
    })
  }

  function resetQuery() {
    proxy.resetForm("queryRef")
    getList()
  }

  getList()
</script>
<style lang="scss" scoped></style>
