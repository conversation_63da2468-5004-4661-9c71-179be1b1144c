<!--
 * @Description: 安排考试弹窗
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-03-08 13:50:52
 * @LastEditTime: 2025-04-09 09:32:05
-->
<template>
  <div>
    <el-dialog
      title="安排考试"
      v-model="dialogVisible"
      width="680px"
      append-to-body
      center
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-width="120px"
        :inline="true"
      >
        <el-row>
          <el-col :span="24">
            <el-form-item label="安排类型" prop="arrangeType">
              <el-radio-group v-model="formData.arrangeType">
                <el-radio-button label="K">考试安排</el-radio-button>
                <el-radio-button
                  label="B"
                  :disabled="
                    examInfo.makeUpType === 'Z' ||
                    (examInfo.makeUpType === 'S' && examInfo.makeUpNumber == 0)
                  "
                >
                  补考安排
                </el-radio-button>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="11" :offset="4">
            <el-tabs v-model="formData.autoType" @tab-click="handleTabClick">
              <el-tab-pane label="立即考试" name="0" />
              <el-tab-pane label="定期考试" name="1" />
            </el-tabs>
          </el-col>
          <template v-if="formData.autoType === '1'">
            <el-col :span="24">
              <el-form-item label="考试频率" prop="examFrequency">
                <el-radio-group v-model="formData.examFrequency">
                  <el-radio label="0">每周</el-radio>
                  <el-radio label="1">每月</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="开始日期" prop="startDay">
                <el-radio-group
                  v-if="formData.examFrequency === '0'"
                  v-model="formData.startDay"
                  @change="handleStartDayChange"
                >
                  <el-radio
                    v-for="(day, index) in weekDays"
                    :key="index"
                    :label="index + 1"
                    >{{ day }}</el-radio
                  >
                </el-radio-group>
                <el-select
                  v-else
                  v-model="formData.startDay"
                  placeholder="请选择日期"
                  @change="handleStartDayChange"
                >
                  <el-option
                    v-for="i in 31"
                    :key="i"
                    :label="`${i}日`"
                    :value="i"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="结束日期" prop="endDay">
                <el-radio-group
                  v-if="formData.examFrequency === '0'"
                  v-model="formData.endDay"
                  :disabled="!formData.startDay"
                >
                  <el-radio
                    v-for="(day, index) in weekDays"
                    :key="index"
                    :label="index + 1"
                    :disabled="index + 1 < formData.startDay"
                    >{{ day }}</el-radio
                  >
                </el-radio-group>
                <el-select
                  v-else
                  v-model="formData.endDay"
                  placeholder="请选择日期"
                  :disabled="!formData.startDay"
                >
                  <el-option
                    v-for="i in 31"
                    :key="i"
                    :label="`${i}日`"
                    :value="i"
                    :disabled="i < formData.startDay"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </template>
          <el-col :span="24">
            <el-form-item
              label="考试开始时间"
              prop="startTime"
              class="arrangeTime"
            >
              <el-date-picker
                class="!w-300px"
                @change="startTimeChange"
                :model-value="
                  formData.autoType === '0'
                    ? formData.startTime
                    : formData.autoStartTime
                "
                @update:model-value="
                  val =>
                    formData.autoType === '0'
                      ? (formData.startTime = val)
                      : (formData.autoStartTime = val)
                "
                value-format="YYYY-MM-DD HH:mm:ss"
                type="datetime"
                placeholder="选择开始时间"
                :disabled-date="disabledDate"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item
              label="考试结束时间"
              prop="endTime"
              class="arrangeTime"
            >
              <el-date-picker
                class="!w-300px"
                :model-value="
                  formData.autoType === '0'
                    ? formData.endTime
                    : formData.autoEndTime
                "
                @update:model-value="
                  val =>
                    formData.autoType === '0'
                      ? (formData.endTime = val)
                      : (formData.autoEndTime = val)
                "
                value-format="YYYY-MM-DD HH:mm:ss"
                type="datetime"
                placeholder="选择结束时间"
                :disabled-date="disabledEndDate"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="考试人员" prop="examUserNames">
              <el-input
                v-model="formData.examUserNames"
                :rows="6"
                type="textarea"
                disabled
              />
              <div class="extendButton">
                <el-button type="primary" @click="choosePersonnel"
                  >选择考试人员</el-button
                >
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="save">确 定</el-button>
          <el-button @click="close">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 考试人员选择弹窗 -->
    <ChoosePersonnelOrDeptDialog
      ref="choosePersonnelOrDeptDialogRef"
      :needDeptId="true"
      @fetch-data="choosePersonnelDone"
    />
  </div>
</template>

<script setup name="arrangeDialog">
  import { addArrange, updateArrange } from "@/api/onlineExam/exam.js"

  const emit = defineEmits(["fetch-data"])
  const { proxy } = getCurrentInstance()
  const dialogVisible = ref(false)
  const flag = ref("")
  const examInfo = ref({})
  const arrangeTime = ref(null)
  const formData = ref({
    autoType: "0", // 默认立即考试
    arrangeType: "K", // 默认考试安排
    examFrequency: "0", // 默认每周
    startDay: null,
    endDay: null,
    autoStartTime: "",
    autoEndTime: "",
    startTime: "",
    endTime: "",
    examUserNames: "",
    examUserIds: "",
    deptIds: ""
  })

  const rules = ref({
    arrangeType: [
      { required: true, trigger: "blur", message: "安排类型不能为空" }
    ],
    startTime: [
      {
        required: true,
        validator: (rule, value, callback) => {
          const startTime =
            formData.value.autoType === "0"
              ? formData.value.startTime
              : formData.value.autoStartTime
          if (!startTime) {
            callback(new Error("考试开始时间不能为空"))
          } else {
            callback()
          }
        },
        trigger: "change"
      }
    ],
    endTime: [
      {
        required: true,
        validator: (rule, value, callback) => {
          const startTime =
            formData.value.autoType === "0"
              ? formData.value.startTime
              : formData.value.autoStartTime
          const endTime =
            formData.value.autoType === "0"
              ? formData.value.endTime
              : formData.value.autoEndTime

          if (!endTime) {
            callback(new Error("考试结束时间不能为空"))
          } else if (
            startTime &&
            endTime &&
            new Date(endTime).getTime() <= new Date(startTime).getTime()
          ) {
            callback(new Error("考试结束时间必须晚于开始时间"))
          } else {
            callback()
          }
        },
        trigger: "change"
      }
    ],
    examUserNames: [
      { required: true, trigger: "change", message: "考试人员不能为空" }
    ],
    examFrequency: [
      { required: true, trigger: "change", message: "考试频率不能为空" }
    ],
    startDay: [
      { required: true, trigger: "change", message: "开始日期不能为空" }
    ],
    endDay: [{ required: true, trigger: "change", message: "结束日期不能为空" }]
  })

  const weekDays = ["周一", "周二", "周三", "周四", "周五", "周六", "周日"]

  const disabledDate = time => {
    return time.getTime() < Date.now() - 8.64e7 // 禁用今天以前的日期
  }

  const disabledEndDate = time => {
    const startTime =
      formData.value.autoType === "0"
        ? formData.value.startTime
        : formData.value.autoStartTime
    if (!startTime) {
      return time.getTime() < Date.now() - 8.64e7 // 如果没有选择开始时间，禁用今天以前的日期
    }
    // 禁用今天以前的日期和开始时间之前的时间
    return (
      time.getTime() < Date.now() - 8.64e7 ||
      time.getTime() < new Date(startTime).getTime()
    )
  }

  const handleStartDayChange = () => {
    formData.value.endDay = null
  }

  const handleTabClick = () => {
    if (formData.value.autoType === "0") {
      formData.value.examFrequency = "0"
      formData.value.startDay = null
      formData.value.endDay = null
    }
    formData.value.startTime = ""
    formData.value.endTime = ""
    formData.value.autoStartTime = ""
    formData.value.autoEndTime = ""
  }

  const startTimeChange = value => {
    // 值已通过v-model直接绑定到formData
    // 检查结束时间是否早于开始时间
    const startTime =
      formData.value.autoType === "0"
        ? formData.value.startTime
        : formData.value.autoStartTime
    const endTime =
      formData.value.autoType === "0"
        ? formData.value.endTime
        : formData.value.autoEndTime

    if (
      endTime &&
      new Date(endTime).getTime() < new Date(startTime).getTime()
    ) {
      // 清空结束时间
      if (formData.value.autoType === "0") {
        formData.value.endTime = ""
      } else {
        formData.value.autoEndTime = ""
      }
    }
  }

  const choosePersonnel = () => {
    proxy.$refs["choosePersonnelOrDeptDialogRef"].openDialog(
      formData.value.examUserIds,
      formData.value.examUserNames,
      formData.value.deptIds
    )
  }

  const choosePersonnelDone = userList => {
    if (!userList || userList.length === 0) return
    delete formData.value.deptName
    formData.value.deptIds = userList.map(user => user.deptId).join()
    formData.value.examUserIds = userList.map(user => user.userId).join()
    formData.value.examUserNames = userList.map(user => user.userName).join()
  }

  const openDialog = (optFlag, baseInfo, row) => {
    flag.value = optFlag
    examInfo.value = baseInfo
    formData.value = {
      autoType: "0",
      arrangeType: "K",
      examFrequency: "0",
      startDay: null,
      endDay: null,
      autoStartTime: "",
      autoEndTime: "",
      startTime: "",
      endTime: "",
      examUserNames: "",
      examUserIds: "",
      deptIds: ""
    }
    arrangeTime.value = null

    if (flag.value === "edit") {
      const editData = { ...row }
      formData.value = editData
    }
    dialogVisible.value = true
  }

  const close = () => {
    formData.value = {
      autoType: "0",
      arrangeType: "K",
      examFrequency: "0",
      startDay: null,
      endDay: null,
      autoStartTime: "",
      autoEndTime: "",
      startTime: "",
      endTime: "",
      examUserNames: "",
      examUserIds: "",
      deptIds: ""
    }
    arrangeTime.value = null
    proxy.resetForm("formRef")
    dialogVisible.value = false
  }

  const save = () => {
    proxy.$refs["formRef"].validate(valid => {
      if (valid) {
        const submitData = {
          ...formData.value,
          baseId: examInfo.value.baseId
        }

        if (formData.value.autoType === "1") {
          submitData.startDay = parseInt(formData.value.startDay)
          submitData.endDay = parseInt(formData.value.endDay)
          delete submitData.startTime
          delete submitData.endTime
        } else {
          delete submitData.autoStartTime
          delete submitData.autoEndTime
          delete submitData.examFrequency
          delete submitData.startDay
          delete submitData.endDay
        }

        const request = flag.value === "edit" ? updateArrange : addArrange
        request(submitData).then(res => {
          if (res.code === 200) {
            proxy.$modal.msgSuccess("操作成功")
            close()
            emit("fetch-data")
          }
        })
      }
    })
  }

  defineExpose({
    openDialog
  })
</script>
<style lang="scss" scoped>
  :deep(.el-tabs__nav-wrap::after) {
    width: 0;
  }
  :deep(.el-textarea__inner) {
    width: 300px;
  }
  .extendButton {
    position: absolute;
    left: 320px;
    top: 50px;
  }
</style>
