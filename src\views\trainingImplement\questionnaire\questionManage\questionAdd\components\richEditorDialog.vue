<!--
 * @Description: 富文本编辑页面
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-02-28 13:28:26
 * @LastEditTime: 2023-05-30 16:15:19
-->
<template>
  <el-dialog v-model="visible" width="70%" center>
    <Toolbar
      v-if="visible"
      style="border-bottom: 1px solid #ccc"
      :editor="editorRef"
      :defaultConfig="toolbarConfig"
      mode="simple"
    />
    <Editor
      v-if="visible"
      style="height: 500px; overflow-y: hidden"
      v-model="valueHtml"
      :defaultConfig="editorConfig"
      mode="simple"
      @onCreated="handleCreated"
    />
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="handleConfirm">确 定</el-button>
        <el-button @click="visible = false">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup name="richEditorDialog">
import "@wangeditor/editor/dist/css/style.css" // 引入 css
import { Editor, Toolbar } from "@wangeditor/editor-for-vue"

const visible = ref(false)
const editorRef = shallowRef(null)
const valueHtml = ref("")
const key = ref("")
const toolbarConfig = {}
const editorConfig = { placeholder: "请输入内容..." }
const questionType = ref(null)

const emit = defineEmits(["ok"])

/** 查询参数列表 */
function show(value, parameterKey, type) {
  valueHtml.value = value || ""
  visible.value = true
  key.value = parameterKey
  if (type) questionType.value = type
}

const handleCreated = editor => {
  editorRef.value = editor // 记录 editor 实例，重要！
}

const handleConfirm = () => {
  const editor = editorRef.value
  const text = editor.getText()
  visible.value = false
  emit("ok", valueHtml.value, key.value, text)
}

onBeforeUnmount(() => {
  editorRef.value && editorRef.value.destroy()
})

defineExpose({
  show
})
</script>
