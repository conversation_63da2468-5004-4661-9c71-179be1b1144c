<!--
 * @Description: 课程报表统计详情
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-10-11 09:48:01
 * @LastEditTime: 2024-02-27 08:54:54
-->
<template>
  <div class="app-container">
    <el-row :gutter="10" class="mb-8px">
      <div class="home_process_header">
        <el-icon class="backIcon" size="20" @click="handleBack">
          <ArrowLeftBold />
        </el-icon>
        <el-divider direction="vertical"></el-divider>
        课程名称： {{ row.courseName }}
      </div>
    </el-row>

    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
    >
      <el-form-item label="姓名" prop="userName">
        <el-input
          v-model="queryParams.userName"
          placeholder="姓名"
          clearable
          style="width: 240px"
          @keyup.enter="getList"
        />
      </el-form-item>
      <el-form-item
        label="所属分公司"
        prop="domainName"
        v-if="roles.includes('taibao_admin') || user.userId === 1"
      >
        <el-tree-select
          v-model="queryParams.domainName"
          :data="officeTreeOption"
          :props="{ value: 'domainName', label: 'label', children: 'children' }"
          value-key="id"
          placeholder="请选择所属分公司"
          check-strictly
          clearable
        />
      </el-form-item>
      <el-form-item label="部门" prop="deptId">
        <el-tree-select
          v-model="queryParams.deptId"
          :data="deptOptions"
          :props="{ value: 'id', label: 'label', children: 'children' }"
          value-key="id"
          placeholder="请选择部门"
          check-strictly
          clearable
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="getList">
          搜索
        </el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb-8px">
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport">
          导出
        </el-button>
      </el-col>
      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table :data="tableData" v-loading="loading">
      <el-table-column label="序号" width="60" type="index" />
      <el-table-column label="姓名" prop="userName" />
      <el-table-column label="所属部门" prop="deptName" />
      <el-table-column label="学习时长" prop="deltaDuration">
        <template #default="scope">
          {{ formatSeconds(scope.row.deltaDuration) }}
        </template>
      </el-table-column>
      <el-table-column label="学习进度" prop="learningProcess">
        <template #default="scope">
          {{ Number(scope.row.learningProcess) * 100 }}%
        </template>
      </el-table-column>
      <!-- <el-table-column label="课程得分" prop="startTime" />
      <el-table-column label="考试得分" prop="submitTime" /> -->
      <el-table-column label="开始时间" prop="startTime" width="180" />
      <el-table-column label="结束时间" prop="endTime" width="180" />
      <el-table-column
        label="完成时间"
        prop="actualCompletionTime"
        width="180"
      />
      <el-table-column label="完成状态" prop="learnStatus" width="120">
        <template #default="scope">
          <dict-tag
            :options="course_learn_status"
            :value="scope.row.learnStatus"
          /> </template
      ></el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script setup name="reportDetails">
  import dayjs from "dayjs"
  import { deptTreeSelect } from "@/api/system/user"
  import { getReportDetail } from "@/api/onlineCourse/course.js"
  import { formatSeconds } from "@/utils/common.js"
  import useUserStore from "@/store/modules/user"

  const { roles, user } = storeToRefs(useUserStore())
  const { proxy } = getCurrentInstance()
  const { course_learn_status } = proxy.useDict("course_learn_status")
  const emit = defineEmits(["updateCurrentView"])
  const props = defineProps({
    row: {
      type: Object,
      default: () => ({})
    }
  })

  const tableData = ref([])
  const loading = ref(true)
  const showSearch = ref(true)
  const total = ref(0)
  const deptOptions = ref([])

  const data = reactive({
    queryParams: {
      pageNum: 1,
      pageSize: 10
    }
  })

  const { queryParams } = toRefs(data)

  /** 查询目录列表 */
  function getList() {
    loading.value = true
    let queryData = {
      ...queryParams.value,
      courseId: props.row.courseId
    }
    getReportDetail(queryData).then(response => {
      tableData.value = response.rows || []
      total.value = response.total
      loading.value = false
    })
  }

  const handleBack = () => {
    emit("updateCurrentView", "courseReport")
  }

  /** 重置按钮操作 */
  function resetQuery() {
    proxy.resetForm("queryRef")
    getList()
  }

  /** 导出按钮操作 */
  function handleExport() {
    proxy.download(
      "course/progress/user-export",
      {
        ...queryParams.value,
        courseId: props.row.courseId
      },
      `${props.row.courseName}_${dayjs().format("YYYY-MM-DD")}.xlsx`
    )
  }
  /** 查询部门下拉树结构 */
  function getDeptTree() {
    deptTreeSelect().then(response => {
      deptOptions.value = response.data
    })
  }
  const officeTreeOption = ref([])
  /** 查询部门下拉树结构 */
  const getBranchOfficeTree = async () => {
    const response = await deptTreeSelect({
      parentId: 100
    })
    officeTreeOption.value = response.data
  }

  getList()
  getDeptTree()
  if (roles.value.includes("taibao_admin") || user.value.userId === 1) {
    getBranchOfficeTree()
  }
</script>

<style lang="scss" scoped>
  .backIcon {
    svg {
      height: 0.7em;
    }
  }
</style>
