/*
 * @Description: vite plugin配置页面
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-02-16 14:11:33
 * @LastEditTime: 2023-09-28 16:31:01
 */
import vue from "@vitejs/plugin-vue"

import createAutoImport from "./auto-import"
import createSvgIcon from "./svg-icon"
// import createCompression from "./compression"
import createSetupExtend from "./setup-extend"
import createAliOssUpload from "./ali-oss-upload"
import createUnoCSS from "./uno-css"

export default function createVitePlugins(viteEnv) {
  const vitePlugins = [vue()]
  vitePlugins.push(createAutoImport())
  vitePlugins.push(createSetupExtend())
  vitePlugins.push(createSvgIcon(false))
  // vitePlugins.push(...createCompression(viteEnv))
  viteEnv.VITE_APP_ENV === "production" &&
    vitePlug<PERSON>.push(createAliOssUpload())
  vitePlugins.push(createUnoCSS())
  return vitePlugins
}
