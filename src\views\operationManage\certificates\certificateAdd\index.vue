<!--
 * @Description: 证书录入
 * @Author: <PERSON><PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-06-08 16:07:45
 * @LastEditTime: 2023-07-17 11:38:22
-->
<template>
  <div class="app-container">
    <el-form :model="rawForm" ref="formRef" label-width="100px" :rules="rules">
      <el-form-item label="模板编号" prop="templateCode">
        <el-input style="width: 300px" v-model="rawForm.templateCode" />
      </el-form-item>
      <el-form-item label="模板名称" prop="templateName">
        <el-input style="width: 300px" v-model="rawForm.templateName" />
      </el-form-item>
      <el-form-item label="模板封面" prop="templateImg">
        <ImageUpload v-model="rawForm.templateImg" :limit="1" />
      </el-form-item>
      <el-form-item label="有效时间" prop="effectiveTime">
        <el-input-number
          v-model="rawForm.effectiveTime"
          :step="1"
          :precision="1"
          :min="0"
        />年
      </el-form-item>
      <el-form-item label="编号规则" prop="codeRules">
        <el-input style="width: 300px" v-model="rawForm.codeRules" />
      </el-form-item>
      <el-form-item label="模板介绍" prop="templateIntroduction">
        <el-input
          style="width: 300px"
          v-model="rawForm.templateIntroduction"
          :rows="4"
          type="textarea"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="submitForm">提交</el-button>
        <el-button @click="reset">重置</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup name="certificateAdd">
  import { getCert, addCert, updateCert } from "@/api/devops/cert-template.js"
  const { proxy } = getCurrentInstance()

  const route = useRoute()

  const items = ref([])

  const data = reactive({
    form: {},
    rawForm: {},
    rules: {
      templateCode: [
        { required: true, message: "模板编号不能为空", trigger: "change" }
      ],
      templateName: [
        { required: true, message: "模板名称不能为空", trigger: "change" }
      ],
      templateImg: [
        { required: true, message: "模板图片不能为空", trigger: "change" }
      ]
    }
  })
  const { form, rules, rawForm } = toRefs(data)

  const flag = ref("add")
  if (Object.keys(route.query).length !== 0) {
    getCert(route.query.certTemplateId).then(res => {
      rawForm.value = res.data
      flag.value = "edit"
    })
  }

  const submitForm = () => {
    proxy.$refs["formRef"].validate(valid => {
      if (valid) {
        if (flag.value === "add") {
          addCert(rawForm.value).then(res => {
            if (res.code === 200) {
              reset()
              proxy.$modal.msgSuccess("新增成功")
              proxy.$tab.closeOpenPage({
                path: "/operationManage/certificates/certificateList"
              })
            }
          })
        } else {
          updateCert(rawForm.value).then(res => {
            if (res.code === 200) {
              reset()
              proxy.$modal.msgSuccess("修改成功")
              proxy.$tab.closeOpenPage({
                path: "/operationManage/certificates/certificateList"
              })
            }
          })
        }
      }
    })
  }

  /** 表单重置 */
  function reset() {
    rawForm.value = {}
  }
</script>
<style lang="scss" scoped>
  :deep(.el-form-item__content) {
    display: block;
  }
  :deep(.el-input) {
    display: inline-block;
    width: auto;
  }
  :deep(.el-input__wrapper) {
    width: 100%;
  }
</style>
