<!--
 * @Description: 积分规则列表
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-04-06 15:29:41
 * @LastEditTime: 2023-04-11 13:34:01
-->
<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
    >
      <el-form-item label="规则类型" prop="ruleType">
        <el-select
          v-model="queryParams.ruleType"
          placeholder="规则类型"
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="dict in devops_integral_rule_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="周期类型" prop="cycle">
        <el-select
          v-model="queryParams.cycle"
          placeholder="周期类型"
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="dict in devops_integral_cycle_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="getList"
          >搜索</el-button
        >
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb-8px">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleUpdate">
          新增
        </el-button>
      </el-col>
      <right-toolbar
        :search="false"
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="tableData">
      <el-table-column prop="ruleType" label="规则类型">
        <template #default="scope">
          <dict-tag
            :options="devops_integral_rule_type"
            :value="scope.row.ruleType"
          />
        </template>
      </el-table-column>
      <el-table-column prop="cycle" label="周期类型">
        <template #default="scope">
          <dict-tag
            :options="devops_integral_cycle_type"
            :value="scope.row.cycle"
          />
        </template>
      </el-table-column>
      <el-table-column
        prop="maxNumber"
        label="周期最多奖励数"
      ></el-table-column>
      <el-table-column
        prop="singleScore"
        label="单次奖励分值"
      ></el-table-column>
      <el-table-column prop="isUsing" label="是否启用" width="150">
        <template #default="scope">
          <el-tag v-if="scope.row.isUsing === '1'" size="large">启用</el-tag>
          <el-tag v-else type="danger" size="large">停用</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        width="200"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-button
            link
            type="primary"
            icon="Edit"
            @click="handleUpdate(scope.row)"
            >修改</el-button
          >
          <el-button
            link
            type="primary"
            icon="Delete"
            @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 弹窗, 新增 / 修改 -->
    <addEditDialog
      ref="addEditDialogRef"
      @refreshDataList="getList"
    ></addEditDialog>
  </div>
</template>

<script setup name="pointsRuleList">
import addEditDialog from "./addEditDialog.vue"
import { listRule, delRule } from "@/api/opertaionManage/points.js"

const { proxy } = getCurrentInstance()
const { devops_integral_rule_type, devops_integral_cycle_type } = proxy.useDict(
  "devops_integral_rule_type",
  "devops_integral_cycle_type"
)

const tableData = ref([])
const loading = ref(false)
const showSearch = ref(true)
const total = ref(0)
const addEditDialogRef = ref()

const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10
  }
})

const { queryParams } = toRefs(data)

/** 查询目录列表 */
function getList() {
  loading.value = true
  listRule(queryParams.value).then((response) => {
    tableData.value = response.rows || []
    total.value = response.total
    loading.value = false
  })
}
/** 修改按钮操作 */
function handleUpdate(row) {
  addEditDialogRef.value.openDialog(row?.integralRuleId)
}
/** 删除按钮操作 */
function handleDelete(row) {
  proxy.$modal
    .confirm(
      '是否确认删除类型为"' +
        proxy.selectDictLabel(devops_integral_rule_type.value, row.ruleType) +
        '"的数据项?'
    )
    .then(function () {
      return delRule(row.integralRuleId)
    })
    .then(() => {
      getList()
      proxy.$modal.msgSuccess("删除成功")
    })
    .catch(() => {})
}

function resetQuery() {
  proxy.resetForm("queryRef")
  getList()
}

getList()
</script>
<style lang="scss" scoped></style>
