<!--
 * @Description: 问卷题目结果详情
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-05-06 13:40:54
 * @LastEditTime: 2024-05-17 17:21:22
-->
<template>
  <div class="app-container">
    <el-row :gutter="10" class="mb-8px">
      <div class="home_process_header">
        <el-icon class="backIcon" size="20" @click="handleBack">
          <ArrowLeftBold />
        </el-icon>
        <el-divider direction="vertical"></el-divider>
        试题结果详情
      </div>
    </el-row>
    <el-row :gutter="10" class="mb-8px">
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport">
          导出
        </el-button>
      </el-col>
    </el-row>
    <el-row :gutter="10" class="center-container">
      <el-col :span="8" class="survey-title">
        题干名称：{{ row.questionName }}
      </el-col>
      <el-col :span="4"> 问卷人数：{{ surveyCount }} </el-col>
      <right-toolbar
        :search="false"
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="tableData">
      <el-table-column
        type="index"
        label="序号"
        align="center"
        width="70"
      ></el-table-column>
      <el-table-column label="手机号" width="200" prop="createBy">
      </el-table-column>
      <el-table-column label="反馈结果" prop="userAnswer"> </el-table-column>
      <el-table-column label="反馈时间" width="280" prop="createTime">
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script setup name="resultDetail">
  import dayjs from "dayjs"
  import {
    questionResultDetail,
    getParticipantsCount
  } from "@/api/trainingImplement/questionnaire.js"

  const { proxy } = getCurrentInstance()
  const emit = defineEmits(["updateCurrentView"])
  const props = defineProps({
    row: {
      type: Object,
      default: () => ({})
    }
  })

  const tableData = ref([])
  const loading = ref(true)
  const showSearch = ref(true)
  const total = ref(0)

  const queryParams = ref({
    pageNum: 1,
    pageSize: 10
  })

  /** 查询目录列表 */
  async function getList() {
    loading.value = true
    let queryData = {
      ...queryParams.value,
      questionnaireId: props.row.questionnaireId,
      questionId: props.row.traningQuestionId
    }
    const response = await questionResultDetail(queryData)
    tableData.value = response.rows || []
    total.value = response.total
    loading.value = false
  }

  const surveyCount = ref(0)
  const getSurveyPersonCount = async () => {
    let queryData = {
      questionnaireId: props.row.questionnaireId,
      questionId: props.row.traningQuestionId
    }
    const res = await getParticipantsCount(queryData)
    surveyCount.value = res.data || 0
  }

  const handleBack = () => {
    emit("updateCurrentView", "resultList", {
      ...props.row
    })
  }

  const handleExport = () => {
    proxy.download(
      "course/answer/user-export",
      {
        questionnaireId: props.row.questionnaireId,
        questionId: props.row.traningQuestionId
      },
      `调查问卷结果详情_${dayjs().format("YYYY-MM-DD HH:mm:ss")}.xlsx`
    )
  }

  getList()
  getSurveyPersonCount()
</script>

<style lang="scss" scoped>
  .backIcon {
    svg {
      height: 0.7em;
    }
  }

  .center-container {
    font-size: 14px;
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    .survey-title {
      font-weight: bolder;
    }
  }
</style>
