<!--
 * @Description: ShortCutCard
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-04-11 10:09:44
 * @LastEditTime: 2023-06-28 10:26:21
-->
<template>
  <div class="container">
    <el-tag :color="randomColor()" class="container-tag">
      <el-icon :size="45">
        <svg-icon :icon-class="props.icon" class="container-tag-icon" />
      </el-icon>
    </el-tag>
    <span class="container-span">{{ props.label }}</span>
  </div>
</template>

<script setup name="shortcut">
  const props = defineProps({
    icon: {
      type: String,
      default: () => "menu-outlined",
      required: false
    },
    label: {
      type: String,
      default: () => "快捷方式",
      required: false
    },
    color: {
      type: String,
      default: () => "",
      required: false
    }
  })
  // 颜色列表
  const colorList = [
    "#7265E6",
    "#FFBF00",
    "#00A2AE",
    "#F56A00",
    "#1890FF",
    "#606D80"
  ]
  // 获取随机颜色
  const randomColor = () => {
    if (props.color) {
      return props.color
    }
    return colorList[randomNum(0, colorList.length - 1)]
  }
  // 获取minNum到maxNum内的随机数
  const randomNum = (minNum, maxNum) => {
    switch (arguments.length) {
      case 1:
        return parseInt(Math.random() * minNum + 1, 10)
        // eslint-disable-next-line no-unreachable
        break
      case 2:
        return parseInt(Math.random() * (maxNum - minNum + 1) + minNum, 10)
        // eslint-disable-next-line no-unreachable
        break
      default:
        return 0
        // eslint-disable-next-line no-unreachable
        break
    }
  }
</script>

<style scoped>
  .container {
    height: 60px;
    border-radius: 5px;
    display: flex;
    align-items: center;
    cursor: pointer;
    /*实现渐变（时间变化效果）*/
    -webkit-transition: all 0.5s;
    -moz-transition: all 0.5s;
    -ms-transition: all 0.5s;
    -o-transition: all 0.5s;
    transition: all 0.5s;
  }
  .container:hover {
    background: #f0f0f0;
  }
  .container-tag {
    width: 42px;
    height: 42px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    margin: 0 10px;
  }
  .container-tag-icon {
    color: #fff;
    width: 100%;
    height: 100%;
    transform: scale(1.5);
  }
  .container-span {
    max-width: 60%;
    font-weight: 500;
  }
</style>
