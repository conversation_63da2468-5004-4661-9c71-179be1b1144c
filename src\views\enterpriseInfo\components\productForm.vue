<!--
 * @Description: 新增/修改短视频
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-04-10 17:21:57
 * @LastEditTime: 2023-05-31 10:17:22
-->
<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    :close-on-click-modal="false"
    center
  >
    <el-form
      ref="dataFormRef"
      :model="dataForm"
      :rules="dataFormRules"
      label-width="190px"
      @keyup.enter="submitHandle()"
    >
      <!-- <el-form-item label="关联企业" prop="enterpriseId">
        <el-input
          placeholder="请输入关联企业"
          v-model="dataForm.enterpriseId"
        />
      </el-form-item> -->
      <el-form-item label="产品/服务名称" prop="productName">
        <el-input
          placeholder="请输入产品/服务名称"
          v-model="dataForm.productName"
        />
      </el-form-item>
      <el-form-item label="产品/服务简介及价格" prop="productIntro">
        <el-input
          placeholder="请输入产品/服务简介及价格"
          v-model="dataForm.productIntro"
        />
      </el-form-item>
      <el-form-item label="对促进会会员优惠/免费说明" prop="discountInfo">
        <el-input
          placeholder="请输入对促进会会员优惠/免费说明"
          v-model="dataForm.discountInfo"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="submitHandle()">确定</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
  import {
    getProduct,
    addProduct,
    updateProduct
  } from "@/api/enterpriseInfo/product.js"

  const emit = defineEmits(["refreshDataList"])

  const { proxy } = getCurrentInstance()

  const visible = ref(false)
  const dataFormRef = ref()
  let dataForm = ref({})
  const dialogTitle = ref("") // 弹窗的标题
  const formType = ref("") // 表单的类型：create - 新增；update - 修改

  const dataFormRules = ref({
    productName: [
      { required: true, message: "必填项不能为空", trigger: "blur" }
    ],
    productIntro: [
      { required: true, message: "必填项不能为空", trigger: "blur" }
    ],
    discountInfo: [
      { required: true, message: "必填项不能为空", trigger: "blur" }
    ]
  })

  // 打开弹窗事件
  const openDialog = async (type, id, enterpriseId) => {
    // 重置表单数据
    dataForm.value = {}
    dialogTitle.value = type === "create" ? "新增" : "修改"
    formType.value = type
    dataForm.value.enterpriseId = enterpriseId
    if (id) {
      const { data } = await getProduct(id)
      Object.assign(dataForm.value, data)
    }
    visible.value = true
  }

  // 表单提交
  const submitHandle = () => {
    dataFormRef.value.validate(valid => {
      if (!valid) {
        return false
      }
      if (formType.value === "create") {
        addProduct(dataForm.value).then(res => {
          if (res.code === 200) {
            proxy.$modal.msgSuccess("新增成功")
            visible.value = false
            emit("refreshDataList")
          }
        })
      } else {
        updateProduct(dataForm.value).then(res => {
          if (res.code === 200) {
            proxy.$modal.msgSuccess("修改成功")
            visible.value = false
            emit("refreshDataList")
          }
        })
      }
    })
  }

  defineExpose({
    openDialog
  })
</script>
