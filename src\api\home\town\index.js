/*
 * @Description: 北蔡防灾减灾-首页
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-12-21 16:14:04
 * @LastEditTime: 2024-03-07 14:16:38
 */

import request from "@/utils/request"

// 居委数据一览
export function townDashboard() {
  return request({
    url: "/course/task/townDashboard",
    method: "get"
  })
}

// 商铺数据一览
export function storeDashboard(params) {
  return request({
    url: "/reportforms/report/townDashboard",
    method: "get",
    params
  })
}

// 居民数据一览
export function residentDashboard(params) {
  return request({
    url: "/reportforms/report/townDashboard",
    method: "get",
    params
  })
}

// 居委社区学习情况
export function towncommDist(params) {
  return request({
    url: "/course/study-log/commDist",
    method: "get",
    params
  })
}

// 社区下属商铺学习情况
export function towncommStore(params) {
  return request({
    url: "/reportforms/report/comm-shops",
    method: "get",
    params
  })
}

// 居民社区学习情况
export function towncommResident(params) {
  return request({
    url: "/reportforms/report/comm-resident",
    method: "get",
    params
  })
}

// 居委学习情况
export function towncountryDist(params) {
  return request({
    url: "/course/study-log/countryDist",
    method: "get",
    params
  })
}

// 居委下属商铺学习情况
export function towncountryStore(params) {
  return request({
    url: "/reportforms/report/country-shops",
    method: "get",
    params
  })
}

// 居民学习情况
export function towncountryResident(params) {
  return request({
    url: "/reportforms/report/country-resident",
    method: "get",
    params
  })
}

// 居委学习排名
export function getTownCommDistRank(params) {
  return request({
    url: "/system/dept/getTownCommDistRank",
    method: "get",
    params
  })
}

// 商铺学习排名
export function getTownStoreRank(params) {
  return request({
    url: "/reportforms/report/getCountryShopsRank",
    method: "get",
    params
  })
}

// 居民学习排名
export function getTownResidentRank(params) {
  return request({
    url: "/reportforms/report/getCountryResidentRank",
    method: "get",
    params
  })
}
