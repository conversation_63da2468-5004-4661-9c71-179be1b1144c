<!--
 * @Description: 设置课程关联弹窗
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-04-18 13:22:03
 * @LastEditTime: 2024-04-18 15:49:38
-->
<template>
  <div class="app-container">
    <el-dialog
      v-model="dialogVisible"
      title="设置课程关联"
      width="60%"
      top="110px"
      @close="close"
      :key="new Date().getTime()"
      center
    >
      <el-table ref="multipleTableRef" :data="tableData">
        <el-table-column type="selection" width="55" />
        <el-table-column label="序号" type="index" width="70" />
        <el-table-column label="标签名称" prop="dictLabel">
          <template #default="scope">
            <span
              v-if="
                scope.row.listClass == '' || scope.row.listClass == 'default'
              "
            >
              {{ scope.row.dictLabel }}
            </span>
            <el-tag
              v-else
              :type="
                scope.row.listClass == 'primary' ? '' : scope.row.listClass
              "
            >
              {{ scope.row.dictLabel }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="标签键值" prop="dictValue" />
        <el-table-column label="排序" prop="dictSort" />
        <el-table-column label="备注" prop="remark" />
        <el-table-column label="创建时间" prop="createTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
      </el-table>
      <template #footer>
        <el-button @click="close">关闭</el-button>
        <el-button type="primary" @click="save">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="chooseCourseLabelDialog">
  import { listData } from "@/api/system/dict/data"
  import { getDeptCourseTagAss, saveDeptCourseTagAss } from "@/api/system/dept"

  const { proxy } = getCurrentInstance()
  const tableData = ref([])
  const dialogVisible = ref(false)
  const deptId = ref(null)
  const currentSelectedRow = ref([])
  const multipleTableRef = ref(null)

  //** 弹框打开事件 */
  const openDialog = async id => {
    dialogVisible.value = true
    deptId.value = id
    await getList()
    await fetchAlreadyAssList()
  }

  const reset = () => {
    currentSelectedRow.value = []
  }

  /** 查询目录列表 */
  const getList = async () => {
    const { rows } = await listData({
      dictType: "course_label_type",
      pageNum: 1,
      pageSize: 999
    })
    tableData.value = rows
  }
  const fetchAlreadyAssList = async () => {
    const { data } = await getDeptCourseTagAss(deptId.value)
    currentSelectedRow.value = data.map(item => item.courseLabel)
    if (currentSelectedRow.value && currentSelectedRow.value.length > 0) {
      nextTick(() => {
        tableData.value.forEach((item, index) => {
          if (currentSelectedRow.value.find(v => v == item.dictValue)) {
            multipleTableRef.value.toggleRowSelection(
              multipleTableRef.value.data[index],
              true
            )
          }
        })
      })
    }
  }
  // 关闭弹框并重置操作
  const close = () => {
    reset()
    dialogVisible.value = false
  }
  const save = async () => {
    console.log("multipleTableRef.value", multipleTableRef.value)
    currentSelectedRow.value = multipleTableRef.value.getSelectionRows()
    const submitData = currentSelectedRow.value.map(item => ({
      deptId: deptId.value,
      courseLabel: item.dictValue,
      courseLabelName: item.dictLabel
    }))
    const res = await saveDeptCourseTagAss(submitData)
    if (res.code === 200) {
      proxy.$modal.msgSuccess("操作成功")
      close()
    }
  }

  defineExpose({
    openDialog
  })
</script>
