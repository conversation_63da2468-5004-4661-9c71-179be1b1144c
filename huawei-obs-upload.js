/*
 * @Description: 华为云上传
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-03-12 11:11:07
 * @LastEditTime: 2024-09-19 15:40:49
 */

const OBSPlugin = require("huawei-obs-plugin")
const path = require("path")
const [bucket1, bucket2] = process.argv.splice(2)

new OBSPlugin({
  accessKeyId: "I5CROPRZ7WFRHNCILVMP",
  secretAccessKey: "lcr5k2ereEVEvjp0TQAgmIeugQGHHLz680n2NeoM",
  server: "https://obs.cn-east-3.myhuaweicloud.com",
  bucket: bucket1,
  exclude: null,
  deleteAll: true,
  output: path.resolve(__dirname, "./dist"),
  local: true
}).upload()

new OBSPlugin({
  accessKeyId: "I5CROPRZ7WFRHNCILVMP",
  secretAccessKey: "lcr5k2ereEVEvjp0TQAgmIeugQGHHLz680n2NeoM",
  server: "https://obs.cn-east-3.myhuaweicloud.com",
  bucket: bucket2,
  exclude: null,
  deleteAll: true,
  output: path.resolve(__dirname, "./dist"),
  local: true
}).upload()
