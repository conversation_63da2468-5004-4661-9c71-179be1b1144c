<!--
 * @Description: 积分兑换列表
 * @Author: <PERSON>
 * @LastEditors: <PERSON><PERSON>
 * @Date: 2023-04-06 15:29:41
 * @LastEditTime: 2023-07-25 09:44:16
-->
<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
    >
      <el-form-item label="赠品名称" prop="goodsName">
        <el-input
          v-model="queryParams.goodsName"
          placeholder="赠品名称"
          clearable
          style="width: 240px"
          @keyup.enter="getList"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="getList"
          >搜索</el-button
        >
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb-8px">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleUpdate">
          新增
        </el-button>
      </el-col>
      <right-toolbar
        :search="false"
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="tableData">
      <el-table-column prop="goodsName" label="赠品名称"> </el-table-column>
      <el-table-column label="赠品图片" prop="photoUrl">
        <template #default="scope">
          <el-popover placement="right" :width="400" trigger="hover">
            <img :src="scope.row.photoUrl" width="375" height="375" />
            <template #reference>
              <img
                :src="scope.row.photoUrl"
                style="max-height: 60px; max-width: 60px"
              />
            </template>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column prop="integral" label="兑换积分数"> </el-table-column>
      <el-table-column prop="availableQty" label="可兑换数"></el-table-column>
      <el-table-column prop="exchangeQty" label="已兑换数量"></el-table-column>
      <el-table-column prop="goodsStatus" label="上架状态">
        <template #default="scope">
          <dict-tag :options="goods_type" :value="scope.row.goodsStatus" />
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        width="200"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-button
            link
            type="primary"
            icon="Edit"
            @click="handleUpdate(scope.row)"
            >修改</el-button
          >
          <el-button
            link
            type="primary"
            icon="Delete"
            @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
    <!-- 弹窗, 新增 / 修改 -->
    <addEditGoodsDialog
      ref="addEditGoodsDialogRef"
      @refreshDataList="getList"
    ></addEditGoodsDialog>
  </div>
</template>

<script setup name="pointsExchangeList">
  import { listExchange, delExchange } from "@/api/opertaionManage/goods.js"
  import addEditGoodsDialog from "./addEditGoodsDialog.vue"
  const { proxy } = getCurrentInstance()

  const { goods_type, goods_recommend_type } = proxy.useDict(
    "goods_type",
    "goods_recommend_type"
  )

  const tableData = ref([])
  const loading = ref(false)
  const showSearch = ref(true)
  const total = ref(0)

  const data = reactive({
    queryParams: {
      pageNum: 1,
      pageSize: 10
    }
  })

  const { queryParams } = toRefs(data)

  /** 查询目录列表 */
  function getList() {
    loading.value = true
    listExchange(queryParams.value).then(response => {
      tableData.value = response.rows || []
      total.value = response.total
      loading.value = false
    })
  }

  const addEditGoodsDialogRef = ref(null)
  /** 修改按钮操作 */
  function handleUpdate(row) {
    addEditGoodsDialogRef.value.openDialog(row?.goodsId)
  }
  /** 删除按钮操作 */
  function handleDelete(row) {
    proxy.$modal
      .confirm('是否确认删除类型为"' + "" + '"的数据项?')
      .then(function () {
        return delExchange(row.goodsId)
      })
      .then(() => {
        getList()
        proxy.$modal.msgSuccess("删除成功")
      })
      .catch(() => {})
  }

  function resetQuery() {
    proxy.resetForm("queryRef")
    getList()
  }

  getList()
</script>
<style lang="scss" scoped></style>
